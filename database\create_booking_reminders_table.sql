-- Booking Reminders Table
-- This table tracks all booking reminders to ensure none are missed

CREATE TABLE IF NOT EXISTS booking_reminders (
    id VARCHAR(36) PRIMARY KEY,
    booking_id VARCHAR(36) NOT NULL,
    reminder_type ENUM('24_HOURS', '1_DAY', '30_MINUTES', 'AT_TIME') NOT NULL,
    priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') NOT NULL DEFAULT 'MEDIUM',
    status ENUM('PENDING', 'SENT', 'FAILED', 'SKIPPED') NOT NULL DEFAULT 'PENDING',
    scheduled_time DATETIME NOT NULL,
    sent_time DATETIME NULL,
    attempts INT DEFAULT 0,
    max_attempts INT DEFAULT 3,
    customer_email_sent BOOLEAN DEFAULT FALSE,
    staff_email_sent BOOLEAN DEFAULT FALSE,
    customer_email_status ENUM('PENDING', 'SENT', 'FAILED') DEFAULT 'PENDING',
    staff_email_status ENUM('PENDING', 'SENT', 'FAILED') DEFAULT 'PENDING',
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    
    -- Indexes for performance
    INDEX idx_booking_id (booking_id),
    INDEX idx_reminder_type (reminder_type),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_pending_reminders (status, scheduled_time),
    INDEX idx_booking_reminder_type (booking_id, reminder_type),
    
    -- Unique constraint to prevent duplicate reminders
    UNIQUE KEY unique_booking_reminder (booking_id, reminder_type)
);

-- Reminder Logs Table for detailed tracking
CREATE TABLE IF NOT EXISTS reminder_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reminder_id VARCHAR(36) NOT NULL,
    booking_id VARCHAR(36) NOT NULL,
    action ENUM('CREATED', 'SENT', 'FAILED', 'RETRY', 'SKIPPED') NOT NULL,
    recipient_type ENUM('CUSTOMER', 'STAFF', 'BOTH') NOT NULL,
    recipient_email VARCHAR(255),
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (reminder_id) REFERENCES booking_reminders(id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_reminder_id (reminder_id),
    INDEX idx_booking_id (booking_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- Insert sample data explanation
/*
Reminder Types and Priorities:
- 24_HOURS (1 DAY): MEDIUM priority - sent 24 hours before appointment
- 1_DAY: MEDIUM priority - alternative naming for 24_HOURS
- 30_MINUTES: HIGH priority - sent 30 minutes before appointment (critical)
- AT_TIME: URGENT priority - sent at appointment time

Priority Levels:
- LOW: Non-critical reminders
- MEDIUM: Standard reminders (24 hours before)
- HIGH: Important reminders (30 minutes before) - should be sent at least twice
- URGENT: Critical reminders (at appointment time)

Status Values:
- PENDING: Reminder scheduled but not sent
- SENT: Reminder successfully sent
- FAILED: Reminder failed to send (will retry)
- SKIPPED: Reminder skipped (e.g., booking cancelled)

The system will:
1. Create reminders when bookings are confirmed
2. Process reminders based on priority and scheduled time
3. Retry failed reminders up to max_attempts
4. Track email delivery to both customer and staff
5. Log all reminder activities for audit trail
*/
