<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../../config/app.php';

// Check authentication and customer role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Check if Stripe is enabled
if (!STRIPE_ENABLED) {
    http_response_code(403);
    echo json_encode(['error' => 'Stripe payments are disabled']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Check if Stripe library exists
    $stripePath = __DIR__ . '/../../../vendor/autoload.php';
    if (!file_exists($stripePath)) {
        // Fallback: Use cURL for Stripe API calls
        error_log("Stripe library not found, using cURL fallback");
        $useStripeLibrary = false;
    } else {
        require_once $stripePath;
        \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
        $useStripeLibrary = true;
    }

    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $paymentId = $input['payment_id'] ?? '';
    $amount = intval($input['amount'] ?? 0);
    $currency = strtolower($input['currency'] ?? 'tzs');
    
    error_log("Stripe create-intent called with: paymentId={$paymentId}, amount={$amount}, currency={$currency}");

    if (empty($paymentId)) {
        throw new Exception('Payment ID is required');
    }

    if ($amount <= 0) {
        throw new Exception('Invalid amount: ' . $amount);
    }
    
    global $database;
    
    // Verify payment belongs to user and is valid
    $payment = $database->fetch("
        SELECT p.*, b.user_id, b.id as booking_id
        FROM payments p
        INNER JOIN bookings b ON p.booking_id = b.id
        WHERE p.id = ? AND b.user_id = ? AND p.payment_gateway = 'STRIPE' AND p.status = 'PENDING'
    ", [$paymentId, $_SESSION['user_id']]);
    
    if (!$payment) {
        error_log("Payment not found: paymentId={$paymentId}, userId={$_SESSION['user_id']}");
        throw new Exception('Payment not found or invalid');
    }

    error_log("Found payment: " . json_encode($payment));
    
    if ($payment['amount'] != $amount) {
        throw new Exception('Amount mismatch');
    }
    
    // Create or retrieve existing payment intent
    $paymentIntentId = null;
    $paymentData = json_decode($payment['payment_data'] ?? '{}', true);

    if ($useStripeLibrary) {
        // Use Stripe library
        if (isset($paymentData['stripe_payment_intent_id'])) {
            try {
                // Try to retrieve existing payment intent
                $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentData['stripe_payment_intent_id']);
                if ($paymentIntent->status === 'requires_payment_method' || $paymentIntent->status === 'requires_confirmation') {
                    $paymentIntentId = $paymentIntent->id;
                }
            } catch (Exception $e) {
                // Payment intent doesn't exist or is invalid, create new one
                $paymentIntentId = null;
            }
        }

        if (!$paymentIntentId) {
            // Create new payment intent
            $paymentIntent = \Stripe\PaymentIntent::create([
                'amount' => $amount * 100, // Stripe expects amount in cents
                'currency' => $currency,
                'metadata' => [
                    'payment_id' => $paymentId,
                    'booking_id' => $payment['booking_id'],
                    'user_id' => $_SESSION['user_id'],
                    'reference' => $payment['payment_reference']
                ],
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
            ]);

            $paymentIntentId = $paymentIntent->id;
            $clientSecret = $paymentIntent->client_secret;
        } else {
            $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId);
            $clientSecret = $paymentIntent->client_secret;
        }

    } else {
        // Use cURL fallback
        if (!isset($paymentData['stripe_payment_intent_id'])) {
            // Create new payment intent using cURL
            $curl = curl_init();

            $postData = [
                'amount' => $amount * 100,
                'currency' => $currency,
                'automatic_payment_methods[enabled]' => 'true',
                'metadata[payment_id]' => $paymentId,
                'metadata[booking_id]' => $payment['booking_id'],
                'metadata[user_id]' => $_SESSION['user_id'],
                'metadata[reference]' => $payment['payment_reference']
            ];

            curl_setopt_array($curl, [
                CURLOPT_URL => 'https://api.stripe.com/v1/payment_intents',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => http_build_query($postData),
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . STRIPE_SECRET_KEY,
                    'Content-Type: application/x-www-form-urlencoded'
                ],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $error = curl_error($curl);
            curl_close($curl);

            if ($error) {
                throw new Exception('cURL error: ' . $error);
            }

            if ($httpCode !== 200) {
                throw new Exception('Stripe API error: HTTP ' . $httpCode);
            }

            $paymentIntent = json_decode($response, true);

            if (!$paymentIntent || !isset($paymentIntent['client_secret'])) {
                throw new Exception('Invalid response from Stripe API');
            }

            $paymentIntentId = $paymentIntent['id'];
            $clientSecret = $paymentIntent['client_secret'];

        } else {
            // Use existing payment intent
            $paymentIntentId = $paymentData['stripe_payment_intent_id'];
            $clientSecret = $paymentData['stripe_client_secret'] ?? '';

            if (empty($clientSecret)) {
                throw new Exception('Client secret not found for existing payment intent');
            }
        }
    }

    // Update payment record with Stripe payment intent ID
    $updatedPaymentData = array_merge($paymentData, [
        'stripe_payment_intent_id' => $paymentIntentId,
        'stripe_client_secret' => $clientSecret
    ]);

    $database->execute("
        UPDATE payments
        SET payment_data = ?, stripe_payment_id = ?, updated_at = NOW()
        WHERE id = ?
    ", [json_encode($updatedPaymentData), $paymentIntentId, $paymentId]);

    // Log payment intent creation
    if (function_exists('logPaymentEvent')) {
        logPaymentEvent($paymentId, 'PROCESSING', 'STRIPE', [
            'payment_intent_id' => $paymentIntentId,
            'amount' => $amount,
            'currency' => $currency,
            'method' => $useStripeLibrary ? 'stripe_library' : 'curl_fallback'
        ]);
    }

    echo json_encode([
        'client_secret' => $clientSecret,
        'payment_intent_id' => $paymentIntentId
    ]);
    
} catch (\Stripe\Exception\ApiErrorException $e) {
    http_response_code(400);
    echo json_encode(['error' => 'Stripe error: ' . $e->getMessage()]);
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}
