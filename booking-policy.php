<?php
/**
 * Booking Policy Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "Booking Policy";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Enhanced Policy Page Styles */
.policy-card {
    background: linear-gradient(135deg, rgba(10, 10, 10, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.policy-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    transition: left 0.6s ease;
}

.policy-card:hover::before {
    left: 100%;
}

.policy-card:hover {
    transform: translateY(-5px);
    border-color: rgba(212, 175, 55, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1);
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate {
    opacity: 1;
    transform: translateY(0);
}

.policy-section {
    transition: all 0.3s ease;
}

.policy-section:hover {
    transform: translateX(10px);
}

.policy-icon {
    transition: all 0.3s ease;
}

.policy-icon:hover {
    transform: scale(1.1) rotate(5deg);
}

.highlight-box {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
    border: 1px solid rgba(212, 175, 55, 0.2);
    position: relative;
}

.highlight-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #D4AF37, #F59E0B);
}
</style>

<!-- Enhanced Hero Section -->
<section class="relative py-32 bg-gradient-to-br from-salon-black via-salon-black to-salon-black overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute top-20 left-10 w-40 h-40 bg-salon-gold/10 rounded-full blur-3xl animate-pulse"></div>
    <div class="absolute bottom-20 right-10 w-60 h-60 bg-salon-gold/5 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
    <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/5 via-transparent to-salon-gold/3"></div>

    <!-- Parallax Background -->
    <div class="absolute inset-0 opacity-10">
        <div class="h-full bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-6 text-center">
        <!-- Luxury Badge -->
        <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-salon-gold/20 animate-on-scroll">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            Booking Guidelines
        </div>

        <h1 class="text-6xl md:text-8xl font-bold font-serif text-white mb-8 leading-tight animate-on-scroll" style="--delay: 0.2s;">
            Booking
            <span class="block md:inline text-transparent bg-clip-text bg-gradient-to-r from-salon-gold via-yellow-400 to-salon-gold">
                Policy
            </span>
        </h1>

        <p class="text-xl md:text-3xl text-gray-300 max-w-5xl mx-auto leading-relaxed mb-12 animate-on-scroll" style="--delay: 0.4s;">
            Please review our booking terms and conditions to ensure a smooth appointment experience.
            <span class="block mt-4 text-salon-gold text-lg md:text-xl">Your satisfaction is our priority</span>
        </p>

        <!-- Quick Policy Highlights -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto animate-on-scroll" style="--delay: 0.6s;">
            <div class="policy-card rounded-xl p-6 text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-white font-semibold mb-2">24-Hour Notice</h3>
                <p class="text-gray-400 text-sm">Required for cancellations</p>
            </div>

            <div class="policy-card rounded-xl p-6 text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-white font-semibold mb-2">15-Minute Grace</h3>
                <p class="text-gray-400 text-sm">Late arrival policy</p>
            </div>

            <div class="policy-card rounded-xl p-6 text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-white font-semibold mb-2">Secure Payment</h3>
                <p class="text-gray-400 text-sm">Multiple payment options</p>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Policy Content -->
<section class="py-32 bg-salon-black relative">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #D4AF37 2px, transparent 2px), radial-gradient(circle at 75% 75%, #D4AF37 2px, transparent 2px); background-size: 50px 50px;"></div>
    </div>

    <div class="max-w-6xl mx-auto px-6 relative">

        <!-- Enhanced Booking Requirements -->
        <div class="policy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Booking Requirements</h2>
                <p class="text-gray-300">Essential information for your appointment</p>
            </div>
            
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-semibold text-white mb-3">Age Requirements</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Clients under 16 must be accompanied by a parent or guardian</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Chemical services require parental consent for clients under 18</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Valid ID required for first-time adult clients</span>
                        </li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-white mb-3">Booking Information Required</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Full name and contact information</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Service(s) requested</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Any allergies or sensitivities</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Previous chemical treatments (if applicable)</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Enhanced Appointment Scheduling -->
        <div class="policy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Appointment Scheduling</h2>
                <p class="text-gray-300">Plan ahead for the best availability</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-10">
                <div class="policy-section bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50">
                    <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                        <svg class="w-6 h-6 text-salon-gold mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Advance Booking
                    </h3>
                    <div class="space-y-3 text-gray-300">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-salon-gold rounded-full mr-3"></div>
                            <span>Regular services: 1-2 weeks recommended</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-salon-gold rounded-full mr-3"></div>
                            <span>Color services: 2-3 weeks recommended</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-salon-gold rounded-full mr-3"></div>
                            <span>Special events: 3-4 weeks recommended</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-salon-gold rounded-full mr-3"></div>
                            <span>Bridal services: 4-6 weeks recommended</span>
                        </div>
                    </div>
                </div>

                <div class="policy-section bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50">
                    <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                        <svg class="w-6 h-6 text-salon-gold mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Same-Day Bookings
                    </h3>
                    <div class="space-y-3 text-gray-300">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-salon-gold rounded-full mr-3"></div>
                            <span>Subject to availability</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-salon-gold rounded-full mr-3"></div>
                            <span>Limited service options</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-salon-gold rounded-full mr-3"></div>
                            <span>Call for availability</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-salon-gold rounded-full mr-3"></div>
                            <span>Walk-ins welcome when possible</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Payment Policy -->
        <div class="policy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Payment Policy</h2>
                <p class="text-gray-300">Secure and convenient payment options</p>
            </div>

            <div class="space-y-8">
                <div class="policy-section bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <svg class="w-6 h-6 text-salon-gold mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                        Accepted Payment Methods
                    </h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                        <div class="text-center p-4 bg-secondary-900/50 rounded-lg">
                            <svg class="w-8 h-8 text-salon-gold mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            <p class="text-gray-300 text-sm font-medium">Credit Cards</p>
                        </div>
                        <div class="text-center p-4 bg-secondary-900/50 rounded-lg">
                            <svg class="w-8 h-8 text-salon-gold mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <p class="text-gray-300 text-sm font-medium">Cash</p>
                        </div>
                        <div class="text-center p-4 bg-secondary-900/50 rounded-lg">
                            <svg class="w-8 h-8 text-salon-gold mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                            <p class="text-gray-300 text-sm font-medium">Apple Pay</p>
                        </div>
                        <div class="text-center p-4 bg-secondary-900/50 rounded-lg">
                            <svg class="w-8 h-8 text-salon-gold mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                            <p class="text-gray-300 text-sm font-medium">Google Pay</p>
                        </div>
                    </div>
                </div>

                <div class="policy-section bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <svg class="w-6 h-6 text-salon-gold mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        Deposit Requirements
                    </h3>
                    <div class="space-y-4">
                        <div class="flex items-start p-4 bg-secondary-900/30 rounded-lg">
                            <svg class="w-5 h-5 text-salon-gold mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-gray-300">Bridal services: 50% deposit required</span>
                        </div>
                        <div class="flex items-start p-4 bg-secondary-900/30 rounded-lg">
                            <svg class="w-5 h-5 text-salon-gold mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-gray-300">Group appointments (3+): 25% deposit required</span>
                        </div>
                        <div class="flex items-start p-4 bg-secondary-600/20 rounded-lg">
                            <svg class="w-5 h-5 text-salon-gold mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-gray-300">Services over TSH 200,000: 25% deposit required</span>
                        </div>
                        <div class="flex items-start p-4 bg-secondary-600/20 rounded-lg">
                            <svg class="w-5 h-5 text-salon-gold mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-gray-300">Deposits are non-refundable but applied to final service cost</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Service Guidelines -->
        <div class="policy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Service Guidelines</h2>
                <p class="text-gray-300">Professional standards for quality results</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-10">
                <div class="policy-section bg-secondary-700/30 rounded-xl p-6 border border-secondary-600/50">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <svg class="w-6 h-6 text-salon-gold mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        Consultation Requirements
                    </h3>
                    <div class="space-y-4">
                        <div class="flex items-start p-4 bg-secondary-600/20 rounded-lg">
                            <svg class="w-5 h-5 text-salon-gold mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-gray-300">Color consultations required for major color changes</span>
                        </div>
                        <div class="flex items-start p-4 bg-secondary-600/20 rounded-lg">
                            <svg class="w-5 h-5 text-salon-gold mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-gray-300">Patch tests required for sensitive clients</span>
                        </div>
                        <div class="flex items-start p-4 bg-secondary-600/20 rounded-lg">
                            <svg class="w-5 h-5 text-salon-gold mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-gray-300">Hair analysis for chemical services</span>
                        </div>
                    </div>
                </div>

                <div class="policy-section bg-secondary-700/30 rounded-xl p-6 border border-secondary-600/50">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <svg class="w-6 h-6 text-yellow-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        Service Limitations
                    </h3>
                    <div class="space-y-4">
                        <div class="flex items-start p-4 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                            <svg class="w-5 h-5 text-yellow-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                            </svg>
                            <span class="text-gray-300">We reserve the right to refuse services that may damage hair</span>
                        </div>
                        <div class="flex items-start p-4 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                            <svg class="w-5 h-5 text-yellow-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                            </svg>
                            <span class="text-gray-300">Multiple chemical processes may require separate appointments</span>
                        </div>
                        <div class="flex items-start p-4 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                            <svg class="w-5 h-5 text-yellow-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                            </svg>
                            <span class="text-gray-300">Pregnancy restrictions apply to certain chemical services</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Arrival & Lateness Policy -->
        <div class="policy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Arrival & Lateness Policy</h2>
                <p class="text-gray-300">Punctuality ensures the best service experience</p>
            </div>

            <div class="space-y-6">
                <div class="policy-section bg-green-500/10 border-l-4 border-salon-gold rounded-xl p-6">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <svg class="w-6 h-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white mb-3">Please Arrive 15 Minutes Early</h3>
                            <p class="text-gray-300 leading-relaxed">
                                This allows time for consultation, form completion, and preparation for your service. Early arrival ensures we can provide you with our full attention and the best possible experience.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="policy-section bg-yellow-500/10 border-l-4 border-yellow-500 rounded-xl p-6">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-yellow-500/20 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <svg class="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white mb-3">Late Arrival (Up to 15 Minutes)</h3>
                            <p class="text-gray-300 leading-relaxed">
                                Service time may be shortened to accommodate the next client. Full service charge applies. We'll do our best to provide quality service within the remaining time.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="policy-section bg-red-500/10 border-l-4 border-red-500 rounded-xl p-6">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <svg class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white mb-3">Late Arrival (Over 15 Minutes)</h3>
                            <p class="text-gray-300 leading-relaxed">
                                Appointment may need to be rescheduled to ensure quality service for all clients. Cancellation fees may apply. Please call ahead if you're running late.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Health & Safety -->
        <div class="bg-secondary-800 rounded-lg p-8 mb-8">
            <h2 class="text-2xl font-bold text-salon-gold mb-6">Health & Safety Requirements</h2>
            
            <div class="space-y-4">
                <div>
                    <h3 class="text-lg font-semibold text-white mb-3">Client Health Disclosure</h3>
                    <p class="text-gray-300 mb-3">
                        Please inform us of any:
                    </p>
                    <ul class="space-y-2 text-gray-300">
                        <li>• Allergies or sensitivities</li>
                        <li>• Medical conditions affecting hair/skin</li>
                        <li>• Medications that may affect services</li>
                        <li>• Recent chemical treatments</li>
                        <li>• Pregnancy or nursing status</li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-white mb-3">Salon Safety Measures</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• All tools are sanitized between clients</li>
                        <li>• Single-use items are disposed of properly</li>
                        <li>• Clean linens for every client</li>
                        <li>• Regular deep cleaning protocols</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Enhanced Contact Section -->
        <div class="highlight-box rounded-3xl p-10 text-center animate-on-scroll">
            <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
            <h2 class="text-3xl font-bold text-white mb-6">Ready to Book Your Appointment?</h2>
            <p class="text-gray-300 mb-8 text-lg leading-relaxed max-w-2xl mx-auto">
                By booking with us, you agree to our booking policy and terms of service. We're excited to provide you with exceptional service.
            </p>
            <div class="flex flex-col sm:flex-row gap-6 justify-center">
                <a href="<?= getBasePath() ?>/customer/book" class="bg-gradient-to-r from-salon-gold to-yellow-500 hover:from-yellow-500 hover:to-salon-gold text-black px-8 py-4 rounded-xl font-bold transition-all hover:scale-105 shadow-lg">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Book Now
                </a>
                <a href="<?= getBasePath() ?>/contact.php" class="bg-secondary-700/50 hover:bg-secondary-600/50 text-white px-8 py-4 rounded-xl font-bold transition-all hover:scale-105 border border-secondary-600/50 backdrop-blur-sm">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    Contact Us
                </a>
            </div>
        </div>
    </div>
</section>

<script>
// Enhanced Policy Page Interactions
document.addEventListener('DOMContentLoaded', function() {
    // Scroll animations
    function handleScrollAnimations() {
        const elements = document.querySelectorAll('.animate-on-scroll');

        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;

            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('animate');
            }
        });
    }

    // Intersection Observer for better performance
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, observerOptions);

    // Observe all animated elements
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });

    // Policy section hover effects
    document.querySelectorAll('.policy-section').forEach(section => {
        section.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(10px)';
        });

        section.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });

    // Policy icon hover effects
    document.querySelectorAll('.policy-icon').forEach(icon => {
        icon.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1) rotate(5deg)';
        });

        icon.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // Initial check for elements already in view
    handleScrollAnimations();

    // Add loading animation to page
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';

    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
