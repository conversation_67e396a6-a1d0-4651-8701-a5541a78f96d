<?php
/**
 * Admin Booking View
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_schedule_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get booking ID from URL
$bookingId = $_GET['id'] ?? '';
if (empty($bookingId)) {
    $_SESSION['error'] = 'Booking ID is required';
    redirect('/admin/bookings');
}

// Handle staff assignment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'assign_staff') {
        $result = assignStaffToBooking($_POST['booking_id'], $_POST['staff_id']);
        if ($result['success']) {
            $_SESSION['success'] = 'Staff member assigned successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }
        redirect('/admin/bookings/view.php?id=' . $bookingId);
    }
}

// Get booking details
$booking = getBookingById($bookingId);
if (!$booking) {
    $_SESSION['error'] = 'Booking not found';
    redirect('/admin/bookings');
}

// Get available staff for this booking
$availableStaff = getAvailableStaff($booking['date'], $booking['start_time'], $booking['end_time']);

// Get all staff members for assignment
$allStaff = getActiveStaffMembers();

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Booking Details";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>
            
            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-white">Booking Details</h1>
                                <p class="mt-1 text-sm text-gray-300">Booking ID: <?= htmlspecialchars($booking['id']) ?></p>
                            </div>
                            <div class="mt-4 sm:mt-0 flex gap-2">
                                <a href="<?= getBasePath() ?>/admin/bookings" 
                                   class="bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                    Back to Bookings
                                </a>
                                <button onclick="updateStatus('<?= $booking['id'] ?>', '<?= $booking['status'] ?>')" 
                                        class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    Update Status
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Booking Information -->
                        <div class="lg:col-span-2">
                            <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                                <h2 class="text-lg font-semibold text-white mb-4">Booking Information</h2>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-300 mb-2">Date & Time</h3>
                                        <p class="text-white"><?= date('l, F j, Y', strtotime($booking['date'])) ?></p>
                                        <p class="text-gray-300"><?= date('g:i A', strtotime($booking['start_time'])) ?> - <?= date('g:i A', strtotime($booking['end_time'])) ?></p>
                                    </div>
                                    
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-300 mb-2">Status</h3>
                                        <?php
                                        require_once __DIR__ . '/../../includes/booking_expiration.php';
                                        $statusInfo = getBookingStatusInfo($booking['status']);
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusInfo['class'] ?>">
                                            <i class="<?= $statusInfo['icon'] ?> mr-1"></i>
                                            <?= $statusInfo['label'] ?>
                                        </span>
                                    </div>
                                    
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-300 mb-2">Service/Package</h3>
                                        <div class="text-white">
                                            <?php if (!empty($booking['service_name'])): ?>
                                                <?= htmlspecialchars($booking['service_name']) ?>
                                            <?php elseif (!empty($booking['package_name'])): ?>
                                                <?= htmlspecialchars($booking['package_name']) ?>
                                                <span class="text-xs bg-salon-gold text-black px-2 py-1 rounded ml-2">PACKAGE</span>
                                            <?php else: ?>
                                                Unknown Service
                                            <?php endif; ?>
                                        </div>
                                        <p class="text-gray-300"><?= $booking['service_duration'] ?> minutes</p>
                                    </div>
                                    
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-300 mb-2">Staff Member</h3>
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <p class="text-white"><?= htmlspecialchars($booking['staff_name'] ?? 'Unassigned') ?></p>
                                                <?php if ($booking['staff_email']): ?>
                                                    <p class="text-gray-300"><?= htmlspecialchars($booking['staff_email']) ?></p>
                                                <?php endif; ?>
                                            </div>
                                            <button onclick="openStaffAssignmentModal()"
                                                    class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors">
                                                <?= $booking['staff_name'] ? 'Reassign' : 'Assign' ?>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <?php if ($booking['package_name']): ?>
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-300 mb-2">Package</h3>
                                        <p class="text-white"><?= htmlspecialchars($booking['package_name']) ?></p>
                                        <p class="text-gray-300">Package Price: <?= formatCurrency($booking['package_price']) ?></p>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-300 mb-2">Total Amount</h3>
                                        <p class="text-salon-gold text-lg font-semibold"><?= formatCurrency($booking['total_amount']) ?></p>
                                        <?php if ($booking['points_used'] > 0): ?>
                                            <p class="text-gray-300">Points Used: <?= $booking['points_used'] ?></p>
                                        <?php endif; ?>
                                        <?php if ($booking['points_earned'] > 0): ?>
                                            <p class="text-gray-300">Points Earned: <?= $booking['points_earned'] ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <?php if ($booking['notes']): ?>
                                <div class="mt-6">
                                    <h3 class="text-sm font-medium text-gray-300 mb-2">Notes</h3>
                                    <p class="text-white bg-secondary-700 p-3 rounded-lg"><?= nl2br(htmlspecialchars($booking['notes'])) ?></p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Customer Information -->
                        <div>
                            <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                                <h2 class="text-lg font-semibold text-white mb-4">Customer Information</h2>
                                
                                <div class="flex items-center mb-4">
                                    <div class="flex-shrink-0 h-12 w-12">
                                        <div class="h-12 w-12 rounded-full bg-salon-gold flex items-center justify-center">
                                            <span class="text-lg font-medium text-black">
                                                <?= strtoupper(substr($booking['customer_name'], 0, 2)) ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h3 class="text-lg font-medium text-white"><?= htmlspecialchars($booking['customer_name']) ?></h3>
                                        <p class="text-gray-300"><?= htmlspecialchars($booking['customer_email']) ?></p>
                                    </div>
                                </div>
                                
                                <?php if ($booking['customer_phone']): ?>
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-300 mb-1">Phone</h4>
                                    <p class="text-white"><?= htmlspecialchars($booking['customer_phone']) ?></p>
                                </div>
                                <?php endif; ?>
                                
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-300 mb-1">Booking Created</h4>
                                    <p class="text-white"><?= date('M j, Y g:i A', strtotime($booking['created_at'])) ?></p>
                                </div>
                                
                                <?php if ($booking['updated_at'] !== $booking['created_at']): ?>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-300 mb-1">Last Updated</h4>
                                    <p class="text-white"><?= date('M j, Y g:i A', strtotime($booking['updated_at'])) ?></p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div id="statusModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-white">Update Booking Status</h2>
            <button onclick="closeStatusModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="statusForm" method="POST" action="<?= getBasePath() ?>/admin/bookings">
            <input type="hidden" name="action" value="update_status">
            <input type="hidden" name="booking_id" id="statusBookingId">
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">New Status</label>
                <select name="status" id="newStatus" required 
                        class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    <option value="PENDING">Pending</option>
                    <option value="CONFIRMED">Confirmed</option>
                    <option value="IN_PROGRESS">In Progress</option>
                    <option value="COMPLETED">Completed</option>
                    <option value="CANCELLED">Cancelled</option>
                    <option value="NO_SHOW">No Show</option>
                    <option value="EXPIRED">Expired</option>
                </select>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="flex-1 bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    Update Status
                </button>
                <button type="button" onclick="closeStatusModal()" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Staff Assignment Modal -->
<div id="staffAssignmentModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-white">Assign Staff Member</h2>
            <button onclick="closeStaffAssignmentModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="staffAssignmentForm" method="POST">
            <input type="hidden" name="action" value="assign_staff">
            <input type="hidden" name="booking_id" value="<?= $booking['id'] ?>">

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Available Staff</label>
                <select name="staff_id" id="staffSelect" required
                        class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    <option value="">Select a staff member...</option>
                    <?php foreach ($allStaff as $staff): ?>
                        <option value="<?= $staff['id'] ?>" <?= $booking['staff_id'] === $staff['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($staff['name']) ?>
                            <?php
                            $isAvailable = false;
                            foreach ($availableStaff as $available) {
                                if ($available['id'] === $staff['id']) {
                                    $isAvailable = true;
                                    break;
                                }
                            }
                            echo $isAvailable ? ' (Available)' : ' (Busy)';
                            ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <?php if (!empty($availableStaff)): ?>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-green-400 mb-2">✓ Available Staff for this time slot:</h4>
                <div class="space-y-2">
                    <?php foreach ($availableStaff as $staff): ?>
                        <div class="bg-secondary-700 p-3 rounded-lg">
                            <p class="text-white font-medium"><?= htmlspecialchars($staff['name']) ?></p>
                            <p class="text-gray-300 text-sm"><?= htmlspecialchars($staff['role']) ?></p>
                            <p class="text-gray-400 text-sm">Rate: <?= formatCurrency($staff['hourly_rate']) ?>/hour</p>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php else: ?>
            <div class="mb-4">
                <p class="text-yellow-400 text-sm">⚠️ No staff members are available for this exact time slot. You can still assign staff, but there may be scheduling conflicts.</p>
            </div>
            <?php endif; ?>

            <div class="flex gap-4">
                <button type="submit" class="flex-1 bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    Assign Staff
                </button>
                <button type="button" onclick="closeStaffAssignmentModal()" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function updateStatus(bookingId, currentStatus) {
    document.getElementById('statusBookingId').value = bookingId;
    document.getElementById('newStatus').value = currentStatus;
    document.getElementById('statusModal').classList.remove('hidden');
}

function closeStatusModal() {
    document.getElementById('statusModal').classList.add('hidden');
}

function openStaffAssignmentModal() {
    document.getElementById('staffAssignmentModal').classList.remove('hidden');
}

function closeStaffAssignmentModal() {
    document.getElementById('staffAssignmentModal').classList.add('hidden');
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeStatusModal();
        closeStaffAssignmentModal();
    }
});

// Close modal on backdrop click
document.getElementById('statusModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeStatusModal();
    }
});

document.getElementById('staffAssignmentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeStaffAssignmentModal();
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
