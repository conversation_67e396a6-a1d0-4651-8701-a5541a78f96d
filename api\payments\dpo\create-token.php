<?php
/**
 * DPO Pay Create Token API
 * Creates payment token for DPO Pay processing
 */

header('Content-Type: application/json');

require_once __DIR__ . '/../../../config/app.php';

// Check if DPO is enabled
if (!DPO_ENABLED) {
    http_response_code(403);
    echo json_encode(['error' => 'DPO Pay is disabled']);
    exit;
}

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $paymentId = $input['payment_id'] ?? '';
    $amount = intval($input['amount'] ?? 0);
    $currency = strtoupper($input['currency'] ?? 'TZS');
    
    error_log("DPO create-token called with: paymentId={$paymentId}, amount={$amount}, currency={$currency}");

    if (empty($paymentId)) {
        throw new Exception('Payment ID is required');
    }

    if ($amount <= 0) {
        throw new Exception('Invalid amount: ' . $amount);
    }
    
    global $database;
    
    // Verify payment belongs to user and is valid
    $payment = $database->fetch("
        SELECT p.*, b.user_id, b.id as booking_id, u.name, u.email, u.phone
        FROM payments p
        INNER JOIN bookings b ON p.booking_id = b.id
        INNER JOIN users u ON b.user_id = u.id
        WHERE p.id = ? AND b.user_id = ? AND p.payment_gateway = 'DPO' AND p.status = 'PENDING'
    ", [$paymentId, $_SESSION['user_id']]);

    if (!$payment) {
        throw new Exception('Payment not found or invalid');
    }

    // Verify amount matches
    if ($payment['amount'] != $amount) {
        throw new Exception('Amount mismatch');
    }

    // Check if token already exists
    $paymentData = json_decode($payment['payment_data'] ?? '{}', true);
    $existingToken = $paymentData['dpo_token'] ?? null;

    if ($existingToken) {
        // Verify existing token
        $verificationResult = verifyDpoToken($existingToken);
        
        if ($verificationResult['success'] && $verificationResult['result'] === '900') {
            // Token is still valid, return existing payment URL
            echo json_encode([
                'success' => true,
                'dpo_token' => $existingToken,
                'payment_url' => DPO_API_URL . '/payv2.php?ID=' . $existingToken,
                'existing_token' => true
            ]);
            exit;
        }
    }

    // Prepare payment data for DPO
    $dpoPaymentData = [
        'amount' => $amount,
        'currency' => $currency,
        'reference' => $payment['payment_reference'],
        'customer_name' => $payment['name'],
        'customer_email' => $payment['email'],
        'customer_phone' => $payment['phone'] ?? '',
        'customer_address' => '',
        'customer_city' => '',
        'redirect_url' => getBaseUrl() . '/customer/payments/dpo-return.php?payment_id=' . $paymentId,
        'back_url' => getBaseUrl() . '/customer/payments'
    ];

    // Create DPO token
    $tokenResult = createDpoToken($dpoPaymentData);

    if ($tokenResult['success']) {
        // Update payment with DPO token
        $updatedPaymentData = array_merge($paymentData, [
            'dpo_token' => $tokenResult['transToken'],
            'dpo_ref' => $tokenResult['transRef'],
            'payment_url' => $tokenResult['paymentUrl'],
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $database->execute("
            UPDATE payments
            SET payment_data = ?, dpo_token = ?, updated_at = NOW()
            WHERE id = ?
        ", [json_encode($updatedPaymentData), $tokenResult['transToken'], $paymentId]);

        // Log payment event
        if (function_exists('logPaymentEvent')) {
            logPaymentEvent($paymentId, 'PROCESSING', 'DPO', [
                'dpo_token' => $tokenResult['transToken'],
                'dpo_ref' => $tokenResult['transRef'],
                'amount' => $amount,
                'currency' => $currency
            ]);
        }

        echo json_encode([
            'success' => true,
            'dpo_token' => $tokenResult['transToken'],
            'payment_url' => $tokenResult['paymentUrl']
        ]);

    } else {
        throw new Exception('Failed to create DPO token: ' . $tokenResult['resultExplanation']);
    }

} catch (Exception $e) {
    error_log("DPO Create Token Error: " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'error' => $e->getMessage(),
        'details' => [
            'payment_id' => $paymentId ?? null,
            'amount' => $amount ?? null,
            'currency' => $currency ?? null
        ]
    ]);
}
