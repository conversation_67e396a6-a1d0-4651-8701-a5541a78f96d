# Payment System Implementation Guide

## Overview

This comprehensive payment system integrates both **Stripe** and **Flutterwave** payment gateways into the Flix Salon & SPA booking system. Customers can optionally pay for their confirmed bookings through a secure, user-friendly interface.

## Features

### ✅ Dual Gateway Support
- **Stripe**: Credit/Debit cards, Apple Pay, Google Pay
- **Flutterwave**: Cards, Bank Transfer, Mobile Money, Digital Wallets

### ✅ Security & Verification
- Robust payment verification system
- Webhook signature validation
- Payment amount verification
- Booking status validation
- Comprehensive audit logging

### ✅ User Experience
- Optional payment (not mandatory)
- Clean, responsive interface
- Real-time payment status updates
- Multiple payment method options
- Secure payment processing

### ✅ Integration
- Seamless booking system integration
- Automatic points awarding on payment completion
- Booking status updates
- Email notifications (ready for integration)

## Installation & Setup

### 1. Database Setup

Run the database update script to add payment tables:

```sql
-- Execute this in your MySQL database
SOURCE database/update_payments_table.sql;
```

This creates:
- Enhanced `payments` table with gateway support
- `payment_logs` table for audit trail
- `payment_webhooks` table for webhook management
- Proper indexes for performance

### 2. API Keys Configuration

Update your `config/app.php` with actual API keys:

```php
// Stripe Configuration
define('STRIPE_PUBLIC_KEY', 'pk_live_...');  // Your actual Stripe public key
define('STRIPE_SECRET_KEY', 'sk_live_...');  // Your actual Stripe secret key

// Flutterwave Configuration
define('FLUTTERWAVE_PUBLIC_KEY', 'FLWPUBK_LIVE-...');  // Your actual Flutterwave public key
define('FLUTTERWAVE_SECRET_KEY', 'FLWSECK_LIVE-...');  // Your actual Flutterwave secret key
define('FLUTTERWAVE_ENCRYPTION_KEY', 'FLWSECK_LIVE...');  // Your actual encryption key
```

### 3. Stripe PHP SDK Installation

Install Stripe PHP SDK via Composer:

```bash
cd /path/to/your/project
composer require stripe/stripe-php
```

Or download manually and place in `vendor/stripe/stripe-php/`

## File Structure

```
├── config/app.php                          # Updated with payment config
├── includes/payment_functions.php          # Core payment functions
├── customer/payments/
│   ├── index.php                          # Payment center dashboard
│   ├── stripe.php                         # Stripe payment page
│   ├── stripe-return.php                  # Stripe return handler
│   └── flutterwave.php                    # Flutterwave payment page
├── api/payments/
│   ├── create.php                         # Create payment record
│   ├── verify.php                         # Verify payment status
│   ├── update-status.php                  # Update payment status
│   ├── stripe/
│   │   └── create-intent.php              # Create Stripe payment intent
│   └── flutterwave/
│       └── verify.php                     # Verify Flutterwave payment
└── database/update_payments_table.sql     # Database migration script
```

## Usage Flow

### Customer Journey

1. **View Bookings**: Customer sees confirmed bookings in `/customer/bookings`
2. **Payment Option**: "Pay Now" button appears for eligible bookings
3. **Payment Center**: Customer navigates to `/customer/payments`
4. **Gateway Selection**: Choose between Stripe or Flutterwave
5. **Payment Processing**: Complete payment through selected gateway
6. **Verification**: System verifies payment and updates booking status
7. **Completion**: Points awarded, booking marked as paid

### Eligible Bookings

Payments are available for bookings with:
- Status: `CONFIRMED` or `COMPLETED`
- No existing payment OR payment status is `FAILED`
- Valid booking amount

## API Endpoints

### Create Payment
```
POST /api/payments/create.php
{
    "booking_id": "uuid",
    "amount": 50000,
    "gateway": "STRIPE" | "FLUTTERWAVE"
}
```

### Verify Payment
```
POST /api/payments/verify.php
{
    "payment_id": "uuid"
}
```

### Stripe Payment Intent
```
POST /api/payments/stripe/create-intent.php
{
    "payment_id": "uuid",
    "amount": 50000,
    "currency": "tzs"
}
```

### Flutterwave Verification
```
POST /api/payments/flutterwave/verify.php
{
    "payment_id": "uuid",
    "transaction_id": "flw_tx_id",
    "tx_ref": "reference"
}
```

## Security Features

### Payment Verification
- Amount matching between booking and payment
- Currency validation
- Gateway-specific verification
- Transaction reference validation

### Audit Logging
- All payment events logged in `payment_logs`
- IP address and user agent tracking
- Error logging for debugging
- Webhook payload storage

### Access Control
- Customer-only access to payment functions
- Payment ownership verification
- Session-based authentication
- CSRF protection ready

## Testing

### Test Mode Setup
The system is configured for test mode by default:
- Stripe test keys (pk_test_... / sk_test_...)
- Flutterwave test keys (FLWPUBK_TEST... / FLWSECK_TEST...)

### Test Payment Flow
1. Create a customer account
2. Book a service/package
3. Admin confirms the booking
4. Customer can now pay for the booking
5. Test with test card numbers from Stripe/Flutterwave docs

### Test Cards
**Stripe Test Cards:**
- Success: 4242 4242 4242 4242
- Decline: 4000 0000 0000 0002

**Flutterwave Test Cards:**
- Success: 5531 8866 5214 2950
- PIN: 3310, OTP: 12345

## Production Deployment

### 1. Update API Keys
Replace test keys with live keys in `config/app.php`

### 2. SSL Certificate
Ensure your site has a valid SSL certificate for secure payments

### 3. Webhook Setup
Configure webhooks for real-time payment updates:

**Stripe Webhooks:**
- Endpoint: `https://yourdomain.com/api/payments/stripe/webhook.php`
- Events: `payment_intent.succeeded`, `payment_intent.payment_failed`

**Flutterwave Webhooks:**
- Endpoint: `https://yourdomain.com/api/payments/flutterwave/webhook.php`
- Events: Transaction status updates

### 4. Error Monitoring
Monitor payment logs and set up alerts for failed payments

## Customization

### Currency Support
The system uses TSH (Tanzanian Shilling) by default. To change:
1. Update `CURRENCY_CODE` and `CURRENCY_SYMBOL` in `config/app.php`
2. Ensure your payment gateways support the currency

### Payment Methods
- Stripe: Automatically detects available methods
- Flutterwave: Configured for cards, mobile money, bank transfer

### UI Customization
Payment pages use the existing design system:
- Tailwind CSS classes
- Consistent color scheme
- Responsive design
- Dark theme

## Troubleshooting

### Common Issues

1. **Payment buttons not showing**: Check `PAYMENT_ENABLED` constant
2. **Stripe errors**: Verify API keys and SDK installation
3. **Flutterwave errors**: Check API keys and network connectivity
4. **Database errors**: Run migration script and check table structure

### Debug Mode
Enable detailed logging by setting error reporting in `config/app.php`

## Support

For technical support:
1. Check payment logs in database
2. Review error messages in browser console
3. Verify API key configuration
4. Test with gateway documentation examples

## Webhook Configuration

### Stripe Webhooks
- **Endpoint**: `https://yourdomain.com/api/payments/stripe/webhook.php`
- **Events**: `payment_intent.succeeded`, `payment_intent.payment_failed`, `payment_intent.canceled`
- **Security**: Webhook signature verification (configure in production)

### Flutterwave Webhooks
- **Endpoint**: `https://yourdomain.com/api/payments/flutterwave/webhook.php`
- **Events**: `charge.completed`, `charge.failed`
- **Security**: Hash verification using secret key

## Implementation Summary

### ✅ Completed Features
- ✅ Dual payment gateway integration (Stripe + Flutterwave)
- ✅ Secure payment processing with verification
- ✅ Customer payment dashboard
- ✅ Payment status tracking and updates
- ✅ Booking integration with payment options
- ✅ Points system integration
- ✅ Comprehensive audit logging
- ✅ Webhook handlers for real-time updates
- ✅ Responsive UI with consistent design
- ✅ Error handling and validation
- ✅ Database schema with proper indexing
- ✅ API endpoints for all payment operations
- ✅ Test mode configuration
- ✅ Security features and access control

### 🎯 Ready for Production
The payment system is fully functional and ready for production use with:
- Complete payment flow from booking to completion
- Real-time payment verification
- Automatic booking status updates
- Points awarding on successful payments
- Comprehensive error handling
- Security best practices

## Future Enhancements

Potential future features:
- Refund processing interface
- Subscription payments for packages
- Payment analytics dashboard
- Multi-currency support
- Payment reminders via email/SMS
- Partial payment options
- Payment installments
