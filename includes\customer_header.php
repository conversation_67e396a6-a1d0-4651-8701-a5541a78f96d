<?php
// Include customer panel functions
require_once __DIR__ . '/customer_panel_functions.php';

// Get customer data for navigation
if (!isset($dashboardData)) {
    $customerId = $_SESSION['user_id'];
    $customerProfile = getCustomerProfile($customerId);
    $customerPoints = getCustomerPointsData($customerId);
} else {
    $customerProfile = $dashboardData['profile'];
    $customerPoints = $dashboardData['pointsData'];
}

// Set current page for navigation highlighting
$currentPath = $_SERVER['REQUEST_URI'];
?>

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME . ' - Customer Portal' ?></title>
    <meta name="description" content="<?= isset($pageDescription) ? $pageDescription : 'Customer portal for Flix Salon & SPA salon booking system' ?>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#1a1a1a',
                            800: '#141414',
                            900: '#0a0a0a',
                        },
                        'salon-black': '#000000',
                        'salon-gold': '#f59e0b',
                        'salon-white': '#ffffff',
                        'gold-light': '#fbbf24',
                        'gold-dark': '#d97706',
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
                        serif: ['Playfair Display', 'ui-serif', 'Georgia'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.95)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                    },
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <style>
        :root {
            --background: #000000;
            --foreground: #ffffff;
            --gold: #f59e0b;
            --gold-light: #fbbf24;
            --gold-dark: #d97706;
            --black-secondary: #0a0a0a;
            --black-tertiary: #141414;
            --black-quaternary: #1a1a1a;
        }

        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        html, body {
            max-width: 100vw;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            background: var(--background);
            color: var(--foreground);
            font-family: 'Inter', ui-sans-serif, system-ui;
            line-height: 1.6;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--black-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gold);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--gold-dark);
        }

        /* Selection */
        ::selection {
            background: var(--gold);
            color: var(--background);
        }

        /* Glass effect for headers and cards */
        .glass-effect {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(245, 158, 11, 0.1);
        }

        /* Hover effects */
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(245, 158, 11, 0.2);
        }
    </style>

    <!-- AJAX Reminder Processor -->
    <script src="<?= getBasePath() ?>/assets/js/reminder-processor.js"></script>
</head>
<body class="antialiased min-h-screen bg-salon-black customer-page">
    <!-- Customer Header -->
    <header class="bg-salon-black/98 backdrop-blur-md border-b border-secondary-700 fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo and Title -->
                <div class="flex items-center">
                    <button id="mobile-menu-toggle" class="lg:hidden p-2 rounded-md text-gray-400 hover:text-salon-gold hover:bg-salon-gold/10 focus:outline-none focus:ring-2 focus:ring-salon-gold transition-all duration-300">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                    <div class="ml-4 lg:ml-0">
                        <h1 class="text-xl font-bold font-serif text-salon-gold">Flix Salon & SPA</h1>
                        <p class="text-sm text-gray-300">Customer Portal</p>
                    </div>
                </div>

                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    <!-- Points Display -->
                    <div class="hidden md:flex items-center space-x-2 px-3 py-2 bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-lg hover-lift">
                        <svg class="h-5 w-5 text-salon-gold" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-salon-gold font-semibold"><?= number_format($customerPoints['currentPoints']) ?> pts</span>
                    </div>

                    <!-- User Profile Dropdown -->
                    <div class="relative">
                        <button id="user-menu-toggle" class="flex items-center space-x-3 p-2 rounded-md text-gray-300 hover:text-white hover:bg-salon-gold/10 focus:outline-none focus:ring-2 focus:ring-salon-gold transition-all duration-300">
                            <div class="h-8 w-8 rounded-full bg-salon-gold flex items-center justify-center">
                                <span class="text-sm font-medium text-black">
                                    <?= strtoupper(substr($customerProfile['name'], 0, 2)) ?>
                                </span>
                            </div>
                            <div class="hidden md:block text-left">
                                <p class="text-sm font-medium"><?= htmlspecialchars($customerProfile['name']) ?></p>
                                <p class="text-xs text-gray-400">Customer</p>
                            </div>
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-secondary-900/90 backdrop-blur-md border border-secondary-700 rounded-lg shadow-xl py-2 z-50">
                            <?php $basePath = getBasePath(); ?>
                            <a href="<?= $basePath ?>/customer/profile" class="block px-4 py-2 text-sm text-gray-300 hover:bg-secondary-700 hover:text-salon-gold transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                Profile Settings
                            </a>
                            <a href="<?= $basePath ?>/customer/rewards" class="block px-4 py-2 text-sm text-gray-300 hover:bg-secondary-700 hover:text-salon-gold transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                                </svg>
                                Points & Rewards
                            </a>
                            <div class="border-t border-secondary-700 my-1"></div>
                            <a href="<?= $basePath ?>/" class="block px-4 py-2 text-sm text-gray-300 hover:bg-secondary-700 hover:text-salon-gold transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                                View Website
                            </a>
                            <a href="<?= $basePath ?>/auth/logout.php" class="block px-4 py-2 text-sm text-red-400 hover:bg-secondary-700 hover:text-red-300 transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Sidebar Overlay -->
    <div id="mobile-sidebar-overlay" class="hidden fixed inset-0 z-40 lg:hidden">
        <div class="fixed inset-0 bg-black opacity-50"></div>
    </div>

    <!-- Mobile Sidebar -->
    <div id="mobile-sidebar" class="hidden fixed inset-y-0 left-0 z-50 w-64 bg-secondary-900/95 backdrop-blur-md border-r border-secondary-700 lg:hidden">
        <div class="flex items-center justify-between p-4 border-b border-secondary-700">
            <h2 class="text-lg font-semibold text-salon-gold">Navigation</h2>
            <button id="mobile-sidebar-close" class="p-2 rounded-md text-gray-400 hover:text-salon-gold hover:bg-salon-gold/10 transition-all duration-300">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <nav class="mt-4">
            <?php include __DIR__ . '/customer_sidebar_nav.php'; ?>
        </nav>
    </div>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/customer_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
