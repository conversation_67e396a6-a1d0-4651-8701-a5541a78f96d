<?php
/**
 * Debug version of staff schedule page
 */

echo "<h1>Debug Staff Schedule</h1>";

try {
    echo "<h2>1. Loading config...</h2>";
    require_once 'config/app.php';
    echo "✓ Config loaded<br>";
    
    echo "<h2>2. Checking authentication...</h2>";
    if (!isLoggedIn()) {
        echo "✗ Not logged in<br>";
        echo "Session data: " . print_r($_SESSION, true) . "<br>";
    } else {
        echo "✓ User logged in: " . $_SESSION['user_id'] . "<br>";
        echo "✓ User role: " . $_SESSION['user_role'] . "<br>";
    }
    
    echo "<h2>3. Checking staff ID parameter...</h2>";
    $staffId = $_GET['id'] ?? '';
    if (empty($staffId)) {
        echo "✗ No staff ID provided<br>";
    } else {
        echo "✓ Staff ID: " . htmlspecialchars($staffId) . "<br>";
    }
    
    echo "<h2>4. Loading staff schedule functions...</h2>";
    require_once 'includes/staff_schedule_functions.php';
    echo "✓ Functions loaded<br>";
    
    echo "<h2>5. Checking staff user...</h2>";
    if (!empty($staffId)) {
        $staffUser = $database->fetch(
            "SELECT * FROM users WHERE id = ? AND role = 'STAFF'",
            [$staffId]
        );
        
        if (!$staffUser) {
            echo "✗ Staff member not found<br>";
            
            // Show available staff
            $allStaff = $database->fetchAll("SELECT id, name, email FROM users WHERE role = 'STAFF'");
            echo "Available staff:<br>";
            foreach ($allStaff as $staff) {
                echo "- " . $staff['name'] . " (ID: " . $staff['id'] . ")<br>";
                echo "  <a href='?id=" . $staff['id'] . "'>Test with this staff</a><br>";
            }
        } else {
            echo "✓ Staff found: " . $staffUser['name'] . "<br>";
            
            echo "<h2>6. Getting schedule...</h2>";
            $currentSchedule = getStaffScheduleForDisplay($staffId);
            if (!$currentSchedule) {
                echo "✗ No schedule found, creating default...<br>";
                $result = createStaffRecord($staffId);
                if ($result['success']) {
                    echo "✓ Default schedule created<br>";
                    $currentSchedule = getStaffScheduleForDisplay($staffId);
                } else {
                    echo "✗ Failed to create schedule: " . $result['error'] . "<br>";
                }
            } else {
                echo "✓ Schedule found<br>";
                echo "Schedule data: <pre>" . print_r($currentSchedule, true) . "</pre>";
            }
        }
    }
    
    echo "<h2>7. Database tables check...</h2>";
    $tables = $database->fetchAll("SHOW TABLES");
    $tableNames = array_column($tables, array_keys($tables[0])[0]);
    
    if (in_array('staff_schedules', $tableNames)) {
        echo "✓ staff_schedules table exists<br>";
        
        $count = $database->fetch("SELECT COUNT(*) as count FROM staff_schedules");
        echo "Records in staff_schedules: " . $count['count'] . "<br>";
    } else {
        echo "✗ staff_schedules table missing<br>";
    }
    
    if (in_array('users', $tableNames)) {
        echo "✓ users table exists<br>";
        
        $staffCount = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'STAFF'");
        echo "Staff users: " . $staffCount['count'] . "<br>";
    } else {
        echo "✗ users table missing<br>";
    }
    
} catch (Exception $e) {
    echo "<h2>ERROR</h2>";
    echo "Message: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}
?>
