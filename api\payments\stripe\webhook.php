<?php
/**
 * Stripe Webhook Handler
 * Handles real-time payment updates from Stripe
 */

header('Content-Type: application/json');

require_once __DIR__ . '/../../../config/app.php';

// Check if Stripe is enabled
if (!STRIPE_ENABLED) {
    http_response_code(403);
    echo json_encode(['error' => 'Stripe webhooks are disabled']);
    exit;
}

try {
    // Get the payload
    $payload = @file_get_contents('php://input');
    $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? '';
    
    if (empty($payload) || empty($sig_header)) {
        throw new Exception('Missing payload or signature');
    }
    
    // Verify webhook signature (you should set this in production)
    $webhook_secret = 'whsec_...'; // Replace with your actual webhook secret
    
    // For now, we'll skip signature verification in development
    // In production, uncomment the verification code below
    /*
    require_once __DIR__ . '/../../../vendor/stripe/stripe-php/init.php';
    \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
    
    try {
        $event = \Stripe\Webhook::constructEvent($payload, $sig_header, $webhook_secret);
    } catch (\UnexpectedValueException $e) {
        throw new Exception('Invalid payload');
    } catch (\Stripe\Exception\SignatureVerificationException $e) {
        throw new Exception('Invalid signature');
    }
    */
    
    // Parse the event
    $event = json_decode($payload, true);
    
    if (!$event) {
        throw new Exception('Invalid JSON payload');
    }
    
    // Log webhook receipt
    global $database;
    $webhookId = generateUUID();
    
    $database->execute("
        INSERT INTO payment_webhooks (
            id, gateway, webhook_id, event_type, payload, 
            signature_verified, created_at
        ) VALUES (?, 'STRIPE', ?, ?, ?, FALSE, NOW())
    ", [
        $webhookId,
        $event['id'] ?? 'unknown',
        $event['type'] ?? 'unknown',
        $payload
    ]);
    
    // Handle the event
    switch ($event['type']) {
        case 'payment_intent.succeeded':
            handlePaymentSucceeded($event['data']['object'], $webhookId);
            break;
            
        case 'payment_intent.payment_failed':
            handlePaymentFailed($event['data']['object'], $webhookId);
            break;
            
        case 'payment_intent.canceled':
            handlePaymentCanceled($event['data']['object'], $webhookId);
            break;
            
        default:
            // Log unhandled event
            error_log("Unhandled Stripe webhook event: " . $event['type']);
            break;
    }
    
    // Mark webhook as processed
    $database->execute("
        UPDATE payment_webhooks 
        SET status = 'PROCESSED', processed_at = NOW()
        WHERE id = ?
    ", [$webhookId]);
    
    echo json_encode(['status' => 'success']);
    
} catch (Exception $e) {
    error_log("Stripe webhook error: " . $e->getMessage());
    
    if (isset($webhookId)) {
        $database->execute("
            UPDATE payment_webhooks 
            SET status = 'FAILED', error_message = ?, processed_at = NOW()
            WHERE id = ?
        ", [$e->getMessage(), $webhookId]);
    }
    
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}

function handlePaymentSucceeded($paymentIntent, $webhookId) {
    global $database;
    
    try {
        // Find payment by Stripe payment intent ID
        $payment = $database->fetch("
            SELECT p.*, b.user_id, b.id as booking_id
            FROM payments p
            INNER JOIN bookings b ON p.booking_id = b.id
            WHERE p.stripe_payment_id = ? AND p.payment_gateway = 'STRIPE'
        ", [$paymentIntent['id']]);
        
        if (!$payment) {
            throw new Exception('Payment not found for payment intent: ' . $paymentIntent['id']);
        }
        
        // Update payment status if not already completed
        if ($payment['status'] !== 'COMPLETED') {
            updatePaymentStatus($payment['id'], 'COMPLETED', [
                'stripe_payment_id' => $paymentIntent['id'],
                'stripe_status' => $paymentIntent['status']
            ]);
            
            logPaymentEvent($payment['id'], 'COMPLETED', 'STRIPE', [
                'webhook_id' => $webhookId,
                'payment_intent_id' => $paymentIntent['id'],
                'amount_received' => $paymentIntent['amount_received']
            ]);
        }
        
    } catch (Exception $e) {
        error_log("Error handling payment success: " . $e->getMessage());
        throw $e;
    }
}

function handlePaymentFailed($paymentIntent, $webhookId) {
    global $database;
    
    try {
        // Find payment by Stripe payment intent ID
        $payment = $database->fetch("
            SELECT p.*, b.user_id, b.id as booking_id
            FROM payments p
            INNER JOIN bookings b ON p.booking_id = b.id
            WHERE p.stripe_payment_id = ? AND p.payment_gateway = 'STRIPE'
        ", [$paymentIntent['id']]);
        
        if (!$payment) {
            throw new Exception('Payment not found for payment intent: ' . $paymentIntent['id']);
        }
        
        // Update payment status
        updatePaymentStatus($payment['id'], 'FAILED', [
            'stripe_payment_id' => $paymentIntent['id'],
            'stripe_status' => $paymentIntent['status']
        ]);
        
        logPaymentEvent($payment['id'], 'FAILED', 'STRIPE', [
            'webhook_id' => $webhookId,
            'payment_intent_id' => $paymentIntent['id'],
            'last_payment_error' => $paymentIntent['last_payment_error'] ?? null
        ]);
        
    } catch (Exception $e) {
        error_log("Error handling payment failure: " . $e->getMessage());
        throw $e;
    }
}

function handlePaymentCanceled($paymentIntent, $webhookId) {
    global $database;
    
    try {
        // Find payment by Stripe payment intent ID
        $payment = $database->fetch("
            SELECT p.*, b.user_id, b.id as booking_id
            FROM payments p
            INNER JOIN bookings b ON p.booking_id = b.id
            WHERE p.stripe_payment_id = ? AND p.payment_gateway = 'STRIPE'
        ", [$paymentIntent['id']]);
        
        if (!$payment) {
            throw new Exception('Payment not found for payment intent: ' . $paymentIntent['id']);
        }
        
        // Update payment status
        updatePaymentStatus($payment['id'], 'FAILED', [
            'stripe_payment_id' => $paymentIntent['id'],
            'stripe_status' => $paymentIntent['status']
        ]);
        
        logPaymentEvent($payment['id'], 'FAILED', 'STRIPE', [
            'webhook_id' => $webhookId,
            'payment_intent_id' => $paymentIntent['id'],
            'cancellation_reason' => $paymentIntent['cancellation_reason'] ?? 'unknown'
        ]);
        
    } catch (Exception $e) {
        error_log("Error handling payment cancellation: " . $e->getMessage());
        throw $e;
    }
}
