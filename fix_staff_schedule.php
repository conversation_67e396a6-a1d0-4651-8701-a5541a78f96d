<?php
/**
 * Fix Staff Schedule Issues
 * This script creates the necessary tables and data for staff scheduling
 */

require_once 'config/app.php';

echo "=== Fixing Staff Schedule System ===\n";

try {
    // Start transaction
    $database->beginTransaction();
    
    echo "1. Creating staff_schedules table...\n";
    
    // Create staff_schedules table
    $createStaffSchedulesSQL = "
    CREATE TABLE IF NOT EXISTS staff_schedules (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        staff_id VARCHAR(36) NULL COMMENT 'For backward compatibility',
        day_of_week VARCHAR(20) NULL COMMENT 'For individual day schedules',
        start_time TIME NULL,
        end_time TIME NULL,
        is_working BOOLEAN DEFAULT TRUE,
        role VARCHAR(100) DEFAULT 'Staff Member',
        hourly_rate DECIMAL(8,2) DEFAULT 50.00,
        bio TEXT,
        experience INT DEFAULT 0,
        schedule JSON COMMENT 'Weekly schedule as JSON for full schedule storage',
        specialties <PERSON><PERSON><PERSON> COMMENT 'For backward compatibility',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_staff_schedules_user (user_id),
        INDEX idx_staff_schedules_day (day_of_week),
        INDEX idx_staff_schedules_working (is_working)
    )";
    
    $database->query($createStaffSchedulesSQL);
    echo "   ✓ staff_schedules table created/verified\n";
    
    echo "2. Creating staff_specialties table...\n";
    
    // Create staff_specialties table
    $createStaffSpecialtiesSQL = "
    CREATE TABLE IF NOT EXISTS staff_specialties (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        service_id VARCHAR(36) NOT NULL,
        proficiency_level ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT') DEFAULT 'INTERMEDIATE',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_service (user_id, service_id),
        INDEX idx_staff_specialties_user (user_id),
        INDEX idx_staff_specialties_service (service_id)
    )";
    
    $database->query($createStaffSpecialtiesSQL);
    echo "   ✓ staff_specialties table created/verified\n";
    
    echo "3. Checking staff users...\n";
    
    // Get all staff users
    $staffUsers = $database->fetchAll("SELECT id, name, email FROM users WHERE role = 'STAFF'");
    echo "   Found " . count($staffUsers) . " staff users\n";
    
    if (empty($staffUsers)) {
        echo "   ⚠ No staff users found. Creating a sample staff user...\n";
        
        // Create a sample staff user
        $staffId = 'staff-' . uniqid();
        $database->query(
            "INSERT INTO users (id, name, email, password, role, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
            [$staffId, 'Sample Staff', '<EMAIL>', password_hash('password123', PASSWORD_DEFAULT), 'STAFF']
        );
        
        $staffUsers = [['id' => $staffId, 'name' => 'Sample Staff', 'email' => '<EMAIL>']];
        echo "   ✓ Created sample staff user\n";
    }
    
    echo "4. Creating staff schedule records...\n";
    
    $created = 0;
    foreach ($staffUsers as $staff) {
        // Check if schedule record exists
        $existing = $database->fetch("SELECT id FROM staff_schedules WHERE user_id = ?", [$staff['id']]);
        
        if (!$existing) {
            $scheduleId = 'schedule-' . $staff['id'];
            
            // Create default schedule
            $defaultSchedule = [
                'monday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => true, 'available' => true, 'start' => '09:00', 'end' => '18:00'],
                'tuesday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => true, 'available' => true, 'start' => '09:00', 'end' => '18:00'],
                'wednesday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => true, 'available' => true, 'start' => '09:00', 'end' => '18:00'],
                'thursday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => true, 'available' => true, 'start' => '09:00', 'end' => '18:00'],
                'friday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => true, 'available' => true, 'start' => '09:00', 'end' => '18:00'],
                'saturday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => true, 'available' => true, 'start' => '09:00', 'end' => '18:00'],
                'sunday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => false, 'available' => false, 'start' => '09:00', 'end' => '18:00']
            ];
            
            $database->query(
                "INSERT INTO staff_schedules (id, user_id, role, hourly_rate, schedule, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
                [$scheduleId, $staff['id'], 'Staff Member', 50.00, json_encode($defaultSchedule)]
            );
            
            $created++;
            echo "   ✓ Created schedule for " . $staff['name'] . "\n";
        } else {
            echo "   - Schedule already exists for " . $staff['name'] . "\n";
        }
    }
    
    echo "5. Testing schedule functions...\n";
    
    // Include the functions
    require_once 'includes/staff_schedule_functions.php';
    
    // Test with first staff user
    if (!empty($staffUsers)) {
        $testUserId = $staffUsers[0]['id'];
        $schedule = getStaffScheduleForDisplay($testUserId);
        
        if ($schedule) {
            echo "   ✓ getStaffScheduleForDisplay() working\n";
            echo "   Schedule days: " . implode(', ', array_keys($schedule)) . "\n";
        } else {
            echo "   ✗ getStaffScheduleForDisplay() failed\n";
        }
    }
    
    // Commit transaction
    $database->commit();
    
    echo "\n=== Fix Complete ===\n";
    echo "Created $created new schedule records\n";
    echo "Staff schedule system should now be working!\n";
    echo "\nYou can now access: /admin/staff/schedule.php?id=" . $staffUsers[0]['id'] . "\n";
    
} catch (Exception $e) {
    $database->rollback();
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
