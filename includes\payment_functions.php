<?php
/**
 * Payment Functions
 * Handles Stripe and Flutterwave payment processing
 */

require_once __DIR__ . '/../config/app.php';

/**
 * Configure cURL with SSL options for development environment
 */
function configureCurlSSL($curl, $options = []) {
    $defaultOptions = [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_USERAGENT => 'Flix Salon Payment System/1.0',
        // SSL options for development environment
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
    ];

    // Merge with custom options
    $finalOptions = array_merge($defaultOptions, $options);

    curl_setopt_array($curl, $finalOptions);

    return $curl;
}

/**
 * Get eligible bookings for payment
 * Only CONFIRMED and COMPLETED bookings can be paid
 */
function getEligibleBookingsForPayment($userId) {
    global $database;
    
    return $database->fetchAll("
        SELECT 
            b.*,
            s.name as service_name,
            s.duration as service_duration,
            p.name as package_name,
            st.name as staff_name,
            pay.status as payment_status,
            pay.id as payment_id
        FROM bookings b
        LEFT JOIN services s ON b.service_id = s.id
        LEFT JOIN packages p ON b.package_id = p.id
        LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
        LEFT JOIN payments pay ON b.id = pay.booking_id
        WHERE b.user_id = ? 
        AND b.status IN ('CONFIRMED', 'COMPLETED')
        AND (pay.status IS NULL OR pay.status IN ('PENDING', 'FAILED'))
        ORDER BY b.date DESC, b.start_time DESC
    ", [$userId]);
}

/**
 * Create payment record
 */
function createPayment($bookingId, $userId, $amount, $gateway = 'STRIPE') {
    global $database;

    // Debug logging
    error_log('createPayment called with: bookingId=' . $bookingId . ', userId=' . $userId . ', amount=' . $amount . ' (' . gettype($amount) . '), gateway=' . $gateway);

    try {
        $database->beginTransaction();
        
        // Check if payment already exists
        $existingPayment = $database->fetch("
            SELECT id FROM payments WHERE booking_id = ?
        ", [$bookingId]);
        
        if ($existingPayment) {
            throw new Exception('Payment already exists for this booking');
        }
        
        // First, check if booking exists at all
        $bookingExists = $database->fetch("
            SELECT id, user_id, status, total_amount
            FROM bookings
            WHERE id = ?
        ", [$bookingId]);

        error_log('Booking existence check: ' . json_encode($bookingExists));
        error_log('Looking for booking ID: ' . $bookingId . ' with user ID: ' . $userId);

        if (!$bookingExists) {
            throw new Exception('Booking not found with ID: ' . $bookingId);
        }

        // Check if booking belongs to user
        if ($bookingExists['user_id'] !== $userId) {
            throw new Exception('Booking does not belong to user. Booking user: ' . $bookingExists['user_id'] . ', Current user: ' . $userId);
        }

        // Check if booking status is eligible for payment
        if (!in_array($bookingExists['status'], ['CONFIRMED', 'COMPLETED'])) {
            throw new Exception('Booking status not eligible for payment. Current status: ' . $bookingExists['status']);
        }

        // Verify booking belongs to user and is eligible for payment
        $booking = $database->fetch("
            SELECT id, user_id, status, total_amount
            FROM bookings
            WHERE id = ? AND user_id = ? AND status IN ('CONFIRMED', 'COMPLETED')
        ", [$bookingId, $userId]);

        if (!$booking) {
            throw new Exception('Booking verification failed after individual checks');
        }

        // Debug logging
        error_log('Booking found: ' . json_encode($booking));
        
        // Convert both amounts to integers for comparison
        $bookingAmount = intval($booking['total_amount']);
        $paymentAmount = intval($amount);

        if ($bookingAmount != $paymentAmount) {
            $errorMsg = 'Payment amount does not match booking amount. Booking: ' . $bookingAmount . ' (' . gettype($booking['total_amount']) . '), Payment: ' . $paymentAmount . ' (' . gettype($amount) . ')';
            error_log('Payment creation error: ' . $errorMsg);
            throw new Exception($errorMsg);
        }
        
        $paymentId = generateUUID();
        $paymentReference = 'FLIX_' . strtoupper($gateway) . '_' . time() . '_' . substr($paymentId, 0, 8);
        
        // Create payment record
        $database->execute("
            INSERT INTO payments (
                id, booking_id, user_id, amount, currency, status, 
                payment_method, payment_gateway, payment_reference, created_at
            ) VALUES (?, ?, ?, ?, ?, 'PENDING', 'card', ?, ?, NOW())
        ", [
            $paymentId, $bookingId, $userId, $amount, 
            CURRENCY_CODE, $gateway, $paymentReference
        ]);
        
        // Log payment creation
        logPaymentEvent($paymentId, 'CREATED', $gateway, [
            'booking_id' => $bookingId,
            'amount' => $amount,
            'currency' => CURRENCY_CODE,
            'reference' => $paymentReference
        ]);
        
        $database->commit();
        
        return [
            'success' => true,
            'payment_id' => $paymentId,
            'payment_reference' => $paymentReference
        ];
        
    } catch (Exception $e) {
        $database->rollback();
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Update payment status
 */
function updatePaymentStatus($paymentId, $status, $gatewayData = []) {
    global $database;
    
    try {
        $database->beginTransaction();
        
        // Update payment record
        $updateFields = ['status = ?', 'updated_at = NOW()'];
        $updateParams = [$status];
        
        if (isset($gatewayData['stripe_payment_id'])) {
            $updateFields[] = 'stripe_payment_id = ?';
            $updateParams[] = $gatewayData['stripe_payment_id'];
        }
        
        if (isset($gatewayData['flutterwave_tx_ref'])) {
            $updateFields[] = 'flutterwave_tx_ref = ?';
            $updateParams[] = $gatewayData['flutterwave_tx_ref'];
        }
        
        if (isset($gatewayData['flutterwave_tx_id'])) {
            $updateFields[] = 'flutterwave_tx_id = ?';
            $updateParams[] = $gatewayData['flutterwave_tx_id'];
        }
        
        if (!empty($gatewayData)) {
            $updateFields[] = 'payment_data = ?';
            $updateParams[] = json_encode($gatewayData);
        }
        
        $updateParams[] = $paymentId;
        
        $database->execute("
            UPDATE payments 
            SET " . implode(', ', $updateFields) . "
            WHERE id = ?
        ", $updateParams);
        
        // If payment is completed, update booking status and award points
        if ($status === 'COMPLETED') {
            $payment = $database->fetch("
                SELECT booking_id, user_id, amount FROM payments WHERE id = ?
            ", [$paymentId]);
            
            if ($payment) {
                // Update booking status if it's still CONFIRMED
                $database->execute("
                    UPDATE bookings 
                    SET status = 'COMPLETED', updated_at = NOW()
                    WHERE id = ? AND status = 'CONFIRMED'
                ", [$payment['booking_id']]);
                
                // Award points for completed payment
                require_once __DIR__ . '/rewards_functions.php';
                $pointsToAward = calculatePointsForBooking($payment['amount'], $payment['user_id']);
                if ($pointsToAward > 0) {
                    awardPoints($payment['user_id'], $pointsToAward, 'Points earned from completed booking payment');
                    
                    // Update booking with points earned
                    $database->execute("
                        UPDATE bookings 
                        SET points_earned = ?, updated_at = NOW()
                        WHERE id = ?
                    ", [$pointsToAward, $payment['booking_id']]);
                }
            }
        }
        
        $database->commit();
        return true;
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
}

/**
 * Log payment events
 */
function logPaymentEvent($paymentId, $eventType, $gateway, $eventData = []) {
    global $database;
    
    $logId = generateUUID();
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    
    $database->execute("
        INSERT INTO payment_logs (
            id, payment_id, event_type, gateway, event_data, 
            ip_address, user_agent, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ", [
        $logId, $paymentId, $eventType, $gateway, 
        json_encode($eventData), $ipAddress, $userAgent
    ]);
}

/**
 * Verify payment with gateway
 */
function verifyPayment($paymentId, $gateway) {
    global $database;
    
    $payment = $database->fetch("
        SELECT * FROM payments WHERE id = ?
    ", [$paymentId]);
    
    if (!$payment) {
        return ['success' => false, 'error' => 'Payment not found'];
    }
    
    // Increment verification attempts
    $database->execute("
        UPDATE payments 
        SET verification_attempts = verification_attempts + 1,
            last_verification_attempt = NOW()
        WHERE id = ?
    ", [$paymentId]);
    
    try {
        if ($gateway === 'DPO') {
            return verifyDpoPayment($payment);
        } elseif ($gateway === 'STRIPE') {
            return verifyStripePayment($payment);
        } elseif ($gateway === 'FLUTTERWAVE') {
            return verifyFlutterwavePayment($payment);
        }

        return ['success' => false, 'error' => 'Unsupported gateway'];
        
    } catch (Exception $e) {
        logPaymentEvent($paymentId, 'FAILED', $gateway, [
            'error' => $e->getMessage(),
            'verification_attempt' => $payment['verification_attempts'] + 1
        ]);
        
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Get payment by booking ID
 */
function getPaymentByBookingId($bookingId) {
    global $database;
    
    return $database->fetch("
        SELECT * FROM payments WHERE booking_id = ?
    ", [$bookingId]);
}

/**
 * Verify Stripe payment
 */
function verifyStripePayment($payment) {
    if (!STRIPE_ENABLED || !$payment['stripe_payment_id']) {
        return ['success' => false, 'error' => 'Stripe payment ID not found'];
    }

    try {
        // Initialize Stripe
        require_once __DIR__ . '/../vendor/stripe/stripe-php/init.php';
        \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);

        // Retrieve payment intent
        $paymentIntent = \Stripe\PaymentIntent::retrieve($payment['stripe_payment_id']);

        if ($paymentIntent->status === 'succeeded') {
            updatePaymentStatus($payment['id'], 'COMPLETED', [
                'stripe_payment_id' => $paymentIntent->id,
                'stripe_status' => $paymentIntent->status
            ]);

            logPaymentEvent($payment['id'], 'VERIFIED', 'STRIPE', [
                'payment_intent_id' => $paymentIntent->id,
                'status' => $paymentIntent->status
            ]);

            return ['success' => true, 'status' => 'completed'];
        } else {
            return ['success' => false, 'error' => 'Payment not completed', 'status' => $paymentIntent->status];
        }

    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Stripe verification failed: ' . $e->getMessage()];
    }
}

/**
 * Verify Flutterwave payment
 */
function verifyFlutterwavePayment($payment) {
    if (!FLUTTERWAVE_ENABLED || !$payment['flutterwave_tx_ref']) {
        return ['success' => false, 'error' => 'Flutterwave transaction reference not found'];
    }

    try {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.flutterwave.com/v3/transactions/verify_by_reference?tx_ref=" . $payment['flutterwave_tx_ref'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => array(
                "Authorization: Bearer " . FLUTTERWAVE_SECRET_KEY,
                "Content-Type: application/json"
            ),
            // SSL options for development environment
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_USERAGENT => 'Flix Salon Payment System/1.0'
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            throw new Exception("cURL Error: " . $err);
        }

        $result = json_decode($response, true);

        if ($result['status'] === 'success' && $result['data']['status'] === 'successful') {
            $txData = $result['data'];

            // Verify amount matches
            if ($txData['amount'] != $payment['amount']) {
                throw new Exception('Payment amount mismatch');
            }

            updatePaymentStatus($payment['id'], 'COMPLETED', [
                'flutterwave_tx_ref' => $txData['tx_ref'],
                'flutterwave_tx_id' => $txData['id'],
                'flutterwave_status' => $txData['status']
            ]);

            logPaymentEvent($payment['id'], 'VERIFIED', 'FLUTTERWAVE', [
                'tx_ref' => $txData['tx_ref'],
                'tx_id' => $txData['id'],
                'status' => $txData['status']
            ]);

            return ['success' => true, 'status' => 'completed'];
        } else {
            return ['success' => false, 'error' => 'Payment verification failed', 'data' => $result];
        }

    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Flutterwave verification failed: ' . $e->getMessage()];
    }
}

/**
 * Verify DPO payment
 */
function verifyDpoPayment($payment) {
    if (!DPO_ENABLED || !$payment['dpo_token']) {
        return ['success' => false, 'error' => 'DPO payment token not found'];
    }

    try {
        $verificationResult = verifyDpoToken($payment['dpo_token']);

        if ($verificationResult['success'] && $verificationResult['verified']) {
            // Payment successful
            $paymentData = json_decode($payment['payment_data'] ?? '{}', true);
            $updatedPaymentData = array_merge($paymentData, [
                'dpo_verification' => $verificationResult,
                'verified_at' => date('Y-m-d H:i:s')
            ]);

            updatePaymentStatus($payment['id'], 'COMPLETED', $updatedPaymentData);

            logPaymentEvent($payment['id'], 'VERIFIED', 'DPO', [
                'dpo_token' => $payment['dpo_token'],
                'verification_result' => $verificationResult
            ]);

            return ['success' => true, 'status' => 'completed'];

        } else if ($verificationResult['success'] && $verificationResult['result'] === '900') {
            // Payment pending
            return ['success' => true, 'status' => 'pending'];

        } else {
            // Payment failed
            return ['success' => false, 'error' => 'Payment verification failed: ' . ($verificationResult['resultExplanation'] ?? 'Unknown error')];
        }

    } catch (Exception $e) {
        return ['success' => false, 'error' => 'DPO verification failed: ' . $e->getMessage()];
    }
}

/**
 * Get payment statistics
 */
function getPaymentStatistics($userId = null) {
    global $database;

    $whereClause = $userId ? "WHERE user_id = ?" : "";
    $params = $userId ? [$userId] : [];

    return $database->fetch("
        SELECT
            COUNT(*) as total_payments,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_payments,
            COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_payments,
            COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_payments,
            SUM(CASE WHEN status = 'COMPLETED' THEN amount ELSE 0 END) as total_revenue,
            AVG(CASE WHEN status = 'COMPLETED' THEN amount ELSE NULL END) as average_payment
        FROM payments
        $whereClause
    ", $params);
}
