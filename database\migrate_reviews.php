<?php
/**
 * Reviews Table Migration Script
 * Run this script to update the reviews table structure
 */

require_once __DIR__ . '/../config/app.php';

echo "Starting Reviews Table Migration...\n";

try {
    // Read the SQL migration file
    $sqlFile = __DIR__ . '/update_reviews_table.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("Migration file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Remove the USE statement since we're already connected to the database
    $sql = preg_replace('/USE\s+[^;]+;/', '', $sql);
    
    // Split the SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "Executing " . count($statements) . " SQL statements...\n";
    
    foreach ($statements as $index => $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        echo "Executing statement " . ($index + 1) . "...\n";
        
        try {
            $database->execute($statement);
            echo "✓ Success\n";
        } catch (Exception $e) {
            // Some statements might fail if columns already exist, that's okay
            if (strpos($e->getMessage(), 'Duplicate column name') !== false || 
                strpos($e->getMessage(), 'already exists') !== false) {
                echo "⚠ Skipped (already exists): " . $e->getMessage() . "\n";
            } else {
                echo "✗ Error: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Verify the table structure
    echo "\nVerifying table structure...\n";
    $result = $database->fetchAll("DESCRIBE reviews");
    
    echo "Reviews table columns:\n";
    foreach ($result as $column) {
        echo "- {$column['Field']} ({$column['Type']})\n";
    }
    
    // Check if we have any reviews
    $count = $database->fetch("SELECT COUNT(*) as count FROM reviews");
    echo "\nTotal reviews in database: " . $count['count'] . "\n";
    
    echo "\n✅ Migration completed successfully!\n";
    
} catch (Exception $e) {
    echo "\n❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
