<?php
/**
 * AJAX Reminder Processing Endpoint
 * Processes pending reminders via AJAX calls from browser
 */

// Set execution time limit to prevent browser timeouts
set_time_limit(30);

// Set JSON content type
header('Content-Type: application/json');

// Prevent caching
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// CORS headers for AJAX requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config/app.php';

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'data' => [
        'processed' => 0,
        'sent' => 0,
        'failed' => 0,
        'skipped' => 0,
        'missed_recovered' => 0,
        'processing_time' => 0
    ],
    'timestamp' => date('Y-m-d H:i:s'),
    'debug' => []
];

try {
    $startTime = microtime(true);

    // Security check - basic validation
    $allowedMethods = ['GET', 'POST'];
    if (!in_array($_SERVER['REQUEST_METHOD'], $allowedMethods)) {
        throw new Exception('Invalid request method');
    }

    // Log request details for debugging
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    error_log("AJAX reminder processing started from IP: $clientIP, User-Agent: $userAgent");

    // Check if required functions exist
    $requiredFunctions = ['createReminderTables', 'processPendingReminders', 'checkMissedReminders'];
    foreach ($requiredFunctions as $func) {
        if (!function_exists($func)) {
            throw new Exception("Required function '$func' not available");
        }
    }

    // Initialize reminder system if needed
    $response['debug'][] = 'Initializing reminder system...';
    $tablesCreated = createReminderTables();
    if (!$tablesCreated) {
        throw new Exception('Failed to initialize reminder tables');
    }
    $response['debug'][] = 'Reminder system initialized successfully';
    
    // 1. Process pending reminders
    $response['debug'][] = 'Processing pending reminders...';
    $pendingResults = processPendingReminders();
    
    $response['data']['processed'] = $pendingResults['processed'];
    $response['data']['sent'] = $pendingResults['sent'];
    $response['data']['failed'] = $pendingResults['failed'];
    $response['data']['skipped'] = $pendingResults['skipped'];
    
    // 2. Check for missed reminders and recover them
    $response['debug'][] = 'Checking for missed reminders...';
    $missedResults = checkMissedReminders();
    $response['data']['missed_recovered'] = $missedResults['sent'];
    
    // 3. Process overdue reminders (even if time range passed)
    $response['debug'][] = 'Processing overdue reminders...';
    try {
        $overdueResults = processOverdueReminders();
        $response['data']['overdue_processed'] = $overdueResults['processed'] ?? 0;
        $response['data']['overdue_sent'] = $overdueResults['sent'] ?? 0;
        $response['debug'][] = "Overdue processing completed: {$overdueResults['processed']} processed, {$overdueResults['sent']} sent";
    } catch (Exception $e) {
        $response['debug'][] = 'Overdue processing failed: ' . $e->getMessage();
        $response['data']['overdue_processed'] = 0;
        $response['data']['overdue_sent'] = 0;
    }
    
    // Calculate processing time
    $endTime = microtime(true);
    $response['data']['processing_time'] = round(($endTime - $startTime) * 1000, 2); // milliseconds
    
    // Build success message
    $totalSent = $response['data']['sent'] + $response['data']['missed_recovered'] + $overdueResults['sent'];
    $totalProcessed = $response['data']['processed'] + $response['data']['missed_recovered'] + $overdueResults['processed'];
    
    if ($totalProcessed > 0) {
        $response['message'] = "Processed {$totalProcessed} reminders. Sent: {$totalSent}, Failed: {$response['data']['failed']}";
    } else {
        $response['message'] = "No reminders to process at this time";
    }
    
    $response['success'] = true;
    
    // Log successful processing
    error_log("AJAX reminder processing completed: " . json_encode($response['data']));
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Error processing reminders: ' . $e->getMessage();
    $response['debug'][] = 'Error: ' . $e->getMessage();
    
    // Log error
    error_log("AJAX reminder processing error: " . $e->getMessage());
}

// Output JSON response
echo json_encode($response, JSON_PRETTY_PRINT);

/**
 * Process overdue reminders (even if time range passed)
 * This ensures reminders are sent even if they're late
 */
function processOverdueReminders() {
    global $database;
    
    $results = [
        'processed' => 0,
        'sent' => 0,
        'failed' => 0
    ];
    
    try {
        // Get overdue reminders for active bookings
        $overdueReminders = $database->fetchAll(
            "SELECT br.*, b.date, b.start_time, b.status as booking_status
             FROM booking_reminders br
             JOIN bookings b ON br.booking_id = b.id
             WHERE br.status = 'PENDING' 
             AND br.scheduled_time < NOW()
             AND br.attempts < br.max_attempts
             AND b.status IN ('CONFIRMED', 'PENDING')
             AND CONCAT(b.date, ' ', b.start_time) > NOW()
             ORDER BY br.priority DESC, br.scheduled_time ASC
             LIMIT 20"
        );
        
        foreach ($overdueReminders as $reminder) {
            $results['processed']++;
            
            // Process the overdue reminder
            $success = processReminder($reminder);
            
            if ($success) {
                $results['sent']++;
            } else {
                $results['failed']++;
            }
        }
        
        return $results;
        
    } catch (Exception $e) {
        error_log("Failed to process overdue reminders: " . $e->getMessage());
        return $results;
    }
}

/**
 * Get current reminder statistics for AJAX response
 */
function getQuickReminderStats() {
    global $database;
    
    try {
        return $database->fetch(
            "SELECT 
                COUNT(*) as total_pending,
                SUM(CASE WHEN priority = 'HIGH' THEN 1 ELSE 0 END) as high_priority_pending,
                SUM(CASE WHEN scheduled_time < NOW() THEN 1 ELSE 0 END) as overdue_pending
             FROM booking_reminders 
             WHERE status = 'PENDING'"
        );
    } catch (Exception $e) {
        return [
            'total_pending' => 0,
            'high_priority_pending' => 0,
            'overdue_pending' => 0
        ];
    }
}
?>
