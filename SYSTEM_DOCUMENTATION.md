# Flix Salon & SPA - Complete System Documentation

## 📋 Table of Contents
1. [System Overview](#system-overview)
2. [Architecture & Technology Stack](#architecture--technology-stack)
3. [Core Features](#core-features)
4. [Database Schema](#database-schema)
5. [User Roles & Permissions](#user-roles--permissions)
6. [API Endpoints](#api-endpoints)
7. [Security Features](#security-features)
8. [Recent Improvements](#recent-improvements)
9. [System Requirements](#system-requirements)
10. [Installation Guide](#installation-guide)

---

## 🎯 System Overview

**Flix Salon & SPA** is a comprehensive PHP-based salon management system that provides a complete solution for managing salon operations, bookings, staff, customers, and business analytics. The system features three distinct panels (Admin, Staff, Customer) with role-based access control and a modern, responsive design.

### Key Characteristics:
- **Multi-Panel Architecture**: Admin, Staff, and Customer interfaces
- **Role-Based Access Control**: Secure authentication and authorization
- **Real-Time Operations**: Live booking management and notifications
- **Mobile-First Design**: Fully responsive across all devices
- **Tanzanian Market Focus**: TSH currency, local business practices
- **Scalable Architecture**: Modular design for easy expansion

---

## 🏗️ Architecture & Technology Stack

### Backend Technologies:
- **PHP 8.0+**: Core application logic
- **MySQL 8.0+**: Primary database
- **Apache/Nginx**: Web server
- **Session Management**: Persistent sessions with database storage

### Frontend Technologies:
- **HTML5/CSS3**: Modern markup and styling
- **Tailwind CSS**: Utility-first CSS framework
- **JavaScript (ES6+)**: Interactive functionality
- **AJAX**: Asynchronous operations
- **Responsive Design**: Mobile-first approach

### Architecture Pattern:
- **MVC-Inspired**: Separation of concerns
- **Modular Design**: Feature-based organization
- **Database-Centric**: Centralized data management
- **API-Driven**: RESTful endpoints for dynamic operations

### Directory Structure:
```
flix-php/
├── config/           # Configuration files
├── includes/         # Shared PHP functions and components
├── admin/           # Admin panel pages
├── staff/           # Staff panel pages
├── customer/        # Customer panel pages
├── auth/            # Authentication pages
├── api/             # API endpoints
├── database/        # Database migrations and seeds
├── assets/          # Static assets (CSS, JS, images)
├── uploads/         # File uploads directory
└── docs/            # Documentation
```

---

## ✨ Core Features

### 1. **Booking Management System**
- **Multi-Type Bookings**: Services and packages
- **Real-Time Availability**: Staff schedule integration
- **Conflict Detection**: Automatic scheduling validation
- **Status Management**: PENDING → CONFIRMED → COMPLETED workflow
- **Automatic Expiration**: Time-based status updates
- **Points Integration**: Earn/redeem loyalty points

### 2. **User Management**
- **Three User Roles**: Admin, Staff, Customer
- **Profile Management**: Complete user profiles
- **Authentication**: Secure login/registration
- **Session Persistence**: 2-week session duration
- **Password Security**: Strong password requirements

### 3. **Staff Management**
- **Schedule Management**: Weekly availability settings
- **Appointment Tracking**: Staff-specific booking views
- **Earnings Tracking**: Commission and payment tracking
- **Specialties Management**: Service-specific assignments
- **Performance Analytics**: Booking and revenue metrics

### 4. **Service & Package Management**
- **Dynamic Categories**: Database-driven service categories
- **Package Creation**: Multi-service bundles
- **Pricing Management**: TSH integer-based pricing
- **Image Management**: Service/package galleries
- **Availability Control**: Active/inactive status

### 5. **Notification System**
- **Real-Time Alerts**: Booking updates and reminders
- **Multi-Channel**: In-app and email notifications
- **Category-Based**: Different notification types
- **Auto-Refresh**: 30-60 second update intervals
- **Priority Levels**: HIGH, MEDIUM, LOW classification

### 6. **Rewards & Loyalty System**
- **Points Earning**: 1 point per TSH 1,000 spent
- **Points Redemption**: Discount on future bookings
- **Referral Program**: Bonus points for referrals
- **Tier System**: Customer loyalty levels
- **Transaction History**: Complete points tracking

### 7. **Wishlist System**
- **Heart Icons**: Toggle favorite services/packages
- **Session Persistence**: Guest wishlist transfer on login
- **AJAX Operations**: Smooth add/remove functionality
- **Counter Badges**: Real-time wishlist counts
- **Cross-Platform**: Available on all user interfaces

### 8. **Admin Dashboard**
- **Analytics**: Revenue, bookings, customer metrics
- **Real-Time Stats**: Live business performance data
- **Chart Visualizations**: Monthly revenue trends
- **Recent Activity**: Latest bookings and updates
- **Management Tools**: Complete CRUD operations

---

## 🗄️ Database Schema

### Core Tables:

#### **users**
- Primary user table for all roles (Admin, Staff, Customer)
- UUID primary keys for security
- Role-based access control
- Points and referral system integration

#### **bookings**
- Central booking management
- Links users, services/packages, and staff
- Status workflow management
- Points earning/redemption tracking

#### **services & packages**
- Service catalog management
- Category-based organization
- TSH integer pricing
- Image and description storage

#### **staff_schedules**
- Staff availability management
- JSON-based schedule storage
- Hourly rate and bio information
- Specialties and experience tracking

#### **notifications**
- Comprehensive notification system
- Category and priority classification
- Metadata storage for rich notifications
- Expiration and read status tracking

#### **wishlists**
- Customer favorite items
- Service and package support
- Session-to-database persistence

#### **point_transactions**
- Complete points history
- Earning and redemption tracking
- Booking association
- Transaction descriptions

### Key Relationships:
- Users → Bookings (One-to-Many)
- Services/Packages → Bookings (One-to-Many)
- Staff → Bookings (One-to-Many)
- Users → Wishlists (One-to-Many)
- Users → Point Transactions (One-to-Many)

---

## 👥 User Roles & Permissions

### **Admin Role**
- **Full System Access**: All features and data
- **User Management**: Create/edit/delete all users
- **Booking Management**: Complete booking control
- **Staff Management**: Schedule and assignment control
- **Analytics Access**: All reports and metrics
- **System Settings**: Configuration management
- **Content Management**: Services, packages, offers

### **Staff Role**
- **Personal Dashboard**: Own appointments and earnings
- **Schedule Management**: Set availability and working hours
- **Appointment Updates**: Change booking status
- **Customer Interaction**: View customer details
- **Earnings Tracking**: Personal revenue metrics
- **Notification Access**: Booking-related alerts

### **Customer Role**
- **Booking Creation**: Schedule appointments
- **Profile Management**: Personal information updates
- **Booking History**: View past and upcoming appointments
- **Wishlist Management**: Save favorite services
- **Points Tracking**: View and redeem loyalty points
- **Notification Access**: Booking confirmations and reminders

---

## 🔌 API Endpoints

### **Authentication APIs**
- `POST /auth/login.php` - User login
- `POST /auth/register.php` - User registration
- `GET /auth/logout.php` - User logout

### **Booking APIs**
- `GET /api/customer/bookings.php` - Get user bookings
- `POST /api/customer/bookings.php` - Create booking
- `PUT /api/admin/bookings.php` - Update booking
- `DELETE /api/admin/bookings.php` - Cancel booking

### **Wishlist APIs**
- `POST /api/wishlist.php?action=toggle` - Toggle wishlist item
- `GET /api/wishlist.php?action=count` - Get wishlist count
- `GET /api/wishlist.php?action=check` - Check item status

### **Notification APIs**
- `GET /api/admin/notifications.php` - Get notifications
- `POST /api/admin/notifications.php` - Mark as read
- `GET /api/admin/dashboard-stats.php` - Dashboard statistics

### **Staff APIs**
- `GET /api/staff/appointments.php` - Get staff appointments
- `POST /api/staff/schedule.php` - Update schedule
- `GET /api/staff/earnings.php` - Get earnings data

---

## 🔒 Security Features

### **Authentication Security**
- **Password Hashing**: PHP password_hash() with bcrypt
- **Session Management**: Database-stored sessions
- **Session Regeneration**: Prevents session fixation
- **Persistent Sessions**: 2-week expiration with sliding renewal
- **Role-Based Access**: Strict permission checking

### **Input Security**
- **SQL Injection Prevention**: Prepared statements
- **XSS Protection**: Input sanitization and output encoding
- **CSRF Protection**: Token-based form protection
- **File Upload Security**: Type and size validation
- **Input Validation**: Server-side validation for all inputs

### **Application Security**
- **Secure Headers**: HTTP security headers
- **HTTPS Ready**: SSL/TLS configuration support
- **Error Handling**: Secure error messages
- **Logging**: Security event logging
- **Access Control**: URL-based permission checking

---

## 🚀 Recent Improvements

### **Authentication Enhancement (2024)**
- Modern design with glass morphism effects
- Real-time password validation with strength meter
- Desktop-optimized two-column registration layout
- Smart requirements panel with focus-based display
- Full navigation integration (header, footer, mobile menu)

### **TSH Currency Conversion (2024)**
- Complete migration from decimal to integer pricing
- Database schema updates for all price fields
- Updated display formatting throughout application
- Form validation changes for whole numbers only
- Performance improvements with integer calculations

### **Wishlist System (2024)**
- Comprehensive favorite system for services/packages
- Session-to-database persistence for guest users
- AJAX-powered heart icons with smooth animations
- Toast notification system with proper cleanup
- Counter badges with real-time updates

### **Staff Panel Fixes (2024)**
- Complete staff dashboard functionality
- Schedule management with JSON storage
- Appointment filtering and status updates
- Earnings tracking and commission calculation
- Notification integration for staff assignments

### **Notification System Enhancement (2024)**
- Real-time notification system with auto-refresh
- Category-based notification organization
- Priority levels and visual differentiation
- Admin-to-staff notification triggers
- Booking reminder system (24h, 5h, 30min, on-time)

---

## 💻 System Requirements

### **Server Requirements**
- **PHP**: 8.0 or higher
- **MySQL**: 8.0 or higher
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **PHP Extensions**: PDO, mysqli, GD, mbstring, openssl
- **Memory**: 512MB minimum, 1GB recommended
- **Storage**: 2GB minimum for application and uploads

### **Client Requirements**
- **Modern Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **JavaScript**: ES6+ support required
- **CSS**: Grid and Flexbox support
- **Mobile**: iOS 12+, Android 8+

### **Development Environment**
- **XAMPP/WAMP**: For local development
- **Git**: Version control
- **Composer**: PHP dependency management (optional)
- **Node.js**: For asset compilation (optional)

---

## 📦 Installation Guide

### **1. Download & Setup**
```bash
# Clone repository
git clone https://github.com/Craqinho/flix-php.git
cd flix-php

# Set permissions
chmod 755 uploads/
chmod 644 config/app.php
```

### **2. Database Configuration**
```sql
-- Create database
CREATE DATABASE flix_salonce;

-- Import schema
mysql -u root -p flix_salonce < database/migrations.sql

-- Run additional migrations
mysql -u root -p flix_salonce < database/update_booking_system.sql
mysql -u root -p flix_salonce < database/migrate_prices_to_integer.sql
```

### **3. Application Configuration**
```php
// Copy and edit config file
cp config/app.php.example config/app.php
cp config/database.php.example config/database.php

// Update database credentials in config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'flix_salonce');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

// Update app URL in config/app.php
define('APP_URL', 'http://your-domain.com/flix-php');
```

### **4. Initial Setup**
```bash
# Run setup script
php setup.php

# Seed sample data (optional)
php database/seed.php
```

### **5. Default Login Credentials**
- **Admin**: <EMAIL> / admin123
- **Staff**: <EMAIL> / staff123
- **Customer**: <EMAIL> / customer123

### **6. Production Deployment**
- Configure SSL/HTTPS
- Set up automated backups
- Configure email SMTP settings
- Set up cron jobs for booking expiration
- Configure file upload limits
- Enable security headers

---

## 🔧 Maintenance & Operations

### **Regular Maintenance Tasks**

#### **Daily Operations**
- Monitor system performance and error logs
- Check booking expiration cron job execution
- Verify notification delivery status
- Review user registration and login activities

#### **Weekly Maintenance**
- Database backup verification
- Clean up expired sessions and temporary files
- Review and respond to customer feedback
- Update service availability and pricing

#### **Monthly Tasks**
- Analyze system performance metrics
- Review security logs and access patterns
- Update system documentation
- Plan feature updates and improvements

### **Backup Strategy**
- **Database Backups**: Daily automated backups with 30-day retention
- **File Backups**: Weekly backup of uploads and configuration files
- **Code Backups**: Git repository with tagged releases
- **Recovery Testing**: Monthly backup restoration tests

### **Monitoring & Alerts**
- **System Health**: CPU, memory, disk usage monitoring
- **Application Errors**: Real-time error tracking and alerting
- **Performance Metrics**: Response time and throughput monitoring
- **Security Events**: Failed login attempts and suspicious activities

---

## 📊 Business Analytics & KPIs

### **Key Performance Indicators**

#### **Revenue Metrics**
- **Total Revenue**: Monthly and yearly revenue tracking
- **Revenue per Customer**: Average customer value
- **Revenue per Service**: Service profitability analysis
- **Revenue Growth**: Month-over-month and year-over-year growth

#### **Operational Metrics**
- **Booking Conversion Rate**: Visitors to bookings ratio
- **Appointment Show Rate**: Confirmed vs. completed bookings
- **Staff Utilization**: Working hours vs. booked hours
- **Customer Retention**: Repeat booking percentage

#### **Customer Metrics**
- **Customer Acquisition Cost**: Marketing spend per new customer
- **Customer Lifetime Value**: Total revenue per customer
- **Customer Satisfaction**: Review ratings and feedback
- **Loyalty Program Engagement**: Points earning and redemption rates

### **Reporting Capabilities**
- **Dashboard Analytics**: Real-time business metrics
- **Custom Reports**: Flexible report generation
- **Export Functions**: PDF, Excel, CSV export options
- **Scheduled Reports**: Automated report delivery

---

## 🔍 Troubleshooting Guide

### **Common Issues & Solutions**

#### **Login/Authentication Issues**
- **Problem**: Users cannot log in
- **Solutions**:
  - Check database connection
  - Verify session configuration
  - Clear browser cache and cookies
  - Check password hash validation

#### **Booking Creation Failures**
- **Problem**: Bookings fail to create
- **Solutions**:
  - Verify staff availability
  - Check service/package existence
  - Validate date/time format
  - Review database constraints

#### **Notification Delivery Issues**
- **Problem**: Notifications not being sent
- **Solutions**:
  - Check email SMTP configuration
  - Verify notification triggers
  - Review notification queue
  - Test email delivery manually

#### **Performance Issues**
- **Problem**: Slow page loading
- **Solutions**:
  - Check database query performance
  - Review server resource usage
  - Optimize images and assets
  - Enable caching mechanisms

### **Error Codes & Messages**
- **DB_CONNECTION_FAILED**: Database connection issues
- **INVALID_CREDENTIALS**: Authentication failures
- **BOOKING_CONFLICT**: Scheduling conflicts
- **INSUFFICIENT_PERMISSIONS**: Access control violations
- **PAYMENT_PROCESSING_ERROR**: Payment gateway issues

---

## 🌐 Integration Capabilities

### **Current Integrations**
- **Email Services**: SMTP integration for notifications
- **Payment Processing**: Stripe-ready payment structure
- **File Storage**: Local file upload and management
- **Session Management**: Database-backed sessions

### **Available Integration Points**
- **Webhook Support**: Real-time event notifications
- **REST API**: Complete API access for external systems
- **Database Access**: Direct database integration options
- **File System**: Upload and asset management APIs

### **Third-Party Integration Opportunities**
- **Calendar Systems**: Google Calendar, Outlook integration
- **Accounting Software**: QuickBooks, Xero connectivity
- **Marketing Tools**: Mailchimp, Constant Contact integration
- **Social Media**: Facebook, Instagram business integration
- **SMS Services**: Twilio, Nexmo for SMS notifications

---

## 📱 Mobile Optimization

### **Current Mobile Features**
- **Responsive Design**: Mobile-first approach
- **Touch Optimization**: Touch-friendly interface elements
- **Mobile Navigation**: Hamburger menu and mobile-specific layouts
- **Mobile Forms**: Optimized form inputs for mobile devices

### **Mobile Performance**
- **Fast Loading**: Optimized for mobile networks
- **Offline Capability**: Basic offline functionality
- **App-like Experience**: PWA-ready architecture
- **Cross-Platform**: Works on iOS and Android browsers

### **Mobile-Specific Enhancements**
- **Swipe Gestures**: Navigation and interaction gestures
- **Camera Integration**: Photo upload capabilities
- **Location Services**: GPS-based features
- **Push Notifications**: Real-time mobile alerts

---

## 🔐 Compliance & Standards

### **Data Protection**
- **GDPR Compliance**: European data protection standards
- **Data Encryption**: Sensitive data encryption at rest and in transit
- **Privacy Controls**: User data access and deletion rights
- **Audit Trails**: Complete data access logging

### **Security Standards**
- **OWASP Guidelines**: Web application security best practices
- **SQL Injection Prevention**: Prepared statements and input validation
- **XSS Protection**: Output encoding and CSP headers
- **CSRF Protection**: Token-based form protection

### **Accessibility Standards**
- **WCAG 2.1**: Web Content Accessibility Guidelines compliance
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and structure
- **Color Contrast**: Sufficient contrast ratios for readability

---

## 📚 Additional Resources

### **Documentation Files**
- `README.md` - Project overview and setup instructions
- `PROGRESS.md` - Development progress tracking
- `WISHLIST_SYSTEM_README.md` - Wishlist feature documentation
- `AUTH_IMPROVEMENTS_SUMMARY.md` - Authentication enhancements
- `TSH_CURRENCY_CONVERSION_SUMMARY.md` - Currency system changes
- `STAFF_PANEL_FIXES.md` - Staff panel improvements

### **Configuration Examples**
- `config/app.php.example` - Application configuration template
- `config/database.php.example` - Database configuration template
- `.htaccess` - Web server configuration
- `database/migrations.sql` - Database schema

### **Development Tools**
- `setup.php` - Initial system setup
- `database/seed.php` - Sample data generation
- `reset-database.php` - Database reset utility
- Various test files for system validation

### **Support & Community**
- **GitHub Repository**: https://github.com/Craqinho/flix-php
- **Issue Tracking**: GitHub Issues for bug reports and feature requests
- **Documentation Updates**: Regular documentation maintenance
- **Community Contributions**: Open to community improvements

---

*This comprehensive documentation serves as the complete reference for the Flix Salon & SPA system. For implementation guidance, refer to the System Improvements Roadmap and individual component documentation files.*
