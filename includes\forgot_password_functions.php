<?php
/**
 * Forgot Password Functions
 * Comprehensive OTP-based password reset system for Flix Salon & SPA
 */

/**
 * Generate a secure 6-digit OTP
 */
function generateOTP() {
    return sprintf('%06d', random_int(100000, 999999));
}

/**
 * Check if user can request OTP (rate limiting)
 */
function canRequestOTP($email) {
    global $database;
    
    $user = $database->fetch(
        "SELECT otp_attempts, otp_expires_at FROM users WHERE email = ?",
        [$email]
    );
    
    if (!$user) {
        return ['can_request' => false, 'error' => 'Email address not found'];
    }
    
    // Check if user has exceeded attempts in the last hour
    $oneHourAgo = date('Y-m-d H:i:s', strtotime('-1 hour'));
    $recentAttempts = $database->fetch(
        "SELECT COUNT(*) as attempt_count FROM users 
         WHERE email = ? AND otp_expires_at > ? AND otp_attempts >= 3",
        [$email, $oneHourAgo]
    );
    
    if ($recentAttempts && $recentAttempts['attempt_count'] > 0) {
        return ['can_request' => false, 'error' => 'Too many attempts. Please try again in 1 hour.'];
    }
    
    return ['can_request' => true];
}

/**
 * Generate and send OTP to user email
 */
function sendPasswordResetOTP($email) {
    global $database;
    
    // Check rate limiting
    $canRequest = canRequestOTP($email);
    if (!$canRequest['can_request']) {
        return ['success' => false, 'error' => $canRequest['error']];
    }
    
    // Get user details
    $user = $database->fetch(
        "SELECT id, name, email FROM users WHERE email = ?",
        [$email]
    );
    
    if (!$user) {
        return ['success' => false, 'error' => 'Email address not found'];
    }
    
    // Generate OTP
    $otp = generateOTP();
    $expiresAt = date('Y-m-d H:i:s', strtotime('+15 minutes'));
    
    try {
        // Update user with OTP
        $database->query(
            "UPDATE users SET reset_otp = ?, otp_expires_at = ?, otp_attempts = 0 WHERE email = ?",
            [$otp, $expiresAt, $email]
        );
        
        // Send OTP email
        $emailSent = sendOTPEmail($user['id'], $otp);
        
        if ($emailSent) {
            return [
                'success' => true,
                'message' => 'OTP sent successfully to your email address'
            ];
        } else {
            return ['success' => false, 'error' => 'Failed to send OTP email. Please try again.'];
        }
        
    } catch (Exception $e) {
        error_log("Failed to generate OTP for email: $email - " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to generate OTP. Please try again.'];
    }
}

/**
 * Verify OTP entered by user
 */
function verifyOTP($email, $enteredOTP) {
    global $database;
    
    $user = $database->fetch(
        "SELECT id, reset_otp, otp_expires_at, otp_attempts FROM users WHERE email = ?",
        [$email]
    );
    
    if (!$user) {
        return ['success' => false, 'error' => 'Invalid session. Please start over.'];
    }
    
    // Check if OTP has expired
    if (!$user['otp_expires_at'] || strtotime($user['otp_expires_at']) < time()) {
        // Clear expired OTP
        $database->query(
            "UPDATE users SET reset_otp = NULL, otp_expires_at = NULL WHERE email = ?",
            [$email]
        );
        return ['success' => false, 'error' => 'OTP has expired. Please request a new one.'];
    }
    
    // Check attempts
    if ($user['otp_attempts'] >= 3) {
        return ['success' => false, 'error' => 'Too many failed attempts. Please request a new OTP.'];
    }
    
    // Verify OTP
    if ($user['reset_otp'] === $enteredOTP) {
        // OTP is correct - clear it and return success
        $database->query(
            "UPDATE users SET reset_otp = NULL, otp_expires_at = NULL, otp_attempts = 0 WHERE email = ?",
            [$email]
        );
        
        return [
            'success' => true,
            'user_id' => $user['id'],
            'message' => 'OTP verified successfully'
        ];
    } else {
        // Increment attempts
        $database->query(
            "UPDATE users SET otp_attempts = otp_attempts + 1 WHERE email = ?",
            [$email]
        );
        
        $remainingAttempts = 3 - ($user['otp_attempts'] + 1);
        return [
            'success' => false,
            'error' => "Invalid OTP. You have $remainingAttempts attempts remaining."
        ];
    }
}

/**
 * Reset user password after OTP verification
 */
function resetUserPassword($userId, $newPassword) {
    global $database;
    
    try {
        // Validate password strength
        if (strlen($newPassword) < 8) {
            return ['success' => false, 'error' => 'Password must be at least 8 characters long'];
        }
        
        // Hash the new password
        $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT, ['cost' => BCRYPT_COST]);
        
        // Update user password
        $database->query(
            "UPDATE users SET password = ?, reset_otp = NULL, otp_expires_at = NULL, otp_attempts = 0 WHERE id = ?",
            [$hashedPassword, $userId]
        );
        
        return [
            'success' => true,
            'message' => 'Password reset successfully'
        ];
        
    } catch (Exception $e) {
        error_log("Failed to reset password for user ID: $userId - " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to reset password. Please try again.'];
    }
}

/**
 * Clean up expired OTPs (can be called periodically)
 */
function cleanupExpiredOTPs() {
    global $database;
    
    try {
        $database->query(
            "UPDATE users SET reset_otp = NULL, otp_expires_at = NULL, otp_attempts = 0 
             WHERE otp_expires_at IS NOT NULL AND otp_expires_at < NOW()"
        );
        return true;
    } catch (Exception $e) {
        error_log("Failed to cleanup expired OTPs: " . $e->getMessage());
        return false;
    }
}

/**
 * Get remaining time for OTP expiration
 */
function getOTPTimeRemaining($email) {
    global $database;
    
    $user = $database->fetch(
        "SELECT otp_expires_at FROM users WHERE email = ? AND otp_expires_at IS NOT NULL",
        [$email]
    );
    
    if (!$user || !$user['otp_expires_at']) {
        return 0;
    }
    
    $expiresAt = strtotime($user['otp_expires_at']);
    $now = time();
    
    return max(0, $expiresAt - $now);
}

/**
 * Check if email exists in the system
 */
function emailExists($email) {
    global $database;
    
    $user = $database->fetch(
        "SELECT id FROM users WHERE email = ?",
        [$email]
    );
    
    return $user !== false;
}
