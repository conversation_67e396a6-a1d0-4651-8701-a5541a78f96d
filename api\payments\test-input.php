<?php
/**
 * Test endpoint to debug payment input data
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Get all input methods
$rawInput = file_get_contents('php://input');
$jsonInput = json_decode($rawInput, true);
$postData = $_POST;
$getData = $_GET;

// Get all headers
$headers = [];
foreach ($_SERVER as $key => $value) {
    if (strpos($key, 'HTTP_') === 0) {
        $headers[str_replace('HTTP_', '', $key)] = $value;
    }
}

$response = [
    'success' => true,
    'debug_info' => [
        'method' => $_SERVER['REQUEST_METHOD'],
        'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'not set',
        'content_length' => $_SERVER['CONTENT_LENGTH'] ?? 'not set',
        'raw_input' => $rawInput,
        'raw_input_length' => strlen($rawInput),
        'json_input' => $jsonInput,
        'json_last_error' => json_last_error_msg(),
        'post_data' => $postData,
        'get_data' => $getData,
        'headers' => $headers,
        'session_id' => session_id(),
        'user_logged_in' => isset($_SESSION['user_id']),
        'user_id' => $_SESSION['user_id'] ?? 'not set',
        'user_role' => $_SESSION['user_role'] ?? 'not set'
    ]
];

// If JSON input exists, extract the specific fields
if ($jsonInput) {
    $response['extracted_data'] = [
        'booking_id' => $jsonInput['booking_id'] ?? 'not set',
        'booking_id_type' => gettype($jsonInput['booking_id'] ?? null),
        'booking_id_length' => isset($jsonInput['booking_id']) ? strlen($jsonInput['booking_id']) : 0,
        'booking_id_empty' => empty($jsonInput['booking_id'] ?? ''),
        'amount' => $jsonInput['amount'] ?? 'not set',
        'amount_type' => gettype($jsonInput['amount'] ?? null),
        'gateway' => $jsonInput['gateway'] ?? 'not set',
        'gateway_type' => gettype($jsonInput['gateway'] ?? null)
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
