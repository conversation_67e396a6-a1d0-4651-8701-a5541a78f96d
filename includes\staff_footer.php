                </div>
            </main>
        </div>
    </div>
</div>

<script>
    // Global utility functions
    function formatCurrency(amount) {
        return 'TSH ' + parseInt(amount).toLocaleString();
    }

    function formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    function formatTime(timeString) {
        return new Date('2000-01-01 ' + timeString).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    }

    // AJAX helper function
    function makeRequest(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };

        const config = { ...defaultOptions, ...options };

        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            });
    }

    // Auto-refresh data every 30 seconds for dashboard
    if (window.location.pathname === '/staff' || window.location.pathname === '/staff/') {
        setInterval(() => {
            // Refresh dashboard stats
            const basePath = '<?= getBasePath() ?>';
            makeRequest(`${basePath}/api/staff/dashboard-stats.php`)
                .then(data => {
                    // Update stats if elements exist
                    const elements = {
                        'today-appointments': data.today_appointments,
                        'today-earnings': formatCurrency(data.today_earnings),
                        'completion-rate': data.completion_rate + '%',
                        'monthly-bookings': data.monthly_bookings
                    };

                    Object.entries(elements).forEach(([id, value]) => {
                        const element = document.getElementById(id);
                        if (element) element.textContent = value;
                    });
                })
                .catch(() => {
                    // Silently fail for auto-refresh
                });
        }, 30000);
    }

    // Global notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

        const bgColor = {
            'success': 'bg-green-600',
            'error': 'bg-red-600',
            'warning': 'bg-yellow-600',
            'info': 'bg-blue-600'
        }[type] || 'bg-blue-600';

        notification.className += ` ${bgColor} text-white`;
        notification.innerHTML = `
            <div class="flex items-center">
                <span class="mr-2">${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }

    // Staff Notification System
    let notifications = [];
    let lastNotificationCount = 0;
    let currentFilter = 'all';

    function loadNotifications(category = 'all') {
        const basePath = '<?= getBasePath() ?>';
        const url = `${basePath}/api/staff/notifications.php${category !== 'all' ? `?category=${category}` : ''}`;

        console.log('🔄 Loading staff notifications from:', url);
        console.log('🔄 Current user role:', '<?= $_SESSION['user_role'] ?? 'not set' ?>');
        console.log('🔄 Current user ID:', '<?= $_SESSION['user_id'] ?? 'not set' ?>');

        makeRequest(url)
            .then(data => {
                console.log('✅ Staff notifications response:', data);
                if (data && data.data) {
                    notifications = data.data.notifications || [];
                    const currentCount = data.data.counts ? data.data.counts.unread : 0;

                    console.log('📊 Notifications loaded:', notifications.length, 'Unread:', currentCount);

                    // Initialize lastNotificationCount on first load
                    if (lastNotificationCount === 0) {
                        lastNotificationCount = currentCount;
                    }

                    updateNotificationCounter(currentCount);
                    updateCategoryCounts(data.data.counts ? data.data.counts.categories : {});
                    renderNotifications();
                } else {
                    console.warn('⚠️ Invalid response format:', data);
                    handleNotificationError('Invalid response format');
                }
            })
            .catch(error => {
                console.error('❌ Failed to load staff notifications:', error);
                console.error('❌ Error details:', error.message);
                handleNotificationError(error.message);
            });
    }

    function refreshNotifications() {
        const basePath = '<?= getBasePath() ?>';
        const url = `${basePath}/api/staff/notifications.php${currentFilter !== 'all' ? `?category=${currentFilter}` : ''}`;

        makeRequest(url)
            .then(data => {
                if (data && data.data) {
                    notifications = data.data.notifications || [];
                    const currentCount = data.data.counts ? data.data.counts.unread : 0;

                    // Check for new notifications
                    if (currentCount > lastNotificationCount) {
                        const newNotifications = currentCount - lastNotificationCount;
                        showHeaderToast(`You have ${newNotifications} new notification${newNotifications > 1 ? 's' : ''}`, 'info');

                        // Add visual indicator to notification button
                        const notificationButton = document.getElementById('notificationButton');
                        if (notificationButton) {
                            notificationButton.classList.add('animate-pulse');
                            setTimeout(() => {
                                notificationButton.classList.remove('animate-pulse');
                            }, 3000);
                        }
                    }

                    lastNotificationCount = currentCount;
                    updateNotificationCounter(currentCount);
                    updateCategoryCounts(data.data.counts ? data.data.counts.categories : {});
                    renderNotifications();
                } else {
                    console.warn('⚠️ Invalid response format:', data);
                }
            })
            .catch(error => {
                console.error('❌ Failed to refresh staff notifications:', error);
            });
    }

    function updateNotificationCounter(count) {
        const counter = document.getElementById('notificationCounter');
        if (counter) {
            if (count > 0) {
                counter.textContent = count > 99 ? '99+' : count;
                counter.classList.remove('hidden');
            } else {
                counter.classList.add('hidden');
            }
        }
    }

    function updateCategoryCounts(categories) {
        // Update category filter buttons with counts
        Object.keys(categories).forEach(category => {
            const button = document.querySelector(`[data-category="${category}"]`);
            if (button && categories[category].unread > 0) {
                const existingBadge = button.querySelector('.category-badge');
                if (existingBadge) {
                    existingBadge.textContent = categories[category].unread;
                } else {
                    const badge = document.createElement('span');
                    badge.className = 'category-badge ml-1 bg-red-500 text-white text-xs rounded-full px-1';
                    badge.textContent = categories[category].unread;
                    button.appendChild(badge);
                }
            }
        });
    }

    function renderNotifications() {
        const container = document.getElementById('notificationsList');
        if (!container) return;

        if (notifications.length === 0) {
            container.innerHTML = `
                <div class="p-4 text-center text-gray-400">
                    <svg class="mx-auto h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-3.5-3.5a1.5 1.5 0 010-2.12L20 8h-5V6a3 3 0 00-6 0v2H4l3.5 3.38a1.5 1.5 0 010 2.12L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                    </svg>
                    <p>No notifications</p>
                </div>
            `;
            return;
        }

        const notificationHTML = notifications.map(notification => {
            const categoryInfo = getCategoryInfo(notification.category);
            const priorityClass = getPriorityClass(notification.priority);
            const isAdminTriggered = notification.metadata && notification.metadata.admin_triggered;

            return `
                <div class="notification-item p-3 border-b border-secondary-700 hover:bg-secondary-700 cursor-pointer transition-colors ${notification.is_read ? 'opacity-60' : ''}"
                     onclick="handleNotificationClick('${notification.id}', '${notification.action_url || ''}')">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 relative">
                            <div class="w-8 h-8 rounded-full ${categoryInfo.bgColor} flex items-center justify-center">
                                ${categoryInfo.icon}
                            </div>
                            ${isAdminTriggered ? '<div class="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full border border-secondary-800" title="Admin Action"></div>' : ''}
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <p class="text-sm font-medium text-white truncate">${notification.title}</p>
                                    ${isAdminTriggered ? '<span class="text-xs bg-yellow-500 text-black px-1 rounded">ADMIN</span>' : ''}
                                </div>
                                <div class="flex items-center space-x-1">
                                    ${notification.priority !== 'MEDIUM' ? `<span class="w-2 h-2 rounded-full ${priorityClass}"></span>` : ''}
                                    <span class="text-xs text-gray-400">${notification.time_ago}</span>
                                </div>
                            </div>
                            <p class="text-sm text-gray-300 mt-1">${notification.message}</p>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = notificationHTML;
    }

    function getCategoryInfo(category) {
        const categoryMap = {
            'BOOKING': {
                icon: '<svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>',
                bgColor: 'bg-blue-600'
            },
            'STAFF': {
                icon: '<svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg>',
                bgColor: 'bg-purple-600'
            },
            'SYSTEM': {
                icon: '<svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>',
                bgColor: 'bg-gray-600'
            }
        };

        return categoryMap[category] || categoryMap['SYSTEM'];
    }

    function getPriorityClass(priority) {
        const priorityMap = {
            'URGENT': 'bg-red-500',
            'HIGH': 'bg-orange-500',
            'MEDIUM': 'bg-yellow-500',
            'LOW': 'bg-green-500'
        };

        return priorityMap[priority] || priorityMap['MEDIUM'];
    }

    // Global loading state
    function showLoading() {
        const loading = document.createElement('div');
        loading.id = 'global-loading';
        loading.className = 'fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center';
        loading.innerHTML = `
            <div class="bg-secondary-800 rounded-lg p-6 flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-salon-gold"></div>
                <span class="text-white">Loading...</span>
            </div>
        `;
        document.body.appendChild(loading);
    }

    function hideLoading() {
        const loading = document.getElementById('global-loading');
        if (loading) {
            loading.remove();
        }
    }

    function handleNotificationClick(notificationId, actionUrl) {
        // Mark notification as read
        markNotificationAsRead(notificationId);

        // Navigate to action URL if provided
        if (actionUrl && actionUrl !== 'null') {
            const basePath = '<?= getBasePath() ?>';
            window.location.href = basePath + actionUrl;
        }
    }

    function markNotificationAsRead(notificationId) {
        const basePath = '<?= getBasePath() ?>';

        makeRequest(`${basePath}/api/staff/notifications.php`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                id: notificationId,
                is_read: true
            })
        })
        .then(() => {
            // Update local notification state
            const notification = notifications.find(n => n.id === notificationId);
            if (notification) {
                notification.is_read = true;
            }

            // Refresh notifications to update counter
            refreshNotifications();
        })
        .catch(error => {
            console.error('Failed to mark notification as read:', error);
        });
    }

    function handleNotificationError(message) {
        const container = document.getElementById('notificationsList');
        if (container) {
            container.innerHTML = `
                <div class="p-4 text-center text-red-400">
                    <svg class="mx-auto h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p>Error loading notifications</p>
                    <p class="text-xs mt-1">${message}</p>
                </div>
            `;
        }
    }

    function showHeaderToast(message, type = 'info') {
        showNotification(message, type);
    }

    // Initialize notification system when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Set up category filter buttons
        document.querySelectorAll('.notification-filter-btn').forEach(button => {
            button.addEventListener('click', function() {
                const category = this.dataset.category;
                currentFilter = category;

                // Update active state
                document.querySelectorAll('.notification-filter-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                this.classList.add('active');

                // Load notifications for selected category
                loadNotifications(category);
            });
        });

        // Set up mark all read button
        const markAllReadBtn = document.getElementById('markAllReadBtn');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', function() {
                markAllNotificationsAsRead();
            });
        }

        // Set up clear all button
        const clearAllBtn = document.getElementById('clearAllBtn');
        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', function() {
                clearAllNotifications();
            });
        }

        // Auto-refresh notifications every 30 seconds for real-time updates
        setInterval(refreshNotifications, 30000);

        // Initial load
        loadNotifications();
    });

    function markAllNotificationsAsRead() {
        const unreadNotifications = notifications.filter(n => !n.is_read);

        if (unreadNotifications.length === 0) {
            showNotification('No unread notifications', 'info');
            return;
        }

        const basePath = '<?= getBasePath() ?>';

        Promise.all(unreadNotifications.map(notification =>
            makeRequest(`${basePath}/api/staff/notifications.php`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id: notification.id,
                    is_read: true
                })
            })
        ))
        .then(() => {
            showNotification('All notifications marked as read', 'success');
            // Reset the counter and update UI
            lastNotificationCount = 0;
            updateNotificationCounter(0);
            refreshNotifications();
        })
        .catch(error => {
            console.error('Failed to mark all notifications as read:', error);
            showNotification('Failed to mark notifications as read', 'error');
        });
    }

    function clearAllNotifications() {
        if (notifications.length === 0) {
            showNotification('No notifications to clear', 'info');
            return;
        }

        if (!confirm('Are you sure you want to delete all notifications? This action cannot be undone.')) {
            return;
        }

        const basePath = '<?= getBasePath() ?>';

        Promise.all(notifications.map(notification =>
            makeRequest(`${basePath}/api/staff/notifications.php?id=${notification.id}`, {
                method: 'DELETE'
            })
        ))
        .then(() => {
            showNotification('All notifications cleared', 'success');
            // Reset the counter and update UI
            lastNotificationCount = 0;
            updateNotificationCounter(0);
            notifications = [];
            renderNotifications();
        })
        .catch(error => {
            console.error('Failed to clear notifications:', error);
            showNotification('Failed to clear notifications', 'error');
        });
    }

    // Form submission with loading state
    function submitFormWithLoading(form) {
        showLoading();
        form.submit();
    }

    // Auto-save functionality for forms
    function enableAutoSave(formId, saveUrl) {
        const form = document.getElementById(formId);
        if (!form) return;

        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                const formData = new FormData(form);
                
                fetch(saveUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Changes saved automatically', 'success');
                    }
                })
                .catch(error => {
                    console.error('Auto-save error:', error);
                });
            });
        });
    }
</script>

</body>
</html>
