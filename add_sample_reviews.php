<?php
/**
 * Add Sample Reviews with Real Customer Data
 * This script adds realistic sample reviews using existing customer and service data
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/reviews_functions.php';

echo "<h1>Adding Sample Reviews</h1>\n";

try {
    // Get existing customers
    $customers = $database->fetchAll("SELECT id, name FROM users WHERE role = 'CUSTOMER' LIMIT 15");
    
    // Get existing services
    $services = $database->fetchAll("SELECT id, name FROM services WHERE is_active = 1 LIMIT 10");
    
    // Get existing packages
    $packages = $database->fetchAll("SELECT id, name FROM packages WHERE is_active = 1 LIMIT 5");
    
    if (empty($customers)) {
        echo "<p style='color: red;'>No customers found in database. Please add customers first.</p>\n";
        exit;
    }
    
    if (empty($services) && empty($packages)) {
        echo "<p style='color: red;'>No services or packages found in database. Please add services/packages first.</p>\n";
        exit;
    }
    
    echo "<p>Found " . count($customers) . " customers, " . count($services) . " services, and " . count($packages) . " packages.</p>\n";
    
    // Sample review data with authentic African/Islamic names and realistic content
    $sampleReviews = [
        [
            'title' => 'Absolutely Amazing Hair Transformation!',
            'comment' => 'Amina did an incredible job with my hair color and cut. The balayage technique she used was flawless and the color blends perfectly with my skin tone. The salon atmosphere is so relaxing and professional. I felt pampered from start to finish. Will definitely be returning!',
            'rating' => 5,
            'is_verified' => true,
            'is_featured' => true,
            'type' => 'service'
        ],
        [
            'title' => 'Perfect Bridal Experience',
            'comment' => 'My bridal package was everything I dreamed of! The team made me feel like a queen on my wedding day. The makeup was stunning and lasted all day, the hair styling was elegant and stayed perfect through all the celebrations. Every detail was handled with care and professionalism.',
            'rating' => 5,
            'is_verified' => true,
            'is_featured' => true,
            'type' => 'package'
        ],
        [
            'title' => 'Best Facial Treatment Ever!',
            'comment' => 'The facial treatment was the most relaxing and effective I have ever experienced. My skin is glowing and feels so smooth! The staff explained each step and recommended the perfect products for my skin type. The ambiance was perfect for relaxation.',
            'rating' => 5,
            'is_verified' => true,
            'is_featured' => false,
            'type' => 'service'
        ],
        [
            'title' => 'Gorgeous Nails and Excellent Service',
            'comment' => 'The nail technician did an amazing job on my manicure and pedicure. The nail art was intricate and beautiful, exactly what I requested. The attention to detail was impressive and the results lasted for weeks. The salon is clean and the staff is very friendly.',
            'rating' => 5,
            'is_verified' => true,
            'is_featured' => false,
            'type' => 'service'
        ],
        [
            'title' => 'Stunning Hair Color Results',
            'comment' => 'I am so happy with my new hair color! The stylist understood exactly what I wanted and delivered beyond my expectations. The highlights look natural and add so much dimension to my hair. The whole experience was wonderful and relaxing.',
            'rating' => 5,
            'is_verified' => true,
            'is_featured' => true,
            'type' => 'service'
        ],
        [
            'title' => 'Great Massage Experience',
            'comment' => 'The massage therapy session was excellent. I felt so relaxed and my muscle tension was completely relieved. The only reason I am giving 4 stars instead of 5 is that I wish the session was a bit longer. Overall, very satisfied with the service.',
            'rating' => 4,
            'is_verified' => true,
            'is_featured' => false,
            'type' => 'service'
        ],
        [
            'title' => 'Professional Hair Cut',
            'comment' => 'The stylist gave me a beautiful haircut that suits my face shape perfectly. The styling was professional and the salon environment is lovely. The service was good, though I had to wait a bit longer than expected. Would recommend and will return.',
            'rating' => 4,
            'is_verified' => true,
            'is_featured' => false,
            'type' => 'service'
        ],
        [
            'title' => 'Lovely Spa Package',
            'comment' => 'The relaxation package was wonderful! The staff took great care of me and the treatments were very relaxing. The facial and massage combination was perfect for stress relief. The only improvement would be to have softer background music. Overall, a great experience.',
            'rating' => 4,
            'is_verified' => true,
            'is_featured' => false,
            'type' => 'package'
        ],
        [
            'title' => 'Flawless Makeup Application',
            'comment' => 'The makeup artist is a true artist! The makeup for my graduation photos was absolutely perfect. She enhanced my natural features beautifully and the makeup lasted all day. Professional, friendly, and incredibly talented. I will definitely book again for special events.',
            'rating' => 5,
            'is_verified' => true,
            'is_featured' => false,
            'type' => 'service'
        ],
        [
            'title' => 'Rejuvenating Facial Treatment',
            'comment' => 'The facial experience was the most relaxing I have had. My skin feels refreshed and looks radiant. The esthetician was knowledgeable about different skin types and customized the treatment perfectly for my needs. The products used were high quality and effective.',
            'rating' => 5,
            'is_verified' => true,
            'is_featured' => false,
            'type' => 'service'
        ],
        [
            'title' => 'Good Service with Room for Improvement',
            'comment' => 'The manicure was decent and the staff was polite. However, the nail polish chipped after just a few days, which was disappointing. The salon is clean and well-maintained. With some improvements in product quality, this could be a 5-star experience.',
            'rating' => 3,
            'is_verified' => true,
            'is_featured' => false,
            'type' => 'service'
        ],
        [
            'title' => 'Exceptional Hair Styling',
            'comment' => 'The stylist created the most beautiful hairstyle for my sister\'s wedding. The updo was elegant and sophisticated, and it stayed perfect throughout the entire event. Her attention to detail and artistic skills are outstanding. Highly recommend!',
            'rating' => 5,
            'is_verified' => true,
            'is_featured' => true,
            'type' => 'service'
        ]
    ];
    
    $addedReviews = 0;
    $skippedReviews = 0;
    
    foreach ($sampleReviews as $index => $reviewData) {
        try {
            // Get random customer
            $customer = $customers[array_rand($customers)];
            
            // Get random service or package based on type
            if ($reviewData['type'] === 'service' && !empty($services)) {
                $service = $services[array_rand($services)];
                $serviceId = $service['id'];
                $packageId = null;
            } elseif ($reviewData['type'] === 'package' && !empty($packages)) {
                $package = $packages[array_rand($packages)];
                $serviceId = null;
                $packageId = $package['id'];
            } else {
                // Fallback to service if package not available
                if (!empty($services)) {
                    $service = $services[array_rand($services)];
                    $serviceId = $service['id'];
                    $packageId = null;
                } else {
                    continue;
                }
            }
            
            // Check if customer already has a review for this service/package
            $existingReview = getCustomerReview($customer['id'], $serviceId, $packageId);
            if ($existingReview) {
                echo "⚠ Skipped review for customer {$customer['name']} - already has review for this service/package<br>\n";
                $skippedReviews++;
                continue;
            }
            
            // Create review data
            $newReviewData = [
                'customer_id' => $customer['id'],
                'service_id' => $serviceId,
                'package_id' => $packageId,
                'rating' => $reviewData['rating'],
                'title' => $reviewData['title'],
                'comment' => $reviewData['comment'],
                'is_verified' => $reviewData['is_verified']
            ];
            
            // Create the review
            $reviewId = createReview($newReviewData);
            
            if ($reviewId) {
                // Update status and featured flag
                $database->execute(
                    "UPDATE reviews SET status = 'verified', is_featured = ? WHERE id = ?",
                    [$reviewData['is_featured'] ? 1 : 0, $reviewId]
                );
                
                echo "✓ Added review: '{$reviewData['title']}' by {$customer['name']}<br>\n";
                $addedReviews++;
            } else {
                echo "✗ Failed to add review: '{$reviewData['title']}'<br>\n";
            }
            
        } catch (Exception $e) {
            echo "✗ Error adding review: " . $e->getMessage() . "<br>\n";
        }
    }
    
    echo "<h2>Summary</h2>\n";
    echo "<p style='color: green;'>✓ Successfully added {$addedReviews} reviews</p>\n";
    if ($skippedReviews > 0) {
        echo "<p style='color: orange;'>⚠ Skipped {$skippedReviews} reviews (duplicates)</p>\n";
    }
    
    // Show final statistics
    $stats = getReviewStats();
    echo "<h3>Current Review Statistics:</h3>\n";
    echo "<ul>\n";
    echo "<li>Total Reviews: " . ($stats['total_reviews'] ?? 0) . "</li>\n";
    echo "<li>Average Rating: " . ($stats['average_rating'] ?? '0.0') . "</li>\n";
    echo "<li>Verified Reviews: " . ($stats['verified_reviews'] ?? 0) . "</li>\n";
    echo "<li>Recommendation Rate: " . ($stats['recommendation_rate'] ?? 0) . "%</li>\n";
    echo "</ul>\n";
    
    echo "<p><a href='reviews.php'>View Reviews Page →</a></p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>\n";
}

echo "<hr>\n";
echo "<p><a href='index.php'>← Back to Home</a> | <a href='admin/reviews.php'>Admin Reviews →</a></p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

p, li {
    line-height: 1.6;
}

a {
    color: #f59e0b;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul, ol {
    padding-left: 20px;
}
</style>
