<?php
/**
 * Admin Send Message to Customer
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get customer ID from URL
$customerId = $_GET['id'] ?? '';
if (empty($customerId)) {
    $_SESSION['error'] = 'Customer ID is required';
    redirect('/admin/customers');
}

// Get customer details
$customer = $database->fetch(
    "SELECT * FROM users WHERE id = ? AND role = 'CUSTOMER'",
    [$customerId]
);

if (!$customer) {
    $_SESSION['error'] = 'Customer not found';
    redirect('/admin/customers');
}

// Get recent messages sent to this customer
$recentMessages = [];
try {
    $recentMessages = $database->fetchAll(
        "SELECT cm.*, u.name as admin_name
         FROM customer_messages cm
         LEFT JOIN users u ON cm.admin_id = u.id
         WHERE cm.customer_id = ?
         ORDER BY cm.created_at DESC
         LIMIT 10",
        [$customerId]
    );
} catch (Exception $e) {
    // Table might not exist yet
    error_log("Customer messages table error: " . $e->getMessage());
    $recentMessages = [];
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $result = sendCustomerMessage($customerId, $_POST);

        if ($result['success']) {
            $messageType = $_POST['message_type'] ?? 'email';
            $successMessage = 'Message sent successfully!';

            if ($messageType === 'email') {
                $successMessage .= ' The customer will receive an email notification.';
            } elseif ($messageType === 'sms') {
                $successMessage .= ' SMS notification sent to customer.';
            } elseif ($messageType === 'both') {
                $successMessage .= ' Both email and SMS notifications sent to customer.';
            }

            $_SESSION['success'] = $successMessage;
            redirect('/admin/customers/view.php?id=' . $customerId);
        } else {
            $_SESSION['error'] = $result['error'] ?? 'Failed to send message. Please try again.';
        }
    } catch (Exception $e) {
        error_log("Customer message error: " . $e->getMessage());
        $_SESSION['error'] = 'An error occurred while sending the message. Please try again.';
    }
}

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Send Message - " . $customer['name'];
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>
            
            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-12 w-12">
                                    <div class="h-12 w-12 rounded-full bg-salon-gold flex items-center justify-center">
                                        <span class="text-lg font-medium text-black">
                                            <?= strtoupper(substr($customer['name'], 0, 2)) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h1 class="text-2xl font-bold text-white">Send Message</h1>
                                    <p class="mt-1 text-sm text-gray-300">To: <?= htmlspecialchars($customer['name']) ?> (<?= htmlspecialchars($customer['email']) ?>)</p>
                                </div>
                            </div>
                            <div class="mt-4 sm:mt-0 flex gap-2">
                                <a href="<?= getBasePath() ?>/admin/customers/view.php?id=<?= $customer['id'] ?>" 
                                   class="bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                    Back to Customer
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Message Form -->
                        <div class="lg:col-span-2">
                            <div class="bg-secondary-800 shadow rounded-lg p-6">
                                <form method="POST" id="messageForm">
                                    <!-- Message Type -->
                                    <div class="mb-6">
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Message Type</label>
                                        <select name="message_type" id="messageType" required 
                                                class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                            <option value="email">Email</option>
                                            <option value="sms">SMS (if phone available)</option>
                                            <option value="both">Both Email & SMS</option>
                                        </select>
                                    </div>

                                    <!-- Subject (for email) -->
                                    <div class="mb-6" id="subjectField">
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Subject</label>
                                        <input type="text" name="subject" id="messageSubject" 
                                               class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                               placeholder="Enter email subject">
                                    </div>

                                    <!-- Message Templates -->
                                    <div class="mb-6">
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Quick Templates</label>
                                        <select id="messageTemplate" 
                                                class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                            <option value="">Select a template...</option>
                                            <option value="appointment_reminder">Appointment Reminder</option>
                                            <option value="thank_you">Thank You Message</option>
                                            <option value="promotion">Special Promotion</option>
                                            <option value="birthday">Birthday Wishes</option>
                                            <option value="feedback">Feedback Request</option>
                                            <option value="custom">Custom Message</option>
                                        </select>
                                    </div>

                                    <!-- Message Content -->
                                    <div class="mb-6">
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Message</label>
                                        <textarea name="message" id="messageContent" rows="8" required 
                                                  class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                                  placeholder="Type your message here..."></textarea>
                                        <p class="text-xs text-gray-400 mt-1">Available variables: {customer_name}, {salon_name}, {points_balance}</p>
                                    </div>

                                    <!-- Send Options -->
                                    <div class="mb-6">
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Send Options</label>
                                        <div class="space-y-2">
                                            <div class="flex items-center">
                                                <input type="checkbox" name="send_immediately" id="sendImmediately" value="1" checked
                                                       class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold">
                                                <span class="ml-2 text-sm text-gray-300">Send immediately</span>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="checkbox" name="save_template" id="saveTemplate" value="1"
                                                       class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold">
                                                <span class="ml-2 text-sm text-gray-300">Save as template for future use</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Submit Button -->
                                    <div class="flex gap-4">
                                        <button type="submit" 
                                                class="flex-1 bg-salon-gold text-black py-3 px-6 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                            Send Message
                                        </button>
                                        <button type="button" onclick="previewMessage()" 
                                                class="bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                            Preview
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Customer Info & Message History -->
                        <div>
                            <!-- Customer Info -->
                            <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                                <h3 class="text-lg font-semibold text-white mb-4">Customer Information</h3>
                                
                                <div class="space-y-3">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-1">Name</h4>
                                        <p class="text-white"><?= htmlspecialchars($customer['name']) ?></p>
                                    </div>
                                    
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-1">Email</h4>
                                        <p class="text-white"><?= htmlspecialchars($customer['email']) ?></p>
                                    </div>
                                    
                                    <?php if ($customer['phone']): ?>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-1">Phone</h4>
                                        <p class="text-white"><?= htmlspecialchars($customer['phone']) ?></p>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-1">Loyalty Points</h4>
                                        <p class="text-purple-400 font-medium"><?= number_format($customer['points']) ?></p>
                                    </div>
                                    
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-1">Member Since</h4>
                                        <p class="text-white"><?= date('M j, Y', strtotime($customer['created_at'])) ?></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Message Tips -->
                            <div class="bg-secondary-800 shadow rounded-lg p-6">
                                <h3 class="text-lg font-semibold text-white mb-4">Message Tips</h3>
                                
                                <div class="space-y-3 text-sm text-gray-300">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 w-2 h-2 bg-salon-gold rounded-full mt-2 mr-3"></div>
                                        <p>Keep messages personal and relevant to the customer</p>
                                    </div>
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 w-2 h-2 bg-salon-gold rounded-full mt-2 mr-3"></div>
                                        <p>Use variables like {customer_name} for personalization</p>
                                    </div>
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 w-2 h-2 bg-salon-gold rounded-full mt-2 mr-3"></div>
                                        <p>Include clear call-to-action when appropriate</p>
                                    </div>
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 w-2 h-2 bg-salon-gold rounded-full mt-2 mr-3"></div>
                                        <p>Keep SMS messages under 160 characters</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Messages Section -->
                <?php if (count($recentMessages) > 0): ?>
                <div class="mt-8">
                    <div class="bg-secondary-800 shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-secondary-700">
                            <h3 class="text-lg font-semibold text-white">Recent Messages</h3>
                            <p class="text-sm text-gray-300">Messages sent to <?= htmlspecialchars($customer['name']) ?></p>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <?php foreach ($recentMessages as $message): ?>
                                <div class="border border-secondary-700 rounded-lg p-4 hover:bg-secondary-700 transition-colors">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-3 mb-2">
                                                <h4 class="font-medium text-white">
                                                    <?= htmlspecialchars($message['subject'] ?: 'No Subject') ?>
                                                </h4>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                    <?php if ($message['status'] === 'SENT'): ?>
                                                        bg-green-100 text-green-800
                                                    <?php elseif ($message['status'] === 'FAILED'): ?>
                                                        bg-red-100 text-red-800
                                                    <?php elseif ($message['status'] === 'PENDING'): ?>
                                                        bg-yellow-100 text-yellow-800
                                                    <?php else: ?>
                                                        bg-gray-100 text-gray-800
                                                    <?php endif; ?>">
                                                    <?= $message['status'] ?>
                                                </span>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-salon-gold text-secondary-900">
                                                    <?= strtoupper($message['message_type']) ?>
                                                </span>
                                            </div>
                                            <p class="text-sm text-gray-300 mb-2 line-clamp-2">
                                                <?= htmlspecialchars(substr($message['message'], 0, 150)) ?><?= strlen($message['message']) > 150 ? '...' : '' ?>
                                            </p>
                                            <div class="flex items-center gap-4 text-xs text-gray-400">
                                                <span>Sent by: <?= htmlspecialchars($message['admin_name'] ?: 'Unknown') ?></span>
                                                <span>•</span>
                                                <span><?= date('M j, Y \a\t g:i A', strtotime($message['created_at'])) ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const messageType = document.getElementById('messageType');
    const subjectField = document.getElementById('subjectField');
    const messageTemplate = document.getElementById('messageTemplate');
    const messageContent = document.getElementById('messageContent');
    const messageSubject = document.getElementById('messageSubject');

    // Show/hide subject field based on message type
    messageType.addEventListener('change', function() {
        if (this.value === 'sms') {
            subjectField.style.display = 'none';
            messageSubject.required = false;
        } else {
            subjectField.style.display = 'block';
            messageSubject.required = true;
        }
    });

    // Message templates
    const templates = {
        appointment_reminder: {
            subject: 'Appointment Reminder - Flix Salonce',
            message: 'Hi {customer_name},\n\nThis is a friendly reminder about your upcoming appointment at Flix Salonce.\n\nWe look forward to seeing you!\n\nBest regards,\nFlix Salonce Team'
        },
        thank_you: {
            subject: 'Thank You - Flix Salonce',
            message: 'Dear {customer_name},\n\nThank you for choosing Flix Salonce! We hope you loved your recent visit.\n\nYour current loyalty points balance: {points_balance}\n\nWe look forward to seeing you again soon!\n\nBest regards,\nFlix Salonce Team'
        },
        promotion: {
            subject: 'Special Offer Just for You!',
            message: 'Hi {customer_name},\n\nWe have a special promotion just for you at Flix Salonce!\n\n[Add promotion details here]\n\nBook your appointment today!\n\nBest regards,\nFlix Salonce Team'
        },
        birthday: {
            subject: 'Happy Birthday from Flix Salonce!',
            message: 'Happy Birthday {customer_name}! 🎉\n\nWishing you a wonderful day filled with joy and beauty.\n\nAs a birthday gift, enjoy [special offer] on your next visit!\n\nCelebrate with us at Flix Salonce!\n\nBest wishes,\nFlix Salonce Team'
        },
        feedback: {
            subject: 'We Value Your Feedback - Flix Salonce',
            message: 'Dear {customer_name},\n\nWe hope you enjoyed your recent visit to Flix Salonce.\n\nYour feedback is important to us. Please take a moment to share your experience.\n\nThank you for helping us improve!\n\nBest regards,\nFlix Salonce Team'
        }
    };

    messageTemplate.addEventListener('change', function() {
        const template = templates[this.value];
        if (template) {
            messageSubject.value = template.subject;
            messageContent.value = template.message;
        } else if (this.value === 'custom') {
            messageSubject.value = '';
            messageContent.value = '';
        }
    });
});

function previewMessage() {
    const messageContent = document.getElementById('messageContent').value;
    const customerName = '<?= htmlspecialchars($customer['name']) ?>';
    const pointsBalance = '<?= number_format($customer['points']) ?>';
    
    let preview = messageContent
        .replace(/{customer_name}/g, customerName)
        .replace(/{salon_name}/g, 'Flix Salonce')
        .replace(/{points_balance}/g, pointsBalance);
    
    alert('Message Preview:\n\n' + preview);
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
