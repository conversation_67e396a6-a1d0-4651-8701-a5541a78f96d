<?php
/**
 * Admin Advanced Booking Search
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get search parameters
$filters = [
    'customer_name' => sanitize($_GET['customer_name'] ?? ''),
    'customer_email' => sanitize($_GET['customer_email'] ?? ''),
    'service_id' => sanitize($_GET['service_id'] ?? ''),
    'staff_id' => sanitize($_GET['staff_id'] ?? ''),
    'status' => sanitize($_GET['status'] ?? ''),
    'date_from' => sanitize($_GET['date_from'] ?? ''),
    'date_to' => sanitize($_GET['date_to'] ?? ''),
    'amount_min' => sanitize($_GET['amount_min'] ?? ''),
    'amount_max' => sanitize($_GET['amount_max'] ?? ''),
    'points_used' => sanitize($_GET['points_used'] ?? ''),
    'created_from' => sanitize($_GET['created_from'] ?? ''),
    'created_to' => sanitize($_GET['created_to'] ?? ''),
];

// Build search query
$whereClause = "WHERE 1=1";
$params = [];

if ($filters['customer_name']) {
    $whereClause .= " AND u.name LIKE ?";
    $params[] = "%{$filters['customer_name']}%";
}

if ($filters['customer_email']) {
    $whereClause .= " AND u.email LIKE ?";
    $params[] = "%{$filters['customer_email']}%";
}

if ($filters['service_id']) {
    $whereClause .= " AND b.service_id = ?";
    $params[] = $filters['service_id'];
}

if ($filters['staff_id']) {
    $whereClause .= " AND b.staff_id = ?";
    $params[] = $filters['staff_id'];
}

if ($filters['status']) {
    $whereClause .= " AND b.status = ?";
    $params[] = $filters['status'];
}

if ($filters['date_from']) {
    $whereClause .= " AND b.date >= ?";
    $params[] = $filters['date_from'];
}

if ($filters['date_to']) {
    $whereClause .= " AND b.date <= ?";
    $params[] = $filters['date_to'];
}

if ($filters['amount_min']) {
    $whereClause .= " AND b.total_amount >= ?";
    $params[] = floatval($filters['amount_min']);
}

if ($filters['amount_max']) {
    $whereClause .= " AND b.total_amount <= ?";
    $params[] = floatval($filters['amount_max']);
}

if ($filters['points_used']) {
    $whereClause .= " AND b.points_used > 0";
}

if ($filters['created_from']) {
    $whereClause .= " AND DATE(b.created_at) >= ?";
    $params[] = $filters['created_from'];
}

if ($filters['created_to']) {
    $whereClause .= " AND DATE(b.created_at) <= ?";
    $params[] = $filters['created_to'];
}

// Get search results
$bookings = [];
$totalResults = 0;

if (array_filter($filters)) {
    $bookings = $database->fetchAll(
        "SELECT b.*, u.name as customer_name, u.email as customer_email, u.phone as customer_phone,
                s.name as service_name, s.price as service_price, s.duration as service_duration,
                st.name as staff_name, st.email as staff_email
         FROM bookings b
         LEFT JOIN users u ON b.user_id = u.id
         LEFT JOIN services s ON b.service_id = s.id
         LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
         $whereClause
         GROUP BY b.id
         ORDER BY b.date DESC, b.start_time DESC
         LIMIT 100",
        $params
    );
    
    $totalResults = count($bookings);
}

// Get services and staff for filters
$services = $database->fetchAll("SELECT id, name FROM services WHERE is_active = 1 ORDER BY name");
$staff = $database->fetchAll("SELECT id, name FROM users WHERE role = 'STAFF' ORDER BY name");

$pageTitle = "Advanced Search";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>
            
            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-white">Advanced Booking Search</h1>
                                <p class="mt-1 text-sm text-gray-300">Search bookings with detailed filters</p>
                            </div>
                            <div class="mt-4 sm:mt-0">
                                <a href="<?= getBasePath() ?>/admin/bookings" 
                                   class="bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                    Back to Bookings
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Search Form -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <form method="GET" class="space-y-6">
                            <!-- Customer Information -->
                            <div>
                                <h3 class="text-lg font-medium text-white mb-4">Customer Information</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Customer Name</label>
                                        <input type="text" name="customer_name" value="<?= htmlspecialchars($filters['customer_name']) ?>" 
                                               placeholder="Search by customer name..."
                                               class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Customer Email</label>
                                        <input type="email" name="customer_email" value="<?= htmlspecialchars($filters['customer_email']) ?>" 
                                               placeholder="Search by email..."
                                               class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                    </div>
                                </div>
                            </div>

                            <!-- Service and Staff -->
                            <div>
                                <h3 class="text-lg font-medium text-white mb-4">Service & Staff</h3>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Service</label>
                                        <select name="service_id" class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                            <option value="">All Services</option>
                                            <?php foreach ($services as $service): ?>
                                                <option value="<?= $service['id'] ?>" <?= $filters['service_id'] == $service['id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($service['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Staff Member</label>
                                        <select name="staff_id" class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                            <option value="">All Staff</option>
                                            <?php foreach ($staff as $staffMember): ?>
                                                <option value="<?= $staffMember['id'] ?>" <?= $filters['staff_id'] == $staffMember['id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($staffMember['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                                        <select name="status" class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                            <option value="">All Status</option>
                                            <option value="PENDING" <?= $filters['status'] === 'PENDING' ? 'selected' : '' ?>>Pending</option>
                                            <option value="CONFIRMED" <?= $filters['status'] === 'CONFIRMED' ? 'selected' : '' ?>>Confirmed</option>
                                            <option value="COMPLETED" <?= $filters['status'] === 'COMPLETED' ? 'selected' : '' ?>>Completed</option>
                                            <option value="CANCELLED" <?= $filters['status'] === 'CANCELLED' ? 'selected' : '' ?>>Cancelled</option>
                                            <option value="NO_SHOW" <?= $filters['status'] === 'NO_SHOW' ? 'selected' : '' ?>>No Show</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Date Ranges -->
                            <div>
                                <h3 class="text-lg font-medium text-white mb-4">Date Ranges</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-2">Booking Date</h4>
                                        <div class="grid grid-cols-2 gap-2">
                                            <input type="date" name="date_from" value="<?= htmlspecialchars($filters['date_from']) ?>" 
                                                   placeholder="From"
                                                   class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                            <input type="date" name="date_to" value="<?= htmlspecialchars($filters['date_to']) ?>" 
                                                   placeholder="To"
                                                   class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                        </div>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-2">Created Date</h4>
                                        <div class="grid grid-cols-2 gap-2">
                                            <input type="date" name="created_from" value="<?= htmlspecialchars($filters['created_from']) ?>" 
                                                   placeholder="From"
                                                   class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                            <input type="date" name="created_to" value="<?= htmlspecialchars($filters['created_to']) ?>" 
                                                   placeholder="To"
                                                   class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Amount and Points -->
                            <div>
                                <h3 class="text-lg font-medium text-white mb-4">Amount & Points</h3>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Min Amount</label>
                                        <input type="number" name="amount_min" value="<?= htmlspecialchars($filters['amount_min']) ?>" 
                                               step="0.01" placeholder="0.00"
                                               class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Max Amount</label>
                                        <input type="number" name="amount_max" value="<?= htmlspecialchars($filters['amount_max']) ?>" 
                                               step="0.01" placeholder="999.99"
                                               class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Points Used</label>
                                        <div class="flex items-center">
                                            <input type="checkbox" name="points_used" value="1" <?= $filters['points_used'] ? 'checked' : '' ?>
                                                   class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold">
                                            <span class="ml-2 text-sm text-gray-300">Only bookings with points used</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-4">
                                <button type="submit" class="bg-salon-gold text-black px-6 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    Search Bookings
                                </button>
                                <a href="<?= getBasePath() ?>/admin/bookings/advanced-search.php" 
                                   class="bg-gray-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                    Clear Filters
                                </a>
                            </div>
                        </form>
                    </div>

                    <!-- Search Results -->
                    <?php if (array_filter($filters)): ?>
                        <div class="bg-secondary-800 shadow rounded-lg p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold text-white">Search Results</h3>
                                <span class="text-sm text-gray-300"><?= $totalResults ?> booking(s) found</span>
                            </div>

                            <?php if ($totalResults > 0): ?>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-secondary-700">
                                        <thead class="bg-secondary-700">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Customer</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Service</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Staff</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Amount</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-secondary-800 divide-y divide-secondary-700">
                                            <?php foreach ($bookings as $booking): ?>
                                                <tr class="hover:bg-secondary-700">
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm font-medium text-white"><?= htmlspecialchars($booking['customer_name']) ?></div>
                                                        <div class="text-sm text-gray-300"><?= htmlspecialchars($booking['customer_email']) ?></div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm text-white"><?= htmlspecialchars($booking['service_name']) ?></div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm text-white"><?= htmlspecialchars($booking['staff_name'] ?? 'Unassigned') ?></div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm text-white"><?= date('M j, Y', strtotime($booking['date'])) ?></div>
                                                        <div class="text-sm text-gray-300"><?= date('g:i A', strtotime($booking['start_time'])) ?></div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm font-medium text-salon-gold"><?= formatCurrency($booking['total_amount']) ?></div>
                                                        <?php if ($booking['points_used'] > 0): ?>
                                                            <div class="text-xs text-gray-400"><?= $booking['points_used'] ?> points used</div>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                            <?php
                                                            switch ($booking['status']) {
                                                                case 'PENDING':
                                                                    echo 'bg-yellow-100 text-yellow-800';
                                                                    break;
                                                                case 'CONFIRMED':
                                                                    echo 'bg-blue-100 text-blue-800';
                                                                    break;
                                                                case 'COMPLETED':
                                                                    echo 'bg-green-100 text-green-800';
                                                                    break;
                                                                case 'CANCELLED':
                                                                    echo 'bg-red-100 text-red-800';
                                                                    break;
                                                                case 'NO_SHOW':
                                                                    echo 'bg-gray-100 text-gray-800';
                                                                    break;
                                                                default:
                                                                    echo 'bg-gray-100 text-gray-800';
                                                            }
                                                            ?>">
                                                            <?= ucfirst(strtolower(str_replace('_', ' ', $booking['status']))) ?>
                                                        </span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                        <a href="<?= getBasePath() ?>/admin/bookings/view.php?id=<?= $booking['id'] ?>" 
                                                           class="text-blue-400 hover:text-blue-300">
                                                            View
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-8">
                                    <p class="text-gray-400">No bookings found matching your search criteria.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
