<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_profile':
                    $updateData = [
                        'name' => $_POST['name'],
                        'phone' => $_POST['phone'],
                        'date_of_birth' => $_POST['date_of_birth'] ?: null,
                        'image' => $_POST['avatar'] ?: null
                    ];
                    
                    updateCustomerProfile($_SESSION['user_id'], $updateData);
                    $message = 'Profile updated successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'change_password':
                    $currentPassword = $_POST['current_password'];
                    $newPassword = $_POST['new_password'];
                    $confirmPassword = $_POST['confirm_password'];
                    
                    if ($newPassword !== $confirmPassword) {
                        throw new Exception("New passwords do not match");
                    }
                    
                    if (strlen($newPassword) < 8) {
                        throw new Exception("Password must be at least 8 characters long");
                    }
                    
                    changeCustomerPassword($_SESSION['user_id'], $currentPassword, $newPassword);
                    $message = 'Password changed successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get customer data
$customerId = $_SESSION['user_id'];
$profile = getCustomerProfile($customerId);
$stats = getIndividualCustomerStats($customerId);
$loyaltyTier = getCustomerLoyaltyTierEnhanced($customerId);

$pageTitle = "Profile Management";

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<!-- Page Header -->
<div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl shadow-xl mb-8 hover-lift">
    <div class="px-6 sm:px-8 lg:px-10">
        <div class="py-8 md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div>
                        <div class="flex items-center">
                            <h1 class="text-3xl font-bold leading-7 text-white sm:leading-9 sm:truncate font-serif">
                                Profile <span class="text-salon-gold">Management</span>
                            </h1>
                        </div>
                        <dl class="mt-3 flex flex-col sm:mt-2 sm:flex-row sm:flex-wrap">
                            <dt class="sr-only">Page description</dt>
                            <dd class="flex items-center text-lg text-gray-300 font-medium sm:mr-6">
                                Manage your personal information and account settings
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

            <!-- Message Display -->
            <?php if ($message): ?>
                <div class="mb-8 p-6 rounded-xl <?= $messageType === 'success' ? 'bg-salon-gold/10 border border-salon-gold text-salon-gold' : 'bg-red-500/10 border border-red-500 text-red-400' ?> backdrop-blur-sm">
                    <div class="flex items-center">
                        <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-triangle' ?> mr-3 text-xl"></i>
                        <?= htmlspecialchars($message) ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Profile Overview -->
            <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-8 mb-8 hover-lift">
                <div class="flex items-center gap-8">
                    <div class="w-32 h-32 rounded-full bg-salon-gold flex items-center justify-center">
                        <?php if ($profile['avatar']): ?>
                            <img src="<?= htmlspecialchars($profile['avatar']) ?>" alt="Profile" class="w-32 h-32 rounded-full object-cover">
                        <?php else: ?>
                            <i class="fas fa-user text-black text-4xl"></i>
                        <?php endif; ?>
                    </div>

                    <div class="flex-1">
                        <h2 class="text-3xl font-bold text-white mb-2"><?= htmlspecialchars($profile['name']) ?></h2>
                        <p class="text-gray-400 text-lg mb-3"><?= htmlspecialchars($profile['email']) ?></p>
                        <div class="flex items-center gap-6 mt-3">
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-salon-gold/20 text-salon-gold border border-salon-gold/30">
                                <i class="fas fa-crown mr-2"></i><?= $loyaltyTier['name'] ?> Member
                            </span>
                            <span class="text-sm text-gray-400">
                                Member since <?= date('F Y', strtotime($profile['created_at'])) ?>
                            </span>
                        </div>
                    </div>

                    <div class="text-right">
                        <div class="grid grid-cols-2 gap-6 text-center">
                            <div class="bg-secondary-800/50 rounded-xl p-4">
                                <p class="text-3xl font-bold text-salon-gold"><?= $stats['completedBookings'] ?></p>
                                <p class="text-sm text-gray-400">Visits</p>
                            </div>
                            <div class="bg-secondary-800/50 rounded-xl p-4">
                                <p class="text-3xl font-bold text-salon-gold"><?= CURRENCY_SYMBOL ?> <?= number_format($stats['totalSpent'], 0) ?></p>
                                <p class="text-sm text-gray-400">Spent</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Personal Information -->
                <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-8 hover-lift">
                    <h3 class="text-xl font-bold text-white mb-6">Personal Information</h3>

                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="action" value="update_profile">

                        <div>
                            <label for="name" class="block text-sm font-semibold text-salon-gold mb-3">Full Name</label>
                            <input type="text" id="name" name="name" value="<?= htmlspecialchars($profile['name']) ?>" required
                                   class="w-full px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-semibold text-salon-gold mb-3">Email Address</label>
                            <input type="email" id="email" value="<?= htmlspecialchars($profile['email']) ?>" disabled
                                   class="w-full px-4 py-3 bg-secondary-800/30 border border-secondary-700 rounded-xl text-gray-400 cursor-not-allowed">
                            <p class="text-sm text-gray-500 mt-2">Email cannot be changed. Contact support if needed.</p>
                        </div>

                        <div>
                            <label for="phone" class="block text-sm font-semibold text-salon-gold mb-3">Phone Number</label>
                            <input type="tel" id="phone" name="phone" value="<?= htmlspecialchars($profile['phone'] ?? '') ?>"
                                   class="w-full px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                        </div>

                        <div>
                            <label for="date_of_birth" class="block text-sm font-semibold text-salon-gold mb-3">Date of Birth</label>
                            <input type="date" id="date_of_birth" name="date_of_birth" value="<?= $profile['date_of_birth'] ? date('Y-m-d', strtotime($profile['date_of_birth'])) : '' ?>"
                                   class="w-full px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                        </div>

                        <div>
                            <label for="avatar" class="block text-sm font-semibold text-salon-gold mb-3">Profile Picture URL</label>
                            <input type="url" id="avatar" name="avatar" value="<?= htmlspecialchars($profile['avatar'] ?? '') ?>"
                                   class="w-full px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300"
                                   placeholder="https://example.com/your-photo.jpg">
                        </div>

                        <button type="submit" class="w-full bg-salon-gold hover:bg-gold-light text-black px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg">
                            <i class="fas fa-save mr-2"></i>Update Profile
                        </button>
                    </form>
                </div>

                <!-- Security Settings -->
                <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-8 hover-lift">
                    <h3 class="text-xl font-bold text-white mb-6">Security Settings</h3>

                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="action" value="change_password">

                        <div>
                            <label for="current_password" class="block text-sm font-semibold text-salon-gold mb-3">Current Password</label>
                            <input type="password" id="current_password" name="current_password" required
                                   class="w-full px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                        </div>

                        <div>
                            <label for="new_password" class="block text-sm font-semibold text-salon-gold mb-3">New Password</label>
                            <input type="password" id="new_password" name="new_password" required minlength="8"
                                   class="w-full px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                            <p class="text-sm text-gray-500 mt-2">Must be at least 8 characters long</p>
                        </div>

                        <div>
                            <label for="confirm_password" class="block text-sm font-semibold text-salon-gold mb-3">Confirm New Password</label>
                            <input type="password" id="confirm_password" name="confirm_password" required minlength="8"
                                   class="w-full px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                        </div>

                        <button type="submit" class="w-full bg-red-500/20 hover:bg-red-500/30 border border-red-500 text-red-400 hover:text-red-300 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                            <i class="fas fa-key mr-2"></i>Change Password
                        </button>
                    </form>

                    <!-- Account Information -->
                    <div class="mt-8 pt-8 border-t border-secondary-700">
                        <h4 class="font-semibold text-white mb-4">Account Information</h4>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-3 bg-secondary-800/50 rounded-lg">
                                <span class="text-gray-400">Account Created:</span>
                                <span class="text-white font-medium"><?= date('M j, Y', strtotime($profile['created_at'])) ?></span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-secondary-800/50 rounded-lg">
                                <span class="text-gray-400">Referral Code:</span>
                                <span class="text-salon-gold font-mono font-semibold"><?= htmlspecialchars($profile['referral_code']) ?></span>
                            </div>
                            <?php if ($profile['referred_by']): ?>
                                <div class="flex justify-between items-center p-3 bg-secondary-800/50 rounded-lg">
                                    <span class="text-gray-400">Referred By:</span>
                                    <span class="text-white font-medium"><?= htmlspecialchars($profile['referred_by']) ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Statistics -->
            <div class="bg-secondary-800 rounded-lg p-6 mt-6">
                <h3 class="text-lg font-semibold text-white mb-4">Account Statistics</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-calendar-check text-blue-600 text-2xl"></i>
                        </div>
                        <p class="text-2xl font-bold text-white"><?= $stats['totalBookings'] ?></p>
                        <p class="text-sm text-gray-400">Total Bookings</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                        </div>
                        <p class="text-2xl font-bold text-white"><?= $stats['completedBookings'] ?></p>
                        <p class="text-sm text-gray-400">Completed Visits</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-dollar-sign text-yellow-600 text-2xl"></i>
                        </div>
                        <p class="text-2xl font-bold text-white"><?= CURRENCY_SYMBOL ?> <?= number_format($stats['avgBookingValue'], 0) ?></p>
                        <p class="text-sm text-gray-400">Avg. Booking Value</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-coins text-purple-600 text-2xl"></i>
                        </div>
                        <p class="text-2xl font-bold text-white"><?= number_format($profile['points']) ?></p>
                        <p class="text-sm text-gray-400">Current Points</p>
                    </div>
                </div>
                
                <?php if ($stats['firstVisit'] && $stats['lastVisit']): ?>
                    <div class="mt-6 pt-6 border-t border-secondary-700 text-center">
                        <p class="text-sm text-gray-400">
                            First visit: <?= date('M j, Y', strtotime($stats['firstVisit'])) ?> • 
                            Last visit: <?= date('M j, Y', strtotime($stats['lastVisit'])) ?>
                        </p>
                    </div>
                <?php endif; ?>
            </div>

<script>
    // Password confirmation validation
    document.getElementById('confirm_password').addEventListener('input', function() {
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = this.value;

        if (newPassword !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });

    // Copy referral code functionality
    function copyReferralCode() {
        const referralCode = '<?= htmlspecialchars($profile['referral_code']) ?>';
        navigator.clipboard.writeText(referralCode).then(function() {
            alert('Referral code copied to clipboard!');
        });
    }
</script>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>
