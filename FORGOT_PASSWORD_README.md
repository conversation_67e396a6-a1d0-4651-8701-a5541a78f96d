# Forgot Password System - Flix Salon & SPA

A comprehensive OTP-based password reset system with email verification, rate limiting, and security features.

## 🚀 Features

### Core Functionality
- **6-digit OTP generation** using cryptographically secure methods
- **Email delivery** via existing SMTP system with professional templates
- **15-minute expiration** for security
- **Rate limiting** (max 3 attempts per hour)
- **Real-time validation** with user-friendly error messages
- **Session-based flow** with proper state management

### Security Features
- **CSRF protection** using existing session tokens
- **Input validation** and sanitization
- **Secure OTP generation** using `random_int()`
- **Automatic cleanup** of expired OTPs
- **Rate limiting** to prevent abuse
- **Session validation** at each step

### User Experience
- **Responsive design** matching existing auth pages
- **Real-time timer** showing OTP expiration
- **Interactive OTP input** with auto-focus and paste support
- **Password strength indicator** with visual feedback
- **Clear error messages** and success notifications
- **Consistent visual design** with login/register pages

## 📁 File Structure

```
auth/
├── forgot-password.php     # Main email input page
├── verify-otp.php         # OTP verification page
└── reset-password.php     # New password creation page

includes/
├── forgot_password_functions.php  # Core OTP functionality
└── email_functions.php           # Updated with OTP email template

database/
├── migrations.sql         # Updated with OTP fields
└── add_otp_fields.php    # Migration script for existing databases
```

## 🔧 Installation

### 1. Database Setup

For new installations, the OTP fields are included in `database/migrations.sql`.

For existing databases, run the migration:
```bash
# Navigate to your project directory
cd /path/to/flix-php

# Run the migration script
php database/add_otp_fields.php
```

### 2. Database Fields Added

The following fields are added to the `users` table:
- `reset_otp` VARCHAR(6) NULL - Stores the 6-digit OTP
- `otp_expires_at` TIMESTAMP NULL - OTP expiration timestamp
- `otp_attempts` INT DEFAULT 0 - Failed attempt counter

### 3. Email Configuration

Ensure your SMTP settings are configured in `config/app.php`:
```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_SECURE', 'tls');
define('SMTP_FROM_NAME', 'Flix Salon & SPA');
define('SMTP_FROM_EMAIL', '<EMAIL>');
```

## 🔄 User Flow

### Step 1: Email Input (`forgot-password.php`)
1. User enters email address
2. System validates email exists in database
3. Generates secure 6-digit OTP
4. Sends OTP via email using professional template
5. Redirects to OTP verification page

### Step 2: OTP Verification (`verify-otp.php`)
1. User enters 6-digit OTP
2. Real-time countdown timer shows expiration
3. System validates OTP and attempts
4. On success, redirects to password reset
5. Includes resend functionality

### Step 3: Password Reset (`reset-password.php`)
1. User creates new password
2. Real-time strength indicator
3. Password requirements validation
4. Confirmation matching
5. Updates database and redirects to login

## 🛡️ Security Implementation

### Rate Limiting
- Maximum 3 OTP requests per hour per email
- Failed attempt tracking with automatic lockout
- Automatic cleanup of expired OTPs

### OTP Security
- Cryptographically secure generation using `random_int()`
- 15-minute expiration window
- Single-use tokens (cleared after verification)
- No OTP storage in logs or session

### Session Management
- Secure session-based flow tracking
- CSRF token validation on all forms
- Session cleanup after completion
- Proper redirect validation

## 📧 Email Template

The system includes a professional OTP email template with:
- Salon branding and colors
- Clear 6-digit code display
- Security warnings and tips
- Responsive design for all devices
- 15-minute expiration notice

## 🎨 Design Integration

### Visual Consistency
- Matches existing auth page design (login.php, register.php)
- Same color scheme and styling patterns
- Consistent error/success message handling
- Responsive layout for all screen sizes

### Interactive Elements
- Auto-advancing OTP input fields
- Real-time password strength checking
- Visual countdown timer
- Smooth animations and transitions

## 🧪 Testing

### Test the Complete Flow
1. Navigate to `/auth/forgot-password.php`
2. Enter a valid email address from your users table
3. Check email for OTP code
4. Enter OTP on verification page
5. Create new password
6. Login with new credentials

### Test Security Features
- Try invalid email addresses
- Test expired OTP codes
- Attempt rate limiting (3+ requests)
- Test invalid OTP entries
- Verify CSRF protection

## 🔍 Troubleshooting

### Email Not Received
1. Check SMTP configuration in `config/app.php`
2. Verify email credentials and app passwords
3. Check spam/junk folders
4. Review email logs in database `email_logs` table

### OTP Not Working
1. Ensure database migration completed successfully
2. Check OTP expiration (15 minutes)
3. Verify rate limiting hasn't been triggered
4. Check for typos in 6-digit code

### Database Issues
1. Run `php database/add_otp_fields.php` to add missing fields
2. Check database connection in `config/database.php`
3. Verify user permissions for ALTER TABLE operations

## 📊 Monitoring

### Email Logs
All email activity is logged in the `email_logs` table:
- Recipient email
- Subject line
- Status (SENT/FAILED)
- Timestamp and error details

### OTP Analytics
Monitor OTP usage through database queries:
```sql
-- Recent OTP requests
SELECT email, otp_expires_at, otp_attempts 
FROM users 
WHERE reset_otp IS NOT NULL;

-- Failed attempts
SELECT email, otp_attempts 
FROM users 
WHERE otp_attempts > 0;
```

## 🔧 Customization

### Modify OTP Length
Edit `generateOTP()` function in `includes/forgot_password_functions.php`:
```php
function generateOTP() {
    return sprintf('%08d', random_int(10000000, 99999999)); // 8-digit OTP
}
```

### Change Expiration Time
Modify the expiration in `sendPasswordResetOTP()`:
```php
$expiresAt = date('Y-m-d H:i:s', strtotime('+30 minutes')); // 30 minutes
```

### Customize Email Template
Edit the `password_reset_otp` template in `includes/email_functions.php` to match your branding.

## 🚀 Production Deployment

### Security Checklist
- [ ] SMTP credentials secured
- [ ] CSRF tokens enabled
- [ ] Rate limiting configured
- [ ] Email logging enabled
- [ ] Database indexes created
- [ ] Error logging configured

### Performance Optimization
- [ ] Database indexes on OTP fields
- [ ] Email queue for high volume
- [ ] Cleanup script for expired OTPs
- [ ] Monitor email delivery rates

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review email logs in the database
3. Test with a known working email address
4. Verify all configuration settings

The forgot password system is now fully integrated and ready for production use!
