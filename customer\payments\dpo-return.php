<?php
/**
 * DPO Pay Return Page
 * Handles return from DPO Pay payment gateway
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('/auth/login.php');
}

// Get payment ID from URL
$paymentId = $_GET['payment_id'] ?? '';
$transactionToken = $_GET['TransactionToken'] ?? '';
$companyRef = $_GET['CompanyRef'] ?? '';

if (empty($paymentId)) {
    redirect('/customer/payments?error=invalid_payment');
}

// Get payment details
$payment = $database->fetch("
    SELECT p.*, b.user_id, b.id as booking_id
    FROM payments p
    INNER JOIN bookings b ON p.booking_id = b.id
    WHERE p.id = ? AND b.user_id = ? AND p.payment_gateway = 'DPO'
", [$paymentId, $_SESSION['user_id']]);

if (!$payment) {
    redirect('/customer/payments?error=payment_not_found');
}

$pageTitle = 'Payment Status';
$paymentStatus = 'processing';
$statusMessage = 'Verifying your payment...';
$statusIcon = 'fas fa-spinner fa-spin';
$statusColor = 'text-yellow-500';

// If we have a transaction token, verify the payment
if (!empty($transactionToken)) {
    try {
        $verificationResult = verifyDpoToken($transactionToken);
        
        if ($verificationResult['success'] && $verificationResult['verified']) {
            // Payment successful
            $paymentStatus = 'success';
            $statusMessage = 'Payment completed successfully!';
            $statusIcon = 'fas fa-check-circle';
            $statusColor = 'text-green-500';
            
            // Update payment status in database
            updatePaymentStatus($payment['id'], 'COMPLETED', [
                'dpo_token' => $transactionToken,
                'dpo_verification' => $verificationResult
            ]);
            
            // Log payment event
            if (function_exists('logPaymentEvent')) {
                logPaymentEvent($payment['id'], 'COMPLETED', 'DPO', [
                    'transaction_token' => $transactionToken,
                    'verification_result' => $verificationResult
                ]);
            }
            
        } else if ($verificationResult['success'] && !$verificationResult['verified']) {
            // Payment pending
            $paymentStatus = 'pending';
            $statusMessage = 'Payment is being processed. Please wait...';
            $statusIcon = 'fas fa-clock';
            $statusColor = 'text-yellow-500';
            
        } else {
            // Payment failed
            $paymentStatus = 'failed';
            $statusMessage = 'Payment verification failed: ' . ($verificationResult['resultExplanation'] ?? 'Unknown error');
            $statusIcon = 'fas fa-times-circle';
            $statusColor = 'text-red-500';
            
            // Update payment status
            updatePaymentStatus($payment['id'], 'FAILED', [
                'dpo_token' => $transactionToken,
                'dpo_verification' => $verificationResult
            ]);
        }
        
    } catch (Exception $e) {
        $paymentStatus = 'error';
        $statusMessage = 'An error occurred while verifying your payment: ' . $e->getMessage();
        $statusIcon = 'fas fa-exclamation-triangle';
        $statusColor = 'text-red-500';
        error_log("DPO Payment Verification Error: " . $e->getMessage());
    }
} else {
    // No transaction token - payment might have been cancelled
    $paymentStatus = 'cancelled';
    $statusMessage = 'Payment was cancelled or not completed.';
    $statusIcon = 'fas fa-times-circle';
    $statusColor = 'text-red-500';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - <?= APP_NAME ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .bg-secondary-900 { background-color: #0f172a; }
        .bg-secondary-800 { background-color: #1e293b; }
        .bg-secondary-700 { background-color: #334155; }
        .text-salon-gold { color: #f59e0b; }
        .bg-salon-gold { background-color: #f59e0b; }
    </style>
</head>
<body class="bg-secondary-900 text-white">

<div class="min-h-screen py-8">
    <div class="max-w-md mx-auto px-4">
        <div class="bg-secondary-800 rounded-lg p-6 text-center">
            <div id="payment-status">
                <!-- Status Icon -->
                <div class="mb-6">
                    <i class="<?= $statusIcon ?> text-6xl <?= $statusColor ?>"></i>
                </div>
                
                <!-- Status Message -->
                <h2 class="text-2xl font-semibold text-white mb-4">
                    <?php
                    switch ($paymentStatus) {
                        case 'success':
                            echo 'Payment Successful!';
                            break;
                        case 'pending':
                            echo 'Payment Processing';
                            break;
                        case 'failed':
                            echo 'Payment Failed';
                            break;
                        case 'cancelled':
                            echo 'Payment Cancelled';
                            break;
                        case 'error':
                            echo 'Payment Error';
                            break;
                        default:
                            echo 'Verifying Payment';
                    }
                    ?>
                </h2>
                
                <p class="text-gray-400 mb-6"><?= htmlspecialchars($statusMessage) ?></p>
                
                <!-- Payment Details -->
                <?php if ($paymentStatus === 'success'): ?>
                <div class="bg-secondary-700 rounded-lg p-4 mb-6 text-left">
                    <h3 class="font-semibold text-salon-gold mb-3">Payment Details</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Payment ID:</span>
                            <span><?= htmlspecialchars($payment['payment_reference']) ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Amount:</span>
                            <span><?= CURRENCY_SYMBOL ?> <?= number_format($payment['amount']) ?></span>
                        </div>
                        <?php if (!empty($transactionToken)): ?>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Transaction:</span>
                            <span class="text-xs"><?= htmlspecialchars(substr($transactionToken, 0, 20)) ?>...</span>
                        </div>
                        <?php endif; ?>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Date:</span>
                            <span><?= date('M j, Y g:i A') ?></span>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Action Buttons -->
                <div class="space-y-3">
                    <?php if ($paymentStatus === 'success'): ?>
                        <a href="<?= getBasePath() ?>/customer/bookings" 
                           class="block w-full bg-salon-gold hover:bg-yellow-600 text-black font-semibold py-3 px-4 rounded-lg transition-colors">
                            <i class="fas fa-calendar-check mr-2"></i>View My Bookings
                        </a>
                        <a href="<?= getBasePath() ?>/customer/dashboard" 
                           class="block w-full bg-secondary-700 hover:bg-secondary-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors">
                            <i class="fas fa-home mr-2"></i>Go to Dashboard
                        </a>
                    <?php elseif ($paymentStatus === 'pending'): ?>
                        <button onclick="location.reload()" 
                                class="w-full bg-salon-gold hover:bg-yellow-600 text-black font-semibold py-3 px-4 rounded-lg transition-colors">
                            <i class="fas fa-refresh mr-2"></i>Check Status Again
                        </button>
                        <a href="<?= getBasePath() ?>/customer/payments" 
                           class="block w-full bg-secondary-700 hover:bg-secondary-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Payments
                        </a>
                    <?php else: ?>
                        <a href="<?= getBasePath() ?>/customer/payments/dpo.php?payment_id=<?= $paymentId ?>" 
                           class="block w-full bg-salon-gold hover:bg-yellow-600 text-black font-semibold py-3 px-4 rounded-lg transition-colors">
                            <i class="fas fa-retry mr-2"></i>Try Again
                        </a>
                        <a href="<?= getBasePath() ?>/customer/payments" 
                           class="block w-full bg-secondary-700 hover:bg-secondary-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Payments
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Help Section -->
        <div class="mt-6 text-center">
            <p class="text-gray-400 text-sm mb-2">Need help with your payment?</p>
            <a href="<?= getBasePath() ?>/contact" class="text-salon-gold hover:text-yellow-400 transition-colors text-sm">
                <i class="fas fa-headset mr-1"></i>Contact Support
            </a>
        </div>
    </div>
</div>

<?php if ($paymentStatus === 'pending'): ?>
<script>
// Auto-refresh for pending payments
setTimeout(function() {
    location.reload();
}, 10000); // Refresh every 10 seconds
</script>
<?php endif; ?>

</body>
</html>
