<?php
/**
 * Admin Booking Bulk Operations
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle bulk operations
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $bookingIds = $_POST['booking_ids'] ?? [];
    
    if (empty($bookingIds)) {
        $_SESSION['error'] = 'Please select at least one booking.';
    } else {
        $result = performBulkOperation($action, $bookingIds);
        
        if ($result['success']) {
            $_SESSION['success'] = $result['message'];
        } else {
            $_SESSION['error'] = $result['error'];
        }
    }
    
    redirect('/admin/bookings/bulk-operations.php');
}

// Get bookings for bulk operations
$bookings = $database->fetchAll(
    "SELECT b.*, u.name as customer_name, u.email as customer_email,
            s.name as service_name, st.name as staff_name
     FROM bookings b
     LEFT JOIN users u ON b.user_id = u.id
     LEFT JOIN services s ON b.service_id = s.id
     LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
     WHERE b.date >= CURDATE()
     GROUP BY b.id
     ORDER BY b.date ASC, b.start_time ASC"
);

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Bulk Operations";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>
            
            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-white">Bulk Operations</h1>
                                <p class="mt-1 text-sm text-gray-300">Perform actions on multiple bookings at once</p>
                            </div>
                            <div class="mt-4 sm:mt-0">
                                <a href="<?= getBasePath() ?>/admin/bookings" 
                                   class="bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                    Back to Bookings
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Bulk Operations Form -->
                    <form method="POST" id="bulkForm">
                        <!-- Bulk Actions Bar -->
                        <div class="bg-secondary-800 shadow rounded-lg p-4 mb-6">
                            <div class="flex flex-col sm:flex-row gap-4 items-center">
                                <div class="flex items-center gap-2">
                                    <input type="checkbox" id="selectAll" class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold">
                                    <label for="selectAll" class="text-sm text-gray-300">Select All</label>
                                </div>
                                
                                <div class="flex-1">
                                    <select name="bulk_action" id="bulkAction" required
                                            class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                        <option value="">Choose Action</option>
                                        <option value="confirm">Confirm Selected</option>
                                        <option value="complete">Mark as Completed</option>
                                        <option value="cancel">Cancel Selected</option>
                                        <option value="reschedule">Reschedule Selected</option>
                                        <option value="send_reminder">Send Reminders</option>
                                        <option value="export">Export Selected</option>
                                    </select>
                                </div>
                                
                                <button type="submit" id="executeAction" disabled
                                        class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                                    Execute Action
                                </button>
                            </div>
                        </div>

                        <!-- Bookings Table -->
                        <div class="bg-secondary-800 shadow rounded-lg overflow-hidden">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-secondary-700">
                                    <thead class="bg-secondary-700">
                                        <tr>
                                            <th class="px-6 py-3 text-left">
                                                <input type="checkbox" id="headerCheckbox" class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold">
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Customer</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Service</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Staff</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date & Time</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-secondary-800 divide-y divide-secondary-700">
                                        <?php foreach ($bookings as $booking): ?>
                                            <tr class="hover:bg-secondary-700">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <input type="checkbox" name="booking_ids[]" value="<?= $booking['id'] ?>" 
                                                           class="booking-checkbox rounded border-secondary-600 text-salon-gold focus:ring-salon-gold">
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        <div class="flex-shrink-0 h-8 w-8">
                                                            <div class="h-8 w-8 rounded-full bg-salon-gold flex items-center justify-center">
                                                                <span class="text-xs font-medium text-black">
                                                                    <?= strtoupper(substr($booking['customer_name'], 0, 2)) ?>
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="ml-3">
                                                            <div class="text-sm font-medium text-white"><?= htmlspecialchars($booking['customer_name']) ?></div>
                                                            <div class="text-xs text-gray-300"><?= htmlspecialchars($booking['customer_email']) ?></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-white"><?= htmlspecialchars($booking['service_name']) ?></div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-white"><?= htmlspecialchars($booking['staff_name'] ?? 'Unassigned') ?></div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-white"><?= date('M j, Y', strtotime($booking['date'])) ?></div>
                                                    <div class="text-xs text-gray-300"><?= date('g:i A', strtotime($booking['start_time'])) ?></div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                        <?php
                                                        switch ($booking['status']) {
                                                            case 'PENDING':
                                                                echo 'bg-yellow-100 text-yellow-800';
                                                                break;
                                                            case 'CONFIRMED':
                                                                echo 'bg-blue-100 text-blue-800';
                                                                break;
                                                            case 'COMPLETED':
                                                                echo 'bg-green-100 text-green-800';
                                                                break;
                                                            case 'CANCELLED':
                                                                echo 'bg-red-100 text-red-800';
                                                                break;
                                                            default:
                                                                echo 'bg-gray-100 text-gray-800';
                                                        }
                                                        ?>">
                                                        <?= ucfirst(strtolower(str_replace('_', ' ', $booking['status']))) ?>
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-salon-gold"><?= formatCurrency($booking['total_amount']) ?></div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const headerCheckbox = document.getElementById('headerCheckbox');
    const bookingCheckboxes = document.querySelectorAll('.booking-checkbox');
    const bulkActionSelect = document.getElementById('bulkAction');
    const executeButton = document.getElementById('executeAction');

    // Handle select all functionality
    function updateSelectAll() {
        const checkedBoxes = document.querySelectorAll('.booking-checkbox:checked');
        selectAllCheckbox.checked = checkedBoxes.length === bookingCheckboxes.length;
        headerCheckbox.checked = checkedBoxes.length === bookingCheckboxes.length;
        
        // Enable/disable execute button
        executeButton.disabled = checkedBoxes.length === 0 || !bulkActionSelect.value;
    }

    selectAllCheckbox.addEventListener('change', function() {
        bookingCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        headerCheckbox.checked = this.checked;
        updateSelectAll();
    });

    headerCheckbox.addEventListener('change', function() {
        bookingCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        selectAllCheckbox.checked = this.checked;
        updateSelectAll();
    });

    bookingCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectAll);
    });

    bulkActionSelect.addEventListener('change', updateSelectAll);

    // Form submission confirmation
    document.getElementById('bulkForm').addEventListener('submit', function(e) {
        const checkedBoxes = document.querySelectorAll('.booking-checkbox:checked');
        const action = bulkActionSelect.value;
        
        if (checkedBoxes.length === 0) {
            e.preventDefault();
            alert('Please select at least one booking.');
            return;
        }

        const actionText = bulkActionSelect.options[bulkActionSelect.selectedIndex].text;
        if (!confirm(`Are you sure you want to ${actionText.toLowerCase()} ${checkedBoxes.length} booking(s)?`)) {
            e.preventDefault();
        }
    });
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
