<?php
/**
 * Email Troubleshooting Script
 * Tests various connection methods and configurations
 */

// Include PHPMailer
require_once 'admin/vendor/PHPMailer/src/Exception.php';
require_once 'admin/vendor/PHPMailer/src/PHPMailer.php';
require_once 'admin/vendor/PHPMailer/src/SMTP.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use P<PERSON>Mailer\PHPMailer\Exception;

echo "<h1>Email System Troubleshooting</h1>";

// Configuration
$smtp_host = 'mail.barberianramadhancup.co.tz';
$smtp_port = 465;
$smtp_username = '<EMAIL>';
$smtp_password = 'UVutmmRceu37zK6';

echo "<h2>1. Basic Connectivity Test</h2>";

// Test 1: Basic socket connection
echo "<h3>Testing socket connection to $smtp_host:$smtp_port</h3>";
$socket = @fsockopen('ssl://' . $smtp_host, $smtp_port, $errno, $errstr, 30);
if ($socket) {
    echo "✅ SSL connection successful<br>";
    fclose($socket);
} else {
    echo "❌ SSL connection failed: $errstr ($errno)<br>";
    
    // Try without SSL
    echo "<h3>Testing non-SSL connection to $smtp_host:$smtp_port</h3>";
    $socket = @fsockopen($smtp_host, $smtp_port, $errno, $errstr, 30);
    if ($socket) {
        echo "✅ Non-SSL connection successful<br>";
        fclose($socket);
    } else {
        echo "❌ Non-SSL connection failed: $errstr ($errno)<br>";
    }
}

// Test 2: Alternative ports
echo "<h2>2. Testing Alternative Ports</h2>";
$ports = [25, 587, 465, 2525];
foreach ($ports as $port) {
    echo "Testing port $port: ";
    $socket = @fsockopen($smtp_host, $port, $errno, $errstr, 10);
    if ($socket) {
        echo "✅ Connected<br>";
        fclose($socket);
    } else {
        echo "❌ Failed<br>";
    }
}

// Test 3: DNS Resolution
echo "<h2>3. DNS Resolution Test</h2>";
$ip = gethostbyname($smtp_host);
if ($ip !== $smtp_host) {
    echo "✅ DNS resolution successful: $smtp_host → $ip<br>";
} else {
    echo "❌ DNS resolution failed for $smtp_host<br>";
}

// Test 4: PHP Configuration
echo "<h2>4. PHP Configuration</h2>";
echo "OpenSSL enabled: " . (extension_loaded('openssl') ? '✅ Yes' : '❌ No') . "<br>";
echo "Socket functions: " . (function_exists('fsockopen') ? '✅ Available' : '❌ Not available') . "<br>";
echo "Stream functions: " . (function_exists('stream_socket_client') ? '✅ Available' : '❌ Not available') . "<br>";
echo "cURL enabled: " . (extension_loaded('curl') ? '✅ Yes' : '❌ No') . "<br>";

// Test 5: PHPMailer with different configurations
if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
    echo "<h2>5. PHPMailer Configuration Tests</h2>";

    // Test different configurations
    $configs = [
        [
            'name' => 'SSL on port 465',
            'secure' => PHPMailer::ENCRYPTION_SMTPS,
            'port' => 465
        ],
        [
            'name' => 'TLS on port 587',
            'secure' => PHPMailer::ENCRYPTION_STARTTLS,
            'port' => 587
        ],
        [
            'name' => 'No encryption on port 25',
            'secure' => '',
            'port' => 25
        ],
        [
            'name' => 'No encryption on port 587',
            'secure' => '',
            'port' => 587
        ]
    ];

    foreach ($configs as $config) {
        echo "<h3>Testing: {$config['name']}</h3>";
        
        try {
            $mail = new PHPMailer(true);
            $mail->isSMTP();
            $mail->Host = $smtp_host;
            $mail->SMTPAuth = true;
            $mail->Username = $smtp_username;
            $mail->Password = $smtp_password;
            $mail->SMTPSecure = $config['secure'];
            $mail->Port = $config['port'];
            $mail->Timeout = 30;
            
            // Disable SSL verification for testing
            $mail->SMTPOptions = array(
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                )
            );

            // Try to connect
            if ($mail->smtpConnect()) {
                echo "✅ SMTP connection successful<br>";
                $mail->smtpClose();
            } else {
                echo "❌ SMTP connection failed<br>";
            }
        } catch (Exception $e) {
            echo "❌ Error: " . htmlspecialchars($e->getMessage()) . "<br>";
        }
    }
} else {
    echo "<h2>5. PHPMailer Not Found</h2>";
    echo "❌ PHPMailer not found in admin/vendor/PHPMailer/<br>";
}

// Test 6: Server environment
echo "<h2>6. Server Environment</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Operating System: " . php_uname() . "<br>";
echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";

// Test 7: Firewall/Network test
echo "<h2>7. Network Connectivity</h2>";
echo "Testing external SMTP servers for comparison:<br>";

$test_servers = [
    ['host' => 'smtp.gmail.com', 'port' => 587],
    ['host' => 'smtp.gmail.com', 'port' => 465],
    ['host' => 'smtp.outlook.com', 'port' => 587]
];

foreach ($test_servers as $server) {
    echo "Testing {$server['host']}:{$server['port']}: ";
    $socket = @fsockopen($server['host'], $server['port'], $errno, $errstr, 10);
    if ($socket) {
        echo "✅ Connected<br>";
        fclose($socket);
    } else {
        echo "❌ Failed<br>";
    }
}

echo "<h2>Recommendations</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>If connections are failing:</h3>";
echo "<ol>";
echo "<li><strong>Check with your hosting provider:</strong> Many shared hosts block outbound SMTP connections</li>";
echo "<li><strong>Verify credentials:</strong> Double-check the email address and password</li>";
echo "<li><strong>Try alternative ports:</strong> Some ISPs block port 25 or 465</li>";
echo "<li><strong>Contact email provider:</strong> Verify the SMTP settings with barberianramadhancup.co.tz</li>";
echo "<li><strong>Check firewall:</strong> Ensure outbound connections are allowed</li>";
echo "<li><strong>Try from different network:</strong> Test from a different internet connection</li>";
echo "</ol>";

echo "<h3>Alternative solutions:</h3>";
echo "<ul>";
echo "<li>Use a third-party email service (SendGrid, Mailgun, etc.)</li>";
echo "<li>Configure a local mail server</li>";
echo "<li>Use your hosting provider's SMTP service</li>";
echo "<li>Try webmail-based sending if available</li>";
echo "</ul>";
echo "</div>";

echo "<h2>Next Steps</h2>";
echo "<p>If any configuration shows ✅ success above, update your email settings to use that configuration.</p>";
echo "<p>If all tests fail, contact your hosting provider or email service provider for assistance.</p>";
?>
