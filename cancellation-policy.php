<?php
/**
 * Cancellation Policy Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "Cancellation Policy";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Enhanced Cancellation Policy Styles */
.cancellation-card {
    background: linear-gradient(135deg, rgba(10, 10, 10, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.cancellation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    transition: left 0.6s ease;
}

.cancellation-card:hover::before {
    left: 100%;
}

.cancellation-card:hover {
    transform: translateY(-5px);
    border-color: rgba(212, 175, 55, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1);
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate {
    opacity: 1;
    transform: translateY(0);
}

.timeline-item {
    position: relative;
    padding-left: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.5rem;
    width: 12px;
    height: 12px;
    background: #D4AF37;
    border-radius: 50%;
    border: 3px solid #141414;
}

.timeline-item::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 1.5rem;
    width: 2px;
    height: calc(100% - 1rem);
    background: linear-gradient(to bottom, #D4AF37, transparent);
}

.timeline-item:last-child::after {
    display: none;
}

.fee-badge {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #FCA5A5;
}

.no-fee-badge {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(22, 163, 74, 0.05) 100%);
    border: 1px solid rgba(34, 197, 94, 0.2);
    color: #86EFAC;
}
</style>

<!-- Enhanced Hero Section -->
<section class="relative py-32 bg-gradient-to-br from-salon-black via-salon-black to-salon-black overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute top-20 left-10 w-40 h-40 bg-salon-gold/10 rounded-full blur-3xl animate-pulse"></div>
    <div class="absolute bottom-20 right-10 w-60 h-60 bg-salon-gold/5 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
    <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/5 via-transparent to-salon-gold/3"></div>

    <!-- Parallax Background -->
    <div class="absolute inset-0 opacity-10">
        <div class="h-full bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-6 text-center">
        <!-- Luxury Badge -->
        <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-salon-gold/20 animate-on-scroll">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            Cancellation Guidelines
        </div>

        <h1 class="text-6xl md:text-8xl font-bold font-serif text-white mb-8 leading-tight animate-on-scroll" style="--delay: 0.2s;">
            Cancellation
            <span class="block md:inline text-transparent bg-clip-text bg-gradient-to-r from-salon-gold via-yellow-400 to-salon-gold">
                Policy
            </span>
        </h1>

        <p class="text-xl md:text-3xl text-gray-300 max-w-5xl mx-auto leading-relaxed mb-12 animate-on-scroll" style="--delay: 0.4s;">
            Please review our cancellation and rescheduling policies to ensure a smooth experience for all clients.
            <span class="block mt-4 text-salon-gold text-lg md:text-xl">Fair policies for everyone's convenience</span>
        </p>

        <!-- Quick Policy Timeline -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto animate-on-scroll" style="--delay: 0.6s;">
            <div class="cancellation-card rounded-xl p-6 text-center">
                <div class="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-white font-semibold mb-2">24+ Hours</h3>
                <p class="text-green-400 text-sm font-medium">No Charge</p>
            </div>

            <div class="cancellation-card rounded-xl p-6 text-center">
                <div class="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                </div>
                <h3 class="text-white font-semibold mb-2">12-24 Hours</h3>
                <p class="text-yellow-400 text-sm font-medium">50% Fee</p>
            </div>

            <div class="cancellation-card rounded-xl p-6 text-center">
                <div class="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-white font-semibold mb-2">Less than 12 Hours</h3>
                <p class="text-red-400 text-sm font-medium">Full Fee</p>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Policy Content -->
<section class="py-32 bg-salon-black relative">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #D4AF37 2px, transparent 2px), radial-gradient(circle at 75% 75%, #D4AF37 2px, transparent 2px); background-size: 50px 50px;"></div>
    </div>

    <div class="max-w-6xl mx-auto px-6 relative">

        <!-- Enhanced Overview -->
        <div class="cancellation-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Policy Overview</h2>
                <p class="text-gray-300">Understanding our fair cancellation guidelines</p>
            </div>
            <p class="text-gray-300 leading-relaxed mb-4">
                At Flix Salon & SPA, we understand that sometimes plans change. Our cancellation policy is designed to be fair to both our clients and our staff while ensuring we can provide the best possible service to everyone.
            </p>
            <p class="text-gray-300 leading-relaxed">
                We kindly ask for advance notice when canceling or rescheduling appointments to allow us to offer those time slots to other clients.
            </p>
        </div>

        <!-- Enhanced Standard Services -->
        <div class="cancellation-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Standard Services</h2>
                <p class="text-gray-300">Cancellation timeline for regular appointments</p>
            </div>

            <div class="space-y-6">
                <div class="timeline-item bg-green-500/10 border-l-4 border-salon-gold rounded-xl p-6">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <svg class="w-6 h-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white mb-3">24+ Hours Notice</h3>
                            <p class="text-gray-300 leading-relaxed">
                                <span class="no-fee-badge px-3 py-1 rounded-full text-sm font-medium mr-2">No Charge</span>
                                Free cancellation or rescheduling with 24 hours or more advance notice. We appreciate your consideration for our schedule.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="timeline-item bg-yellow-500/10 border-l-4 border-yellow-500 rounded-xl p-6">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-yellow-500/20 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <svg class="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white mb-3">12-24 Hours Notice</h3>
                            <p class="text-gray-300 leading-relaxed">
                                <span class="fee-badge px-3 py-1 rounded-full text-sm font-medium mr-2">50% Service Fee</span>
                                Cancellations made 12-24 hours before appointment time. This helps cover preparation costs and lost booking opportunities.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="timeline-item bg-red-500/10 border-l-4 border-red-500 rounded-xl p-6">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <svg class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white mb-3">Less than 12 Hours / No-Show</h3>
                            <p class="text-gray-300 leading-relaxed">
                                <span class="fee-badge px-3 py-1 rounded-full text-sm font-medium mr-2">Full Service Fee</span>
                                Late cancellations and no-shows will be charged the full service amount. This ensures fairness to our staff and other clients.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Special Services -->
        <div class="cancellation-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Special Services & Events</h2>
                <p class="text-gray-300">Premium services require extended notice periods</p>
            </div>

            <div class="space-y-8">
                <div class="bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <svg class="w-6 h-6 text-salon-gold mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        Bridal & Wedding Services
                    </h3>
                    <div class="space-y-4">
                        <div class="flex items-start p-4 bg-green-500/10 rounded-lg border border-salon-gold/20">
                            <div class="w-2 h-2 bg-salon-gold rounded-full mr-3 mt-2"></div>
                            <span class="text-gray-300"><strong class="text-salon-gold">7+ days:</strong> No charge for cancellation or rescheduling</span>
                        </div>
                        <div class="flex items-start p-4 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                            <div class="w-2 h-2 bg-yellow-500 rounded-full mr-3 mt-2"></div>
                            <span class="text-gray-300"><strong class="text-yellow-500">3-7 days:</strong> 50% of total service cost</span>
                        </div>
                        <div class="flex items-start p-4 bg-red-500/10 rounded-lg border border-red-500/20">
                            <div class="w-2 h-2 bg-red-500 rounded-full mr-3 mt-2"></div>
                            <span class="text-gray-300"><strong class="text-red-500">Less than 3 days:</strong> Full service cost</span>
                        </div>
                    </div>
                </div>

                <div class="bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <svg class="w-6 h-6 text-salon-gold mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        Group Appointments (3+ people)
                    </h3>
                    <div class="space-y-4">
                        <div class="flex items-start p-4 bg-green-500/10 rounded-lg border border-salon-gold/20">
                            <div class="w-2 h-2 bg-salon-gold rounded-full mr-3 mt-2"></div>
                            <span class="text-gray-300"><strong class="text-salon-gold">48+ hours:</strong> No charge for cancellation or rescheduling</span>
                        </div>
                        <div class="flex items-start p-4 bg-red-500/10 rounded-lg border border-red-500/20">
                            <div class="w-2 h-2 bg-red-500 rounded-full mr-3 mt-2"></div>
                            <span class="text-gray-300"><strong class="text-red-500">Less than 48 hours:</strong> 50% of total group cost</span>
                        </div>
                    </div>
                </div>

                <div class="bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50">
                    <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                        <svg class="w-6 h-6 text-salon-gold mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        Package Services
                    </h3>
                    <p class="text-gray-300 leading-relaxed">
                        Package appointments follow the same policy as individual services, but cancellation fees apply to the entire package value. This ensures fair treatment for comprehensive service bookings.
                    </p>
                </div>
            </div>
        </div>

        <!-- Enhanced How to Cancel -->
        <div class="cancellation-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">How to Cancel or Reschedule</h2>
                <p class="text-gray-300">Multiple convenient ways to manage your appointments</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50 hover:border-salon-gold/30 transition-colors">
                    <div class="w-20 h-20 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Online</h3>
                    <p class="text-gray-300 leading-relaxed">Log into your account and manage your appointments 24/7 with instant confirmation</p>
                </div>

                <div class="text-center bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50 hover:border-salon-gold/30 transition-colors">
                    <div class="w-20 h-20 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Phone</h3>
                    <p class="text-gray-300 leading-relaxed">Call us at (************* during business hours for personal assistance</p>
                </div>

                <div class="text-center bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50 hover:border-salon-gold/30 transition-colors">
                    <div class="w-20 h-20 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Email</h3>
                    <p class="text-gray-300 leading-relaxed">Email <NAME_EMAIL> with your appointment details</p>
                </div>
            </div>
        </div>

        <!-- Enhanced Emergency Situations -->
        <div class="cancellation-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Emergency Situations</h2>
                <p class="text-gray-300">We understand that life happens unexpectedly</p>
            </div>

            <div class="bg-secondary-900/50 rounded-xl p-8 border border-secondary-700/50">
                <p class="text-gray-300 leading-relaxed mb-6 text-lg">
                    We understand that genuine emergencies can occur. In cases of medical emergencies, family emergencies, or severe weather conditions, we may waive cancellation fees at our discretion.
                </p>
                <div class="highlight-box rounded-lg p-6">
                    <p class="text-gray-300 leading-relaxed">
                        Please contact us as soon as possible to discuss your situation. Documentation may be required for emergency exemptions. We're here to support our clients during difficult times.
                    </p>
                </div>
            </div>
        </div>

        <!-- Enhanced Payment Information -->
        <div class="cancellation-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Payment & Refunds</h2>
                <p class="text-gray-300">Clear and transparent payment processing</p>
            </div>

            <div class="space-y-6">
                <div class="bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50">
                    <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                        <svg class="w-6 h-6 text-salon-gold mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        Cancellation Fees
                    </h3>
                    <p class="text-gray-300 leading-relaxed">
                        Cancellation fees will be charged to the payment method on file. If no payment method is available, fees must be paid before booking future appointments.
                    </p>
                </div>

                <div class="bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50">
                    <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                        <svg class="w-6 h-6 text-salon-gold mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Prepaid Services
                    </h3>
                    <p class="text-gray-300 leading-relaxed">
                        For prepaid services, refunds will be processed according to the cancellation timeline above, minus any applicable fees.
                    </p>
                </div>

                <div class="bg-secondary-900/50 rounded-xl p-6 border border-secondary-700/50">
                    <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                        <svg class="w-6 h-6 text-salon-gold mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                        </svg>
                        Gift Cards
                    </h3>
                    <p class="text-gray-300 leading-relaxed">
                        Services paid with gift cards follow the same cancellation policy. Unused portions remain on the gift card for future use.
                    </p>
                </div>
            </div>
        </div>

        <!-- Enhanced Contact Section -->
        <div class="highlight-box rounded-3xl p-10 text-center animate-on-scroll">
            <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h2 class="text-3xl font-bold text-white mb-6">Questions About Our Policy?</h2>
            <p class="text-gray-300 mb-8 text-lg leading-relaxed max-w-2xl mx-auto">
                If you have questions about our cancellation policy or need to discuss a specific situation, please don't hesitate to contact us. We're here to help.
            </p>
            <div class="flex flex-col sm:flex-row gap-6 justify-center">
                <a href="<?= getBasePath() ?>/contact.php" class="bg-gradient-to-r from-salon-gold to-yellow-500 hover:from-yellow-500 hover:to-salon-gold text-black px-8 py-4 rounded-xl font-bold transition-all hover:scale-105 shadow-lg">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    Contact Us
                </a>
                <a href="tel:+15551234567" class="bg-secondary-900/80 hover:bg-secondary-800/80 text-white px-8 py-4 rounded-xl font-bold transition-all hover:scale-105 border border-secondary-700/50 backdrop-blur-sm">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                    Call (*************
                </a>
            </div>
        </div>
    </div>
</section>

<script>
// Enhanced Cancellation Policy Page Interactions
document.addEventListener('DOMContentLoaded', function() {
    // Scroll animations
    function handleScrollAnimations() {
        const elements = document.querySelectorAll('.animate-on-scroll');

        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;

            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('animate');
            }
        });
    }

    // Intersection Observer for better performance
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, observerOptions);

    // Observe all animated elements
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });

    // Timeline item animations
    document.querySelectorAll('.timeline-item').forEach((item, index) => {
        setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, index * 200);
    });

    // Initial check for elements already in view
    handleScrollAnimations();

    // Add loading animation to page
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';

    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
