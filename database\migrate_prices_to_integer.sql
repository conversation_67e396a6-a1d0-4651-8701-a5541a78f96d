-- Migration Script: Convert Price Fields from DECIMAL to INT for TSH Currency
-- Tanzanian Shilling does not use fractional units, so we convert to whole numbers
-- Execute this script to update all price-related fields

USE flix_salonce2;

-- Start transaction for data integrity
START TRANSACTION;

-- 1. SERVICES TABLE
-- First, round any existing decimal values and convert to integers
UPDATE services SET price = ROUND(price);

-- Change column type to INT
ALTER TABLE services MODIFY COLUMN price INT NOT NULL;

-- 2. PACKAGES TABLE  
-- Round existing decimal values
UPDATE packages SET price = ROUND(price);
UPDATE packages SET discount = ROUND(discount);

-- Change column types to INT
ALTER TABLE packages MODIFY COLUMN price INT NOT NULL;
ALTER TABLE packages MODIFY COLUMN discount INT DEFAULT 0;

-- 3. BOOKINGS TABLE
-- Round existing decimal values
UPDATE bookings SET total_amount = ROUND(total_amount);

-- Change column type to INT
ALTER TABLE bookings MODIFY COLUMN total_amount INT NOT NULL;

-- 4. <PERSON>YMENTS TABLE
-- Round existing decimal values  
UPDATE payments SET amount = ROUND(amount);

-- Change column type to INT
ALTER TABLE payments MODIFY COLUMN amount INT NOT NULL;

-- 5. STAFF_SCHEDULES TABLE
-- Round existing decimal values
UPDATE staff_schedules SET hourly_rate = ROUND(hourly_rate);

-- Change column type to INT
ALTER TABLE staff_schedules MODIFY COLUMN hourly_rate INT DEFAULT 50;

-- 6. REWARDS TABLE (if exists)
-- Check if rewards table exists and update if present
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables
                    WHERE table_schema = 'flix_salonce2' AND table_name = 'rewards');

-- Update rewards table if it exists (split into separate statements)
SET @sql1 = IF(@table_exists > 0, 'UPDATE rewards SET value = ROUND(value)', 'SELECT 1');
SET @sql2 = IF(@table_exists > 0, 'ALTER TABLE rewards MODIFY COLUMN value INT NOT NULL', 'SELECT 1');

PREPARE stmt1 FROM @sql1;
EXECUTE stmt1;
DEALLOCATE PREPARE stmt1;

PREPARE stmt2 FROM @sql2;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

-- 7. OFFERS TABLE
-- Round existing decimal values
UPDATE offers SET discount = ROUND(discount);

-- Change column type to INT  
ALTER TABLE offers MODIFY COLUMN discount INT NOT NULL;

-- Commit the transaction
COMMIT;

-- Verify the changes
SELECT 'Migration completed successfully. Verifying table structures...' as status;

-- Show updated table structures
DESCRIBE services;
DESCRIBE packages;
DESCRIBE bookings;
DESCRIBE payments;
DESCRIBE staff_schedules;
DESCRIBE offers;

-- Verification queries (optional - comment out if not needed)
-- SELECT 'Migration completed successfully!' as final_status;
-- SELECT 'Verifying Services:' as info;
-- SELECT id, name, price FROM services LIMIT 3;
-- SELECT 'Verifying Packages:' as info;
-- SELECT id, name, price, discount FROM packages LIMIT 3;
-- SELECT 'Verifying Bookings:' as info;
-- SELECT id, total_amount FROM bookings LIMIT 3;
