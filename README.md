# Flix Salon & SPA - PHP Version (V3 Black Theme)

A complete PHP-based salon management system with luxurious black theme design, advanced features, and comprehensive functionality for salon operations.

## 🌟 V3 Black Theme Features

### 🎨 Visual Design
- **Unified Black Theme**: Elegant black color palette across all panels
- **Glass Header Effects**: Modern glassmorphism design elements
- **3D Interactive Elements**: Advanced animations and hover effects
- **Mobile-First Design**: Fully responsive across all devices

### Three Panel Systems
- **Admin Panel**: Complete administrative control with dashboard, analytics, and management tools
- **Staff Panel**: Staff-specific features for managing appointments and schedules
- **Customer Panel**: Customer-facing interface for bookings and account management

### 💳 Payment Integration
- **Multiple Gateways**: Stripe, Flutterwave, and DPO payment support
- **Secure Processing**: PCI-compliant payment handling
- **Real-time Verification**: Instant payment confirmation

### 📧 Communication System
- **Email Integration**: SMTP-based email system
- **Forgot Password**: OTP-based password recovery
- **Booking Reminders**: Automated appointment notifications
- **Customer Messaging**: Direct admin-to-customer communication

### Core Functionality
- ✅ Complete booking system with calendar integration
- ✅ Payment processing integration (Stripe-ready)
- ✅ Reward points system with referral bonuses
- ✅ Role-based authentication (Admin, Staff, Customer)
- ✅ Mobile-responsive design
- ✅ CMS functionality for content management
- ✅ Gallery and blog management
- ✅ Email notifications system
- ✅ Advanced admin dashboard with analytics

### Design & UX
- 🎨 Luxury salon theme (black/gold/white color scheme)
- 📱 Fully responsive mobile design
- ✨ Smooth animations and transitions
- 🔍 Intuitive user interface
- 🎯 Exact visual parity with Next.js version

## 🛠 Technology Stack

- **Backend**: Pure PHP 8.0+ (no frameworks)
- **Frontend**: HTML5, CSS3, Tailwind CSS
- **JavaScript**: Vanilla JS with modern ES6+ features
- **Database**: MySQL 8.0+ with PDO
- **Authentication**: Custom session-based system
- **Security**: CSRF protection, input sanitization, SQL injection prevention

## 📁 Project Structure

```
flix-php/
├── config/                 # Database and app configuration
│   ├── app.php            # Main application config
│   └── database.php       # Database connection
├── includes/              # Common PHP includes and utilities
│   ├── auth.php           # Authentication system
│   ├── functions.php      # Helper functions
│   ├── header.php         # Main site header
│   ├── footer.php         # Main site footer
│   ├── admin_header.php   # Admin panel header
│   ├── admin_footer.php   # Admin panel footer
│   └── admin_sidebar.php  # Admin navigation
├── admin/                 # Admin panel
│   ├── index.php          # Admin dashboard
│   ├── services/          # Service management
│   ├── bookings/          # Booking management
│   ├── customers/         # Customer management
│   ├── staff/             # Staff management
│   ├── earnings/          # Revenue tracking
│   ├── rewards/           # Points & rewards
│   ├── cms/               # Content management
│   └── settings/          # System settings
├── staff/                 # Staff panel
├── user/                  # User/customer panel
├── auth/                  # Authentication system
│   ├── login.php          # Login page
│   ├── register.php       # Registration page
│   └── logout.php         # Logout handler
├── api/                   # API endpoints
│   ├── admin/             # Admin API routes
│   └── newsletter.php     # Newsletter subscription
├── database/              # Database setup and migrations
│   ├── migrations.sql     # Database schema
│   └── seed.php           # Sample data seeder
├── uploads/               # File uploads directory
├── assets/                # Static assets
├── .htaccess              # URL rewriting and security
├── setup.php              # Database setup script
└── index.php              # Main landing page
```

## 🚀 Installation & Setup

### Prerequisites
- PHP 8.0 or higher
- MySQL 8.0 or higher
- Apache/Nginx web server
- mod_rewrite enabled (for Apache)
- Git (for cloning from GitHub)

### Installation from GitHub

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/flix-salonce-php.git
   cd flix-salonce-php
   ```

2. **Set up configuration files**
   ```bash
   # Copy configuration templates
   cp config/app.php.example config/app.php
   cp config/database.php.example config/database.php
   ```

3. **Configure Database**
   - Create a MySQL database named `flix_salonce2`
   - Edit `config/database.php` with your database credentials:
   ```php
   private $host = 'localhost';
   private $db_name = 'flix_salonce2';
   private $username = 'root';
   private $password = '';
   ```

   - **Option A: Import Complete Database (Recommended for V3)**
   ```bash
   # Import the complete database with all data and structure
   mysql -u root -p flix_salonce2 < database/flix_salonce2.sql
   ```

   - **Option B: Use Migration Files**
   ```bash
   # Import schema only, then run setup for sample data
   mysql -u root -p flix_salonce2 < database/migrations.sql
   ```

4. **Configure Application**
   - Edit `config/app.php` with your application settings:
   ```php
   define('APP_URL', 'http://localhost/flix-salonce-php');
   define('JWT_SECRET', 'your-unique-secret-key-here');
   // Update other settings as needed
   ```

5. **Run Setup Script**
   - Navigate to `http://localhost/flix-salonce-php/setup.php`
   - The script will automatically:
     - Create all database tables
     - Seed with sample data
     - Create demo accounts

6. **Access the Application**
   - **Website**: `http://localhost/flix-salonce-php/`
   - **Admin Panel**: `http://localhost/flix-salonce-php/admin/`

### Demo Accounts

After setup, you can login with these demo accounts:

| Role | Email | Password | Access |
|------|-------|----------|---------|
| Admin | <EMAIL> | admin123 | Full system access |
| Staff | <EMAIL> | staff123 | Staff panel access |
| Customer | <EMAIL> | customer123 | User panel access |

## 🎯 Feature Parity with Next.js Version

This PHP version maintains **100% feature parity** with the original Next.js application:

### ✅ Completed Features
- [x] User authentication & authorization
- [x] Role-based access control (Admin/Staff/Customer)
- [x] Complete booking system
- [x] Service & package management
- [x] Staff scheduling system
- [x] Payment integration structure
- [x] Reward points & referral system
- [x] Admin dashboard with analytics
- [x] Customer management
- [x] Gallery & CMS functionality
- [x] Newsletter subscription
- [x] Mobile responsive design
- [x] Luxury salon theme (black/gold/white)
- [x] Email notification system
- [x] Settings management
- [x] Security features

### 🎨 Design Consistency
- Identical color scheme (#0f172a background, #f59e0b gold accents)
- Same typography (Inter + Playfair Display fonts)
- Matching component layouts and spacing
- Consistent animations and transitions
- Mobile responsiveness identical to original

## 🔧 Configuration

### Environment Settings
Key settings can be modified in `config/app.php`:

```php
// Application settings
define('APP_NAME', 'Flix Salon & SPA');
define('APP_URL', 'http://localhost/flix-php');

// Email configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_USERNAME', '<EMAIL>');

// Payment configuration
define('STRIPE_PUBLIC_KEY', 'pk_test_...');
define('STRIPE_SECRET_KEY', 'sk_test_...');
```

### Database Configuration
Update `config/database.php` for your environment:

```php
private $host = 'localhost';        // Database host
private $db_name = 'flix_salonce';  // Database name
private $username = 'root';         // Database username
private $password = '';             // Database password
```

## 🔐 Security Features

- **SQL Injection Prevention**: All queries use prepared statements
- **XSS Protection**: Input sanitization and output escaping
- **CSRF Protection**: Session-based token validation
- **Authentication**: Secure password hashing with bcrypt
- **Session Management**: Secure session handling
- **File Upload Security**: Type and size validation
- **Access Control**: Role-based permissions

## 📊 Admin Panel Features

### Dashboard
- Real-time statistics and analytics
- Revenue tracking and charts
- Recent bookings overview
- Quick action buttons
- System status monitoring

### Management Modules
- **Services**: Add, edit, delete salon services
- **Packages**: Create service bundles with discounts
- **Bookings**: View and manage all appointments
- **Customers**: Customer database and profiles
- **Staff**: Staff management and scheduling
- **Earnings**: Revenue reports and analytics
- **Rewards**: Points system configuration
- **CMS**: Content and gallery management
- **Settings**: System configuration

## 🎨 Customization

### Styling
The application uses Tailwind CSS with custom color variables:
```css
:root {
  --background: #0f172a;    /* Main background */
  --foreground: #ffffff;    /* Text color */
  --gold: #f59e0b;         /* Primary accent */
  --gold-light: #fbbf24;   /* Light accent */
  --gold-dark: #d97706;    /* Dark accent */
}
```

### Adding New Features
1. Create new PHP files in appropriate directories
2. Add database tables via migrations
3. Update navigation in sidebar files
4. Follow existing code patterns for consistency

## 🚀 Deployment

### Production Deployment
1. Upload files to your web server
2. Configure database credentials
3. Run setup.php once
4. Delete setup.php for security
5. Configure SSL certificate
6. Set up email SMTP settings
7. Configure payment gateway

### Server Requirements
- PHP 8.0+
- MySQL 8.0+
- Apache with mod_rewrite OR Nginx
- SSL certificate (recommended)
- Email server access (for notifications)

## 🤝 Support & Maintenance

### Regular Maintenance
- Monitor error logs
- Update database backups
- Review security settings
- Update dependencies as needed

### Troubleshooting
Common issues and solutions:

1. **Database Connection Error**
   - Check credentials in `config/database.php`
   - Ensure MySQL service is running
   - Verify database exists

2. **Permission Errors**
   - Check file permissions (755 for directories, 644 for files)
   - Ensure uploads directory is writable

3. **URL Rewriting Issues**
   - Verify mod_rewrite is enabled
   - Check .htaccess file exists and is readable

## 🔒 Security & Configuration

### Important Security Notes
- **Never commit sensitive configuration files** - `config/app.php` and `config/database.php` are excluded from Git
- **Change default secrets** - Update `JWT_SECRET` and other security keys in your configuration
- **Use HTTPS in production** - Update `APP_URL` and session settings for SSL
- **Secure file uploads** - The uploads directory is protected but monitor for security
- **Regular updates** - Keep PHP and MySQL updated for security patches

### Configuration Templates
This repository includes template files for easy setup:
- `config/app.php.example` - Application configuration template
- `config/database.php.example` - Database configuration template

Copy these files and update with your actual settings before running the application.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is part of the Flix Salon & SPA salon management system.

---

**Flix Salon & SPA PHP Version** - Complete salon management system with luxury design and comprehensive features.
