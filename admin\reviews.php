<?php
/**
 * Admin Reviews Management
 * Flix Salon & SPA - Admin Panel
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/reviews_functions.php';

// Check admin authentication
if (!isLoggedIn() || !hasRole('ADMIN')) {
    header('Location: ' . getBasePath() . '/auth/login.php');
    exit;
}

// Handle review actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $reviewId = sanitize($_POST['review_id'] ?? '');

        if (empty($reviewId)) {
            throw new Exception("Invalid review ID");
        }

        switch ($_POST['action']) {
            case 'approve':
                $result = $database->execute("UPDATE reviews SET status = 'verified', is_verified = 1 WHERE id = ?", [$reviewId]);
                if ($result) {
                    $_SESSION['success'] = "Review approved successfully.";
                } else {
                    throw new Exception("Failed to approve review");
                }
                break;

            case 'reject':
                $result = $database->execute("UPDATE reviews SET status = 'rejected', is_verified = 0 WHERE id = ?", [$reviewId]);
                if ($result) {
                    $_SESSION['success'] = "Review rejected successfully.";
                } else {
                    throw new Exception("Failed to reject review");
                }
                break;

            case 'feature':
                $result = $database->execute("UPDATE reviews SET is_featured = 1 WHERE id = ? AND status = 'verified'", [$reviewId]);
                if ($result) {
                    $_SESSION['success'] = "Review marked as featured.";
                } else {
                    throw new Exception("Failed to feature review. Make sure the review is verified first.");
                }
                break;

            case 'unfeature':
                $result = $database->execute("UPDATE reviews SET is_featured = 0 WHERE id = ?", [$reviewId]);
                if ($result) {
                    $_SESSION['success'] = "Review removed from featured.";
                } else {
                    throw new Exception("Failed to unfeature review");
                }
                break;

            case 'delete':
                $result = $database->execute("DELETE FROM reviews WHERE id = ?", [$reviewId]);
                if ($result) {
                    $_SESSION['success'] = "Review deleted successfully.";
                } else {
                    throw new Exception("Failed to delete review");
                }
                break;

            default:
                throw new Exception("Invalid action");
        }

        // Redirect to prevent form resubmission
        $redirectUrl = getBasePath() . '/admin/reviews';
        if (!empty($_GET)) {
            $redirectUrl .= '?' . http_build_query($_GET);
        }
        header('Location: ' . $redirectUrl);
        exit;

    } catch (Exception $e) {
        $_SESSION['error'] = "Error: " . $e->getMessage();
        // Redirect even on error
        $redirectUrl = getBasePath() . '/admin/reviews';
        if (!empty($_GET)) {
            $redirectUrl .= '?' . http_build_query($_GET);
        }
        header('Location: ' . $redirectUrl);
        exit;
    }
}

// Get filter parameters
$search = sanitize($_GET['search'] ?? '');
$status = sanitize($_GET['status'] ?? '');
$rating = sanitize($_GET['rating'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;

// Build query conditions
$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(r.title LIKE ? OR r.comment LIKE ? OR u.name LIKE ? OR u.email LIKE ? OR s.name LIKE ? OR p.name LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

if ($status && $status !== 'all') {
    $whereConditions[] = "r.status = ?";
    $params[] = $status;
}

if ($rating) {
    $whereConditions[] = "r.rating = ?";
    $params[] = $rating;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get reviews
$offset = ($page - 1) * $limit;
$sql = "
    SELECT 
        r.*,
        u.name as customer_name,
        u.email as customer_email,
        s.name as service_name,
        p.name as package_name
    FROM reviews r
    JOIN users u ON r.customer_id = u.id
    LEFT JOIN services s ON r.service_id = s.id
    LEFT JOIN packages p ON r.package_id = p.id
    {$whereClause}
    ORDER BY r.created_at DESC
    LIMIT ? OFFSET ?
";

$allParams = array_merge($params, [$limit, $offset]);
$reviews = $database->fetchAll($sql, $allParams);

// Get total count for pagination
$countSql = "
    SELECT COUNT(*) as total
    FROM reviews r
    JOIN users u ON r.customer_id = u.id
    LEFT JOIN services s ON r.service_id = s.id
    LEFT JOIN packages p ON r.package_id = p.id
    {$whereClause}
";
$totalResult = $database->fetch($countSql, $params);
$totalReviews = $totalResult['total'];
$totalPages = ceil($totalReviews / $limit);

// Get statistics
$stats = getReviewStats();

$pageTitle = "Reviews Management";
include __DIR__ . '/../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-white">Reviews Management</h1>
                                <p class="mt-1 text-sm text-gray-300">Manage customer reviews and feedback</p>
                            </div>
                            <div class="mt-4 sm:mt-0 flex gap-3">
                                <button onclick="exportReviews()" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Export Reviews
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Success/Error Messages -->
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="mb-6 p-4 rounded-lg bg-green-100 border border-green-400 text-green-700">
                            <?= htmlspecialchars($_SESSION['success']) ?>
                        </div>
                        <?php unset($_SESSION['success']); ?>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="mb-6 p-4 rounded-lg bg-red-100 border border-red-400 text-red-700">
                            <?= htmlspecialchars($_SESSION['error']) ?>
                        </div>
                        <?php unset($_SESSION['error']); ?>
                    <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-500/20 text-blue-400">
                                <i class="fas fa-star text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-400">Total Reviews</p>
                                <p class="text-2xl font-semibold text-white"><?= number_format($stats['total_reviews'] ?? 0) ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-500/20 text-yellow-400">
                                <i class="fas fa-chart-line text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-400">Average Rating</p>
                                <p class="text-2xl font-semibold text-white"><?= $stats['average_rating'] ?? '0.0' ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-500/20 text-green-400">
                                <i class="fas fa-check-circle text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-400">Verified Reviews</p>
                                <p class="text-2xl font-semibold text-white"><?= number_format($stats['verified_reviews'] ?? 0) ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-500/20 text-purple-400">
                                <i class="fas fa-thumbs-up text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-400">Recommendation Rate</p>
                                <p class="text-2xl font-semibold text-white"><?= $stats['recommendation_rate'] ?? 0 ?>%</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <form method="GET" class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="<?= htmlspecialchars($search ?? '') ?>"
                                   placeholder="Search reviews..."
                                   class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>
                        <div>
                            <select name="status" class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">All Status</option>
                                <option value="pending" <?= ($status ?? '') === 'pending' ? 'selected' : '' ?>>Pending</option>
                                <option value="verified" <?= ($status ?? '') === 'verified' ? 'selected' : '' ?>>Verified</option>
                                <option value="rejected" <?= ($status ?? '') === 'rejected' ? 'selected' : '' ?>>Rejected</option>
                            </select>
                        </div>
                        <div>
                            <select name="rating" class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">All Ratings</option>
                                <?php for ($i = 5; $i >= 1; $i--): ?>
                                    <option value="<?= $i ?>" <?= ($rating ?? '') == $i ? 'selected' : '' ?>><?= $i ?> Stars</option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Filter
                        </button>
                        <?php if (($search ?? '') || ($status ?? '') || ($rating ?? '')): ?>
                            <a href="<?= getBasePath() ?>/admin/reviews" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                Clear
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Reviews Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                    <?php if (empty($reviews)): ?>
                        <div class="col-span-full bg-secondary-800 rounded-lg p-8 text-center">
                            <div class="text-gray-400">
                                <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                </svg>
                                <p class="text-lg font-medium">No reviews found</p>
                                <p class="text-sm">No reviews match your current criteria.</p>
                            </div>
                        </div>
                    <?php else: ?>
                                        <?php foreach ($reviews as $review): ?>
                            <div class="bg-secondary-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                                <div class="p-6">
                                    <!-- Customer Info -->
                                    <div class="flex items-center mb-4">
                                        <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                            <span class="text-salon-gold font-semibold">
                                                <?= strtoupper(substr($review['customer_name'] ?? 'U', 0, 1)) ?>
                                            </span>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-white"><?= htmlspecialchars($review['customer_name'] ?? 'Unknown') ?></div>
                                            <div class="text-xs text-gray-400"><?= htmlspecialchars($review['customer_email'] ?? '') ?></div>
                                        </div>
                                        <div class="ml-auto">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php
                                                switch ($review['status'] ?? 'pending') {
                                                    case 'verified':
                                                        echo 'bg-green-100 text-green-800';
                                                        break;
                                                    case 'pending':
                                                        echo 'bg-yellow-100 text-yellow-800';
                                                        break;
                                                    case 'rejected':
                                                        echo 'bg-red-100 text-red-800';
                                                        break;
                                                    default:
                                                        echo 'bg-gray-100 text-gray-800';
                                                }
                                                ?>">
                                                <?= ucfirst($review['status'] ?? 'pending') ?>
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Service/Package Info -->
                                    <div class="mb-3">
                                        <div class="text-sm text-white font-medium">
                                            <?= htmlspecialchars($review['service_name'] ?? $review['package_name'] ?? 'N/A') ?>
                                        </div>
                                        <div class="text-xs text-gray-400 flex items-center">
                                            <?= ($review['service_name'] ?? false) ? 'Service' : 'Package' ?>
                                            <?php if ($review['is_verified'] ?? false): ?>
                                                <span class="ml-2 text-green-400">
                                                    <i class="fas fa-check-circle"></i> Verified
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($review['is_featured'] ?? false): ?>
                                                <span class="ml-2 text-purple-400">
                                                    <i class="fas fa-star"></i> Featured
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Rating -->
                                    <div class="flex items-center mb-3">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star <?= $i <= ($review['rating'] ?? 0) ? 'text-salon-gold' : 'text-gray-400' ?> text-sm"></i>
                                        <?php endfor; ?>
                                        <span class="ml-2 text-sm text-gray-300"><?= $review['rating'] ?? 0 ?>/5</span>
                                        <span class="ml-auto text-xs text-gray-400"><?= date('M j, Y', strtotime($review['created_at'] ?? 'now')) ?></span>
                                    </div>

                                    <!-- Review Content -->
                                    <div class="mb-4">
                                        <h4 class="text-sm font-medium text-white mb-1"><?= htmlspecialchars($review['title'] ?? 'No title') ?></h4>
                                        <p class="text-sm text-gray-300 line-clamp-3">
                                            <?= htmlspecialchars($review['comment'] ?? 'No comment') ?>
                                        </p>
                                    </div>
                                                    <!-- Action Buttons -->
                                    <div class="flex gap-2">
                                        <?php if (($review['status'] ?? 'pending') === 'pending'): ?>
                                            <form method="POST" class="flex-1">
                                                <input type="hidden" name="action" value="approve">
                                                <input type="hidden" name="review_id" value="<?= $review['id'] ?? '' ?>">
                                                <button type="submit" class="w-full bg-green-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors">
                                                    <i class="fas fa-check mr-1"></i>Approve
                                                </button>
                                            </form>
                                            <form method="POST" class="flex-1">
                                                <input type="hidden" name="action" value="reject">
                                                <input type="hidden" name="review_id" value="<?= $review['id'] ?? '' ?>">
                                                <button type="submit" class="w-full bg-red-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors">
                                                    <i class="fas fa-times mr-1"></i>Reject
                                                </button>
                                            </form>
                                        <?php endif; ?>

                                        <?php if (($review['status'] ?? '') === 'verified'): ?>
                                            <?php if (!($review['is_featured'] ?? false)): ?>
                                                <form method="POST" class="flex-1">
                                                    <input type="hidden" name="action" value="feature">
                                                    <input type="hidden" name="review_id" value="<?= $review['id'] ?? '' ?>">
                                                    <button type="submit" class="w-full bg-purple-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors">
                                                        <i class="fas fa-star mr-1"></i>Feature
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <form method="POST" class="flex-1">
                                                    <input type="hidden" name="action" value="unfeature">
                                                    <input type="hidden" name="review_id" value="<?= $review['id'] ?? '' ?>">
                                                    <button type="submit" class="w-full bg-gray-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors">
                                                        <i class="fas fa-star-half-alt mr-1"></i>Unfeature
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <form method="POST" onsubmit="return confirm('Are you sure you want to delete this review?')">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="review_id" value="<?= $review['id'] ?? '' ?>">
                                            <button type="submit" class="bg-red-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Pagination -->
                <?php if (($totalPages ?? 1) > 1): ?>
                    <div class="bg-secondary-800 px-4 py-3 flex items-center justify-between border-t border-secondary-700 sm:px-6 rounded-lg">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <?php if (($page ?? 1) > 1): ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['page' => ($page ?? 1) - 1])) ?>"
                                   class="relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                    Previous
                                </a>
                            <?php endif; ?>
                            <?php if (($page ?? 1) < ($totalPages ?? 1)): ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['page' => ($page ?? 1) + 1])) ?>"
                                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                    Next
                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-300">
                                    Showing <span class="font-medium"><?= (($page ?? 1) - 1) * ($limit ?? 10) + 1 ?></span> to <span class="font-medium"><?= min(($page ?? 1) * ($limit ?? 10), ($totalReviews ?? 0)) ?></span> of <span class="font-medium"><?= $totalReviews ?? 0 ?></span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                    <?php for ($i = 1; $i <= ($totalPages ?? 1); $i++): ?>
                                        <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"
                                           class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?= $i === ($page ?? 1) ? 'z-10 bg-salon-gold border-salon-gold text-black' : 'bg-secondary-700 border-secondary-600 text-gray-300 hover:bg-secondary-600' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>
                                </nav>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../includes/admin_footer.php'; ?>
