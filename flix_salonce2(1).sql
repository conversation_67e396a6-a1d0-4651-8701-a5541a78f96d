-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 04, 2025 at 02:24 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `flix_salonce2`
--

-- --------------------------------------------------------

--
-- Table structure for table `blog`
--

CREATE TABLE `blog` (
  `id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `excerpt` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_published` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE `bookings` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `service_id` varchar(36) DEFAULT NULL,
  `package_id` varchar(36) DEFAULT NULL,
  `staff_id` varchar(36) DEFAULT NULL,
  `offer_id` varchar(36) DEFAULT NULL,
  `date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `status` enum('PENDING','CONFIRMED','IN_PROGRESS','COMPLETED','CANCELLED','NO_SHOW','EXPIRED') DEFAULT 'PENDING',
  `total_amount` decimal(10,2) NOT NULL,
  `points_used` int(11) DEFAULT 0,
  `points_earned` int(11) DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `bookings`
--

INSERT INTO `bookings` (`id`, `user_id`, `service_id`, `package_id`, `staff_id`, `offer_id`, `date`, `start_time`, `end_time`, `status`, `total_amount`, `points_used`, `points_earned`, `notes`, `created_at`, `updated_at`) VALUES
('017f50dc-a285-4279-b031-9930af51b0f8', '6085b727-0ac8-4d95-b10f-c6e766420e0c', 'c001781b-954d-4ca6-a48a-66e20b6b1ed7', NULL, '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, '2025-06-05', '23:00:00', '00:00:00', 'PENDING', 45.00, 0, 0, '', '2025-06-03 01:25:16', '2025-06-03 21:12:26'),
('14acbd0c-9ac2-4e2f-acd9-7b43f387d8da', '1ec02ea0-8b70-44b0-bf07-c59a279dae8d', '6000b747-3dae-4dcf-bb6f-e05e9fa0b10f', NULL, '8c42848c-6123-4438-9f23-8f5c07680371', NULL, '2025-06-05', '14:45:00', '15:45:00', 'PENDING', 55.00, 0, 0, '', '2025-06-03 01:11:59', '2025-06-03 21:10:47'),
('20c27171-cd79-4d28-8a88-75ae1cbcfc19', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', '389f948c-141b-4fa3-9ec1-c1887d9ed745', NULL, '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, '2025-06-10', '14:50:00', '15:12:00', 'COMPLETED', 343.00, 0, 0, '', '2025-06-03 02:28:19', '2025-06-03 19:53:13'),
('a60757d5-a9be-41c6-b78c-9ea28ea5b0ed', '1ec02ea0-8b70-44b0-bf07-c59a279dae8d', '7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', NULL, '8c42848c-6123-4438-9f23-8f5c07680371', NULL, '2025-06-11', '14:34:00', '15:49:00', 'PENDING', 95.00, 0, 0, '', '2025-06-03 19:55:32', '2025-06-03 20:11:41'),
('b6b42f70-d97f-479c-b29c-c9fa8f5da1d5', '350e5a4d-da0d-4c42-ab08-302eb513c732', 'c001781b-954d-4ca6-a48a-66e20b6b1ed7', NULL, '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, '2025-06-10', '12:03:00', '13:03:00', 'COMPLETED', 45.00, 0, 0, '', '2025-06-03 01:11:26', '2025-06-03 11:06:24');

-- --------------------------------------------------------

--
-- Table structure for table `booking_reminders`
--

CREATE TABLE `booking_reminders` (
  `id` varchar(36) NOT NULL,
  `booking_id` varchar(36) NOT NULL,
  `reminder_type` enum('BOOKING_REMINDER_24H','BOOKING_REMINDER_5H','BOOKING_REMINDER_30M','BOOKING_REMINDER_NOW') NOT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `booking_status_log`
--

CREATE TABLE `booking_status_log` (
  `id` varchar(36) NOT NULL,
  `booking_id` varchar(36) NOT NULL,
  `old_status` varchar(20) NOT NULL,
  `new_status` varchar(20) NOT NULL,
  `changed_by` varchar(36) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cms_content`
--

CREATE TABLE `cms_content` (
  `id` varchar(36) NOT NULL,
  `section` varchar(100) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `order_index` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cms_content`
--

INSERT INTO `cms_content` (`id`, `section`, `title`, `content`, `image`, `order_index`, `is_active`, `created_at`, `updated_at`) VALUES
('1614ac99-e2f0-4a1b-90d8-292b34ab11e9', 'hero', 'Welcome to Flix Salonce', 'Experience luxury and elegance at our premium beauty salon', NULL, 1, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('5cb3b7c9-110e-498d-9544-3229756b3f48', 'mission', 'Our Mission', 'To enhance natural beauty and boost confidence through professional beauty services', NULL, 1, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('a92aa99e-c89b-43d5-a1b5-5c3230200795', 'about', 'About Our Salon', 'We are dedicated to providing exceptional beauty services in a luxurious environment', NULL, 1, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02');

-- --------------------------------------------------------

--
-- Table structure for table `customer_messages`
--

CREATE TABLE `customer_messages` (
  `id` varchar(36) NOT NULL,
  `customer_id` varchar(36) NOT NULL,
  `admin_id` varchar(36) NOT NULL,
  `message_type` enum('email','sms','both') NOT NULL DEFAULT 'email',
  `subject` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `status` enum('SENT','DELIVERED','FAILED','PENDING') NOT NULL DEFAULT 'PENDING',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gallery`
--

CREATE TABLE `gallery` (
  `id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image_url` varchar(255) NOT NULL,
  `category` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `gallery`
--

INSERT INTO `gallery` (`id`, `title`, `description`, `image_url`, `category`, `is_active`, `created_at`, `updated_at`) VALUES
('203ba46b-f6f9-40ae-82be-bc70d7af0d5f', 'Beauty Treatment', 'Luxury beauty service', 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=600', 'Beauty', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('3092b133-ccdc-43e9-8b47-197c866354b9', 'Facial Treatment', 'Relaxing facial session', 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=600', 'Facial', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('46b0e2ae-0076-4419-b6d4-3b2207a5ca9a', 'Makeup Application', 'Professional makeup', 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=600', 'Beauty', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('7245c1a5-f08e-4a83-a9b1-ffa3d2d25c07', 'Salon Interior', 'Modern salon space', 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=600', 'Interior', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('8d967d81-4c0c-4529-8ac5-9fd501bc6f23', 'Hair Coloring', 'Professional hair coloring', 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=600', 'Hair', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('b46b7efc-41e9-4f53-a2f3-b1924989b237', 'Nail Art Design', 'Creative nail art', 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=600', 'Nails', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('da84b87c-552e-4ba3-bc7f-574ff41fe3c1', 'Massage Therapy', 'Therapeutic massage session', 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=600', 'Massage', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('f4c968f3-40ae-45b2-a1df-0f78a85e1af9', 'Hair Styling Work', 'Beautiful hair transformation', 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=600', 'Hair', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02');

-- --------------------------------------------------------

--
-- Table structure for table `newsletter`
--

CREATE TABLE `newsletter` (
  `id` varchar(36) NOT NULL,
  `email` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `newsletter`
--

INSERT INTO `newsletter` (`id`, `email`, `is_active`, `created_at`) VALUES
('', '<EMAIL>', 1, '2025-06-01 01:51:19');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('BOOKING_NEW','BOOKING_CONFIRMED','BOOKING_CANCELLED','BOOKING_COMPLETED','BOOKING_REMINDER','BOOKING_EXPIRED','BOOKING_NO_SHOW','BOOKING_REMINDER_24H','BOOKING_REMINDER_5H','BOOKING_REMINDER_30M','BOOKING_REMINDER_NOW','CUSTOMER_NEW','CUSTOMER_BIRTHDAY','NEWSLETTER_SUBSCRIBER','PAYMENT_SUCCESS','PAYMENT_FAILED','REFUND_PROCESSED','STAFF_NEW','STAFF_SCHEDULE_CHANGE','STAFF_LEAVE_REQUEST','SYSTEM_MAINTENANCE','SYSTEM_UPDATE','SYSTEM_BACKUP','PROMOTION_NEW','OFFER_EXPIRING','LOYALTY_MILESTONE','REVIEW_NEW','COMPLAINT_NEW','GENERAL','ADMIN_STAFF_ASSIGNED','ADMIN_STAFF_UNASSIGNED','ADMIN_BOOKING_UPDATED','ADMIN_STATUS_CHANGED','ADMIN_BOOKING_RESCHEDULED','ADMIN_BOOKING_DETAILS_CHANGED') NOT NULL,
  `category` enum('BOOKING','CUSTOMER','STAFF','PAYMENT','SYSTEM','MARKETING','FEEDBACK') NOT NULL,
  `priority` enum('LOW','MEDIUM','HIGH','URGENT') DEFAULT 'MEDIUM',
  `is_read` tinyint(1) DEFAULT 0,
  `action_url` varchar(500) DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `category`, `priority`, `is_read`, `action_url`, `metadata`, `expires_at`, `created_at`, `updated_at`) VALUES
('08e1ede7-58bf-4c0c-b350-d89521a45d88', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'New Booking Request', 'Craq Venture has requested a booking for Gel Manicure on 2025-06-18 at 14:45:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 1, '/admin/bookings/view.php?id=14acbd0c-9ac2-4e2f-acd9-7b43f387d8da', '{\"booking_id\":\"14acbd0c-9ac2-4e2f-acd9-7b43f387d8da\",\"customer_name\":\"Craq Venture\",\"service_name\":\"Gel Manicure\",\"staff_name\":\"Staff Member\",\"booking_date\":\"2025-06-18\",\"booking_time\":\"14:45:00\"}', NULL, '2025-06-03 01:11:59', '2025-06-03 10:36:35'),
('0d04aea5-1c41-4e33-a210-c87d868314ac', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'New Booking Request', 'John Customer has requested a booking for Hair Cut & Style on 2025-06-10 at 12:03:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 1, '/admin/bookings/view.php?id=b6b42f70-d97f-479c-b29c-c9fa8f5da1d5', '{\"booking_id\":\"b6b42f70-d97f-479c-b29c-c9fa8f5da1d5\",\"customer_name\":\"John Customer\",\"service_name\":\"Hair Cut & Style\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-10\",\"booking_time\":\"12:03:00\"}', NULL, '2025-06-03 01:11:26', '2025-06-03 10:36:35'),
('35cb0467-dafe-4a8c-89ab-f351df7a829b', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'Booking Rescheduled by Admin', 'Admin Flix Admin has rescheduled your booking with Ailya Hassan (Test Package with Image). Changes: Date changed from 2025-06-20 to 2025-06-04', '', 'SYSTEM', 'HIGH', 1, '/staff/appointments?date=2025-06-04', '{\"booking_id\":\"211d2d09-26cb-470c-a30b-11f41497efc7\",\"customer_name\":\"Ailya Hassan\",\"service_name\":\"Test Package with Image\",\"staff_name\":\"chaz vet\",\"admin_name\":\"Flix Admin\",\"booking_date\":\"2025-06-04\",\"booking_time\":\"14:00:00\",\"admin_triggered\":true,\"changes\":{\"date\":{\"old\":\"2025-06-20\",\"new\":\"2025-06-04\"}}}', NULL, '2025-06-03 20:13:47', '2025-06-03 20:14:43'),
('74749452-33ce-43df-99b6-4bc1727a482e', '8c42848c-6123-4438-9f23-8f5c07680371', 'New Assignment from Admin', 'Admin Flix Admin has assigned you to a booking with Craq Venture for Deep Tissue Massage on 2025-06-11 at 14:34:00', '', 'SYSTEM', 'HIGH', 0, '/staff/appointments?date=2025-06-11', '{\"booking_id\":\"a60757d5-a9be-41c6-b78c-9ea28ea5b0ed\",\"customer_name\":\"Craq Venture\",\"service_name\":\"Deep Tissue Massage\",\"staff_name\":\"Staff Member\",\"admin_name\":\"Flix Admin\",\"booking_date\":\"2025-06-11\",\"booking_time\":\"14:34:00\",\"admin_triggered\":true}', NULL, '2025-06-03 20:11:41', '2025-06-03 20:11:41'),
('74c83115-86c9-4f47-bc19-84e5ee7ec752', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'New Booking Request', 'mmbaku the great has requested a booking for Craq Venture2 on 2025-06-10 at 14:50:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 1, '/admin/bookings/view.php?id=20c27171-cd79-4d28-8a88-75ae1cbcfc19', '{\"booking_id\":\"20c27171-cd79-4d28-8a88-75ae1cbcfc19\",\"customer_name\":\"mmbaku the great\",\"service_name\":\"Craq Venture2\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-10\",\"booking_time\":\"14:50:00\"}', NULL, '2025-06-03 02:28:19', '2025-06-03 19:49:06'),
('89ed9536-3ef8-4ffc-a542-a971774125c9', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'New Customer Registration', 'New customer mmbaku the great (<EMAIL>) has registered', 'CUSTOMER_NEW', 'CUSTOMER', 'MEDIUM', 1, '/admin/customers/view.php?id=bd021aa2-59cd-4423-97c2-a4e7698460a3', '{\"customer_id\":\"bd021aa2-59cd-4423-97c2-a4e7698460a3\",\"customer_name\":\"mmbaku the great\",\"customer_email\":\"<EMAIL>\"}', NULL, '2025-06-03 02:13:54', '2025-06-03 10:36:35'),
('8b627da4-74eb-4b35-a395-4d0acd65af24', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'Booking Rescheduled by Admin', 'Admin Flix Admin has rescheduled your booking with Lisa Garcia (Hair Cut & Style). Changes: Date changed from 2025-06-25 to 2025-06-05, Time changed from 23:02:00 to 13:02:00', '', 'SYSTEM', 'HIGH', 0, '/staff/appointments?date=2025-06-05', '{\"booking_id\":\"017f50dc-a285-4279-b031-9930af51b0f8\",\"customer_name\":\"Lisa Garcia\",\"service_name\":\"Hair Cut & Style\",\"staff_name\":\"chaz vet\",\"admin_name\":\"Flix Admin\",\"booking_date\":\"2025-06-05\",\"booking_time\":\"13:02:00\",\"admin_triggered\":true,\"changes\":{\"date\":{\"old\":\"2025-06-25\",\"new\":\"2025-06-05\"},\"start_time\":{\"old\":\"23:02:00\",\"new\":\"13:02:00\"}}}', NULL, '2025-06-03 20:25:33', '2025-06-03 20:25:33'),
('8bb4bfcd-9ee2-4dd4-ac3e-3e22dd3cc41e', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'New Booking Request', 'Lisa Garcia has requested a booking for Hair Cut & Style on 2025-06-25 at 23:02:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 1, '/admin/bookings/view.php?id=017f50dc-a285-4279-b031-9930af51b0f8', '{\"booking_id\":\"017f50dc-a285-4279-b031-9930af51b0f8\",\"customer_name\":\"Lisa Garcia\",\"service_name\":\"Hair Cut & Style\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-25\",\"booking_time\":\"23:02:00\"}', NULL, '2025-06-03 01:25:16', '2025-06-03 10:36:35'),
('961346c0-19dd-49a1-8b82-98c18ed8353e', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'Booking Rescheduled by Admin', 'Admin Flix Admin has rescheduled your booking with Lisa Garcia (Hair Cut & Style). Changes: Time changed from 13:02:00 to 23:00:00', 'ADMIN_BOOKING_RESCHEDULED', 'SYSTEM', 'HIGH', 1, '/staff/appointments?date=2025-06-05', '{\"booking_id\":\"017f50dc-a285-4279-b031-9930af51b0f8\",\"customer_name\":\"Lisa Garcia\",\"service_name\":\"Hair Cut & Style\",\"staff_name\":\"chaz vet\",\"admin_name\":\"Flix Admin\",\"booking_date\":\"2025-06-05\",\"booking_time\":\"23:00:00\",\"admin_triggered\":true,\"changes\":{\"start_time\":{\"old\":\"13:02:00\",\"new\":\"23:00:00\"}}}', NULL, '2025-06-03 21:12:26', '2025-06-03 21:20:31'),
('971607cb-4404-4198-806a-64e28d59903a', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'New Booking Request', 'mmbaku the great has requested a booking for Test Package with Image on 2025-06-20 at 00:00:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 1, '/admin/bookings/view.php?id=211d2d09-26cb-470c-a30b-11f41497efc7', '{\"booking_id\":\"211d2d09-26cb-470c-a30b-11f41497efc7\",\"customer_name\":\"mmbaku the great\",\"service_name\":\"Test Package with Image\",\"staff_name\":\"Staff Member\",\"booking_date\":\"2025-06-20\",\"booking_time\":\"00:00:00\"}', NULL, '2025-06-03 02:14:33', '2025-06-03 10:36:35'),
('97e039e3-b25f-47bc-93d9-4ad984b85e9a', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'Booking Confirmed', 'Booking for Ailya Hassan (Test Package with Image) has been confirmed for 2025-06-20 at 00:00:00', 'BOOKING_CONFIRMED', 'BOOKING', 'MEDIUM', 0, '/admin/bookings/view.php?id=211d2d09-26cb-470c-a30b-11f41497efc7', '{\"booking_id\":\"211d2d09-26cb-470c-a30b-11f41497efc7\",\"customer_name\":\"Ailya Hassan\",\"service_name\":\"Test Package with Image\",\"staff_name\":\"Staff Member\",\"booking_date\":\"2025-06-20\",\"booking_time\":\"00:00:00\"}', NULL, '2025-06-03 10:40:32', '2025-06-03 10:40:32'),
('a088b8a1-9e2c-487f-a137-b023a4e91b66', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'New Booking Request', 'Craq Venture has requested a booking for Deep Tissue Massage on 2025-06-11 at 14:34:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/admin/bookings/view.php?id=a60757d5-a9be-41c6-b78c-9ea28ea5b0ed', '{\"booking_id\":\"a60757d5-a9be-41c6-b78c-9ea28ea5b0ed\",\"customer_name\":\"Craq Venture\",\"service_name\":\"Deep Tissue Massage\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-11\",\"booking_time\":\"14:34:00\"}', NULL, '2025-06-03 19:55:32', '2025-06-03 19:55:32'),
('a9605a43-b61c-475c-896a-a4a02897d4bf', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'Assignment Removed by Admin', 'Admin Flix Admin has removed you from the booking with Craq Venture for Deep Tissue Massage on 2025-06-11', '', 'SYSTEM', 'MEDIUM', 1, '/staff/appointments?date=2025-06-11', '{\"booking_id\":\"a60757d5-a9be-41c6-b78c-9ea28ea5b0ed\",\"customer_name\":\"Craq Venture\",\"service_name\":\"Deep Tissue Massage\",\"staff_name\":\"chaz vet\",\"admin_name\":\"Flix Admin\",\"booking_date\":\"2025-06-11\",\"booking_time\":\"14:34:00\",\"admin_triggered\":true,\"old_staff_id\":\"3d2c13a0-d867-452d-a8e6-87dc6f1433bf\"}', NULL, '2025-06-03 20:11:41', '2025-06-03 20:20:36'),
('b2cf24fc-f9dc-48f8-b7ae-138778bedb49', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with Craq Venture for Deep Tissue Massage on 2025-06-11 at 14:34:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 1, '/staff/appointments?date=2025-06-11', '{\"booking_id\":\"a60757d5-a9be-41c6-b78c-9ea28ea5b0ed\",\"customer_name\":\"Craq Venture\",\"service_name\":\"Deep Tissue Massage\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-11\",\"booking_time\":\"14:34:00\"}', NULL, '2025-06-03 19:55:32', '2025-06-03 19:57:35'),
('c3f46c12-fa5f-4d03-babd-3770c00584a9', '8c42848c-6123-4438-9f23-8f5c07680371', 'Booking Rescheduled by Admin', 'Admin Flix Admin has rescheduled your booking with Craq Venture (Gel Manicure). Changes: Date changed from 2025-06-19 to 2025-06-05', 'ADMIN_BOOKING_RESCHEDULED', 'SYSTEM', 'HIGH', 0, '/staff/appointments?date=2025-06-05', '{\"booking_id\":\"14acbd0c-9ac2-4e2f-acd9-7b43f387d8da\",\"customer_name\":\"Craq Venture\",\"service_name\":\"Gel Manicure\",\"staff_name\":\"Staff Member\",\"admin_name\":\"Flix Admin\",\"booking_date\":\"2025-06-05\",\"booking_time\":\"14:45:00\",\"admin_triggered\":true,\"changes\":{\"date\":{\"old\":\"2025-06-19\",\"new\":\"2025-06-05\"}}}', NULL, '2025-06-03 21:10:47', '2025-06-03 21:10:47'),
('d192e46c-c71a-4e36-9d3e-865e6348b63c', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'New Booking Request', 'John Customer has requested a booking for Gel Manicure on 2025-06-29 at 00:00:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 1, '/admin/bookings/view.php?id=34667b71-751f-4b46-8600-9defde4e793e', '{\"booking_id\":\"34667b71-751f-4b46-8600-9defde4e793e\",\"customer_name\":\"John Customer\",\"service_name\":\"Gel Manicure\",\"staff_name\":\"Staff Member\",\"booking_date\":\"2025-06-29\",\"booking_time\":\"00:00:00\"}', NULL, '2025-06-03 01:13:46', '2025-06-03 10:36:35');

-- --------------------------------------------------------

--
-- Table structure for table `offers`
--

CREATE TABLE `offers` (
  `id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `discount` decimal(5,2) NOT NULL,
  `code` varchar(50) NOT NULL,
  `valid_from` datetime NOT NULL,
  `valid_to` datetime NOT NULL,
  `max_usage` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `offers`
--

INSERT INTO `offers` (`id`, `title`, `description`, `discount`, `code`, `valid_from`, `valid_to`, `max_usage`, `is_active`, `image`, `created_at`, `updated_at`) VALUES
('b93eead3-2c1b-46cc-bc52-4003ea3b8ab7', 'New Customer Special', 'Get 20% off your first visits', 20.00, 'WELCOME20', '2025-06-01 00:00:00', '2025-09-01 00:00:00', 100, 1, 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400', '2025-06-01 00:06:02', '2025-06-01 15:38:47'),
('c259412c-36cf-4138-ad4f-746eb31add9b', 'Referral Bonus', 'Refer a friend and both get 15% off', 15.00, 'REFER15', '2025-06-01 03:06:02', '2025-12-01 03:06:02', NULL, 1, 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('cf2926c4-21f0-4bd3-8ad4-93fa338810e3', 'Summer Glow Package', 'Special summer facial and hair treatment combo', 25.00, 'SUMMER25', '2025-06-01 03:06:02', '2025-08-01 03:06:02', 50, 1, 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400', '2025-06-01 00:06:02', '2025-06-01 00:06:02');

-- --------------------------------------------------------

--
-- Table structure for table `packages`
--

CREATE TABLE `packages` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `discount` decimal(5,2) DEFAULT 0.00,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `packages`
--

INSERT INTO `packages` (`id`, `name`, `description`, `price`, `discount`, `image`, `is_active`, `created_at`, `updated_at`) VALUES
('97a321b1-9598-4831-b4eb-ea1f92a70903', 'Test Package with Image', 'Test package for debugging image functionality', 99.99, 0.00, 'https://img.freepik.com/free-photo/make-up-artist-getting-model-ready-photoshootin_23-2149305147.jpg', 1, '2025-06-01 16:18:06', '2025-06-01 16:33:01'),
('a0f1c3f8-f561-4aa8-af25-59d8ac475cce', 'Hair Transformation', 'Complete hair makeover with cut, color, and styling', 150.00, 10.00, 'https://img.freepik.com/free-photo/make-up-artist-getting-model-ready-photoshootin_23-2149305132.jpg', 1, '2025-06-01 00:06:02', '2025-06-01 16:41:43'),
('b7306f8c-e63e-4fa2-a761-2b47a94e9034', 'chaz', 'dcdsc', 222.86, 0.00, 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?fm=jpg&amp;amp;q=60&amp;amp;w=3000&amp;amp;ixlib=rb-4.1.0&amp;amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D', 1, '2025-06-01 15:56:06', '2025-06-01 16:33:58');

-- --------------------------------------------------------

--
-- Table structure for table `package_services`
--

CREATE TABLE `package_services` (
  `id` varchar(36) NOT NULL,
  `package_id` varchar(36) NOT NULL,
  `service_id` varchar(36) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `package_services`
--

INSERT INTO `package_services` (`id`, `package_id`, `service_id`, `created_at`) VALUES
('025bd55b-f72a-4e3a-97ad-5ed75a839dbd', 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', '386ff31e-0274-443f-8807-53202fa0b7fe', '2025-06-01 16:41:43'),
('27757780-88d9-4d5b-b0ba-0bd96c923dc8', 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', '7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', '2025-06-01 16:41:43'),
('2d6f8983-8e5c-4537-a716-15e2c9ec0d41', '97a321b1-9598-4831-b4eb-ea1f92a70903', '3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', '2025-06-01 16:33:01'),
('47ff5d34-5f5f-409b-9aed-55aa623bccbd', 'b7306f8c-e63e-4fa2-a761-2b47a94e9034', '389f948c-141b-4fa3-9ec1-c1887d9ed745', '2025-06-01 16:33:58'),
('90f21688-174b-4478-b130-da26cb09fd41', 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', 'cf8fe43b-9e8e-4d0b-a5c9-bfed2c9f5816', '2025-06-01 16:41:43'),
('a1a304cd-5200-4865-be42-aae99bdaaa4a', 'b7306f8c-e63e-4fa2-a761-2b47a94e9034', '7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', '2025-06-01 16:33:58'),
('dcd68cf7-259b-4938-9285-a933e74f21a4', 'b7306f8c-e63e-4fa2-a761-2b47a94e9034', 'cf8fe43b-9e8e-4d0b-a5c9-bfed2c9f5816', '2025-06-01 16:33:58'),
('f94033e0-874b-470c-a81d-3af03f50eda4', '97a321b1-9598-4831-b4eb-ea1f92a70903', '386ff31e-0274-443f-8807-53202fa0b7fe', '2025-06-01 16:33:01');

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `id` varchar(36) NOT NULL,
  `booking_id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'USD',
  `status` enum('PENDING','COMPLETED','FAILED','REFUNDED') DEFAULT 'PENDING',
  `payment_method` varchar(50) DEFAULT 'card',
  `stripe_payment_id` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `point_transactions`
--

CREATE TABLE `point_transactions` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `points` int(11) NOT NULL,
  `type` enum('EARNED','REDEMPTION','BONUS','REFUND') NOT NULL,
  `description` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `booking_id` varchar(36) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reviews`
--

CREATE TABLE `reviews` (
  `id` varchar(36) NOT NULL,
  `booking_id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `staff_id` varchar(36) DEFAULT NULL,
  `rating` int(11) NOT NULL CHECK (`rating` >= 1 and `rating` <= 5),
  `comment` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `rewards`
--

CREATE TABLE `rewards` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `points_required` int(11) NOT NULL,
  `value` decimal(10,2) NOT NULL,
  `type` enum('discount','service','product','bonus') DEFAULT 'discount',
  `is_active` tinyint(1) DEFAULT 1,
  `max_usage` int(11) DEFAULT NULL,
  `usage_count` int(11) DEFAULT 0,
  `image` varchar(255) DEFAULT NULL,
  `terms_conditions` text DEFAULT NULL,
  `valid_from` date DEFAULT NULL,
  `valid_to` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `rewards`
--

INSERT INTO `rewards` (`id`, `name`, `description`, `points_required`, `value`, `type`, `is_active`, `max_usage`, `usage_count`, `image`, `terms_conditions`, `valid_from`, `valid_to`, `created_at`, `updated_at`) VALUES
('reward-1', 'TSH 5,000 Service Discount', 'Get TSH 5,000 off any service booking', 500, 5000.00, 'discount', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-2', 'TSH 10,000 Service Discount', 'Get TSH 10,000 off any service booking', 1000, 10000.00, 'discount', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-3', 'Free Basic Manicure', 'Complimentary basic manicure service', 1500, 25000.00, 'service', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-4', 'TSH 20,000 Package Discount', 'Get TSH 20,000 off any package booking', 2000, 20000.00, 'discount', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-5', 'Free Hair Wash & Blow Dry', 'Complimentary hair wash and blow dry service', 800, 15000.00, 'service', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-6', 'VIP Treatment Upgrade', 'Upgrade any service to VIP treatment', 1200, 30000.00, 'bonus', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-7', 'Birthday Special Package', 'Special birthday package with multiple services', 3000, 50000.00, 'service', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-8', 'TSH 15,000 Service Discount', 'Get TSH 15,000 off any service booking', 1500, 15000.00, 'discount', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44');

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `duration` int(11) NOT NULL COMMENT 'Duration in minutes',
  `category` varchar(100) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `services`
--

INSERT INTO `services` (`id`, `name`, `description`, `price`, `duration`, `category`, `image`, `is_active`, `created_at`, `updated_at`) VALUES
('3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', 'Pedicure new', 'Relaxing pedicure service', 45.00, 60, 'Nails', 'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 15:17:41'),
('386ff31e-0274-443f-8807-53202fa0b7fe', 'Facial Treatment', 'Deep cleansing facial treatment', 65.00, 75, 'Facial', 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('389f948c-141b-4fa3-9ec1-c1887d9ed745', 'Craq Venture2', 'ca', 343.00, 22, 'Hair', 'https://img.freepik.com/free-photo/make-up-artist-getting-model-ready-photoshootin_23-2149305116.jpg?t=st=1748796051~exp=1748799651~hmac=a3c787643f154275d3422dfa773b6f63174ba9741dac774fd9e45b5cf466084a', 1, '2025-06-01 14:46:57', '2025-06-01 20:52:18'),
('5ffce5a5-f93e-4fdd-bb66-7c54376f0fef', 'Hair Highlights', 'Professional highlighting service', 95.00, 150, 'Hair', 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('6000b747-3dae-4dcf-bb6f-e05e9fa0b10f', 'Gel Manicure', 'Long-lasting gel manicure', 55.00, 60, 'Nails', 'https://images.unsplash.com/photo-1610992015732-2449b76344bc?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', 'Deep Tissue Massage', 'Therapeutic deep tissue massage', 95.00, 75, 'Massage', 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('958b58c2-c476-463b-b7f6-12b82fda1930', 'Makeup Application', 'Professional makeup service', 75.00, 60, 'Beauty', 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('9a169567-0b73-45a4-bc1f-5aa9677c6f73', 'Swedish Massage', 'Relaxing full body massage', 80.00, 60, 'Massage', 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('bde7fc78-ba62-4f92-9e2a-81214bb5a247', 'Hair Coloring', 'Full hair coloring service', 85.00, 120, 'Hair', 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('c001781b-954d-4ca6-a48a-66e20b6b1ed7', 'Hair Cut & Style', 'Professional haircut with styling', 45.00, 60, 'Hair', 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('cf8fe43b-9e8e-4d0b-a5c9-bfed2c9f5816', 'Eyebrow Shaping', 'Professional eyebrow shaping', 25.00, 30, 'Beauty', 'https://images.unsplash.com/photo-1588681664899-f142ff2dc9b1?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('d4d35cfc-2eb6-474c-9b67-6775aadad633', 'Manicure', 'Classic manicure service', 35.00, 45, 'Nails', 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('dc20dff0-0f57-4389-953f-69e348b40a74', 'Anti-Aging Facial', 'Advanced anti-aging facial', 95.00, 90, 'Facial', 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02');

-- --------------------------------------------------------

--
-- Table structure for table `service_categories`
--

CREATE TABLE `service_categories` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `service_categories`
--

INSERT INTO `service_categories` (`id`, `name`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
('037c4a11-9a00-4c86-be46-2f15237f20b3', 'Beauty', 'Comprehensive beauty treatments and cosmetic services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('4a24a5b0-9682-4940-be09-e17034f6ddd2', 'Hair Removal', 'Professional hair removal services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('5335ca5a-8253-498c-9ab8-94eae3321c9e', 'Makeup', 'Professional makeup application and styling services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('69910667-eb76-46b6-8d51-d093fae52683', 'Eyebrows', 'Professional eyebrow shaping and styling services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('69b5a253-aa59-4f3c-8206-0889880e4544', 'Massage', 'Relaxing therapeutic massage and wellness services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('7dba5069-0c5c-451b-b081-61474a750e85', 'Lashes', 'Eyelash extensions and enhancement services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('8768082b-b036-4d2e-9e51-48385da7e4c2', 'Facial', 'Rejuvenating facial treatments and skincare services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('87e28781-979b-4259-94a0-cd2deeac8c0d', 'Hair', 'Professional hair cutting, styling, and treatment services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('bd15df65-bc22-4def-a42c-45367426bf75', 'Body Treatments', 'Full body treatments and wellness services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('e85a3548-eb2f-45d5-b3d9-113d39d7865b', 'Nails', 'Complete nail care including manicure and pedicure services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35');

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(36) NOT NULL,
  `session_token` varchar(255) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `expires` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `session_token`, `user_id`, `expires`, `created_at`) VALUES
('43384ab4-082e-44c4-9251-77d44175b369', 'ceb886c57e5d7e7eb5b15946bc591d23f24bbef17895fb0ed8d1e8f3662b0a2f', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', '2025-06-18 00:21:07', '2025-06-03 21:21:07'),
('43513b99-1d25-438f-a62d-4af0a9b4130e', 'fb2fcc555c8afd4a3462213c67b4385dd36a3bda4b0f2bc78394d8b3b351e61b', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', '2025-07-03 05:14:10', '2025-06-03 02:14:10'),
('5935e5ca-dc41-484f-bf9c-6dbc8482ea24', '928a4806c57310141ea8cb79e5981732221b35482f35a00919ade0c7a0ead6d7', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', '2025-06-17 11:03:51', '2025-06-03 08:03:51'),
('af33851a-2a90-4338-ae57-ab72bea1abe7', 'cf08c0262ebe4b804e038b56d6207331007d5acf2ede9310e5ca67ba9df21317', '350e5a4d-da0d-4c42-ab08-302eb513c732', '2025-07-03 03:31:10', '2025-06-03 00:31:10'),
('af762139-a450-4d62-96a9-6d9953a32eff', 'e426047cae0e467a9cd181b2a3c1c9ccff9f923219e47283b75295d49fc1f254', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2025-07-02 20:23:35', '2025-06-02 17:23:35'),
('c3ad8c61-2d27-4fed-973b-bc7578a80c77', '2833ce9bf5cc2c1f2f5f430d847de62e96217a05575a05db12275846af10ee27', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2025-07-03 05:00:17', '2025-06-03 02:00:17'),
('c3b4c13d-3d8a-411c-9f8a-3ebdb748e3c9', 'e79b3662368702373d003d79048fddd99b8f4175f4537ca76e72ca2de6939e34', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2025-07-03 02:04:10', '2025-06-02 23:04:10'),
('c9b37220-1639-4b60-b226-5b0467b1869e', '6ad690b65fa0dd6fc84715d9088a12408640bdc041a3c296dcb681d2a57e3608', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2025-06-18 00:17:13', '2025-06-03 19:56:43'),
('d7b87513-6aa7-4d6d-ad1d-7e320e5d1695', '6547fd06a9c46a2cb31e8e34cea2b739b152ad5fb53bc5a83db78f8bc7668294', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2025-07-02 19:33:14', '2025-06-02 16:33:14'),
('f0ec934a-1635-4dbc-b2df-a0dd5201d0c7', 'b651008520bca33caabcf8641056e59a6c08c1b5ff90a465bc9a59e28f2877c7', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', '2025-07-03 10:43:44', '2025-06-03 07:43:44');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` varchar(36) NOT NULL,
  `category` varchar(100) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` longtext NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `category`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
('009b951d-fc8e-4bb6-a7ac-31a168420107', 'business', 'address', 'Upanga, Dar Es Salaam, Tanzania', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('1a50d335-dd0a-474b-bb01-4f2a02401720', 'business', 'email', '<EMAIL>', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('28d52ba6-e191-44f0-bf19-61a226700947', 'business', 'phone', '(255) 745 456-789', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('4f40db19-8f13-4a17-9464-d8553c026191', 'points', 'redeem_rate', '100', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('71b96c2e-5914-491a-b699-f214c80001e0', 'email', 'notifications', '1', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('8e8ef51d-57a6-4165-b3e7-459c7bc82de2', 'payment', 'stripe_enabled', '1', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('a1831bd1-e677-4189-b91c-1e7bc2f1b42b', 'business', 'hours', 'Mon-Sat: 9AM-7PM, Sun: 10AM-6PM', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('b34108c4-68e1-4e5a-b407-117217b54e24', 'booking', 'advance_days', '30', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('c41ad510-e282-4790-8f15-ed58b84826f6', 'booking', 'cancellation_hours', '24', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('e415ff38-12a1-4734-bc17-f1f96d9bf964', 'business', 'name', 'Flix Salon & SPA', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('e698120e-1b3c-4a9c-a8fb-b05743345cd5', 'points', 'earn_rate', '1', '2025-06-01 00:06:02', '2025-06-01 00:06:02');

-- --------------------------------------------------------

--
-- Table structure for table `staff_schedules`
--

CREATE TABLE `staff_schedules` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `staff_id` varchar(36) DEFAULT NULL,
  `day_of_week` varchar(20) DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `is_working` tinyint(1) DEFAULT 1,
  `role` varchar(100) DEFAULT 'Staff Member',
  `hourly_rate` decimal(8,2) DEFAULT 50.00,
  `bio` text DEFAULT NULL,
  `experience` int(11) DEFAULT 0,
  `schedule` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`schedule`)),
  `specialties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`specialties`)),
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `staff_schedules`
--

INSERT INTO `staff_schedules` (`id`, `user_id`, `staff_id`, `day_of_week`, `start_time`, `end_time`, `is_working`, `role`, `hourly_rate`, `bio`, `experience`, `schedule`, `specialties`, `is_active`, `created_at`, `updated_at`) VALUES
('200ae44c-7b50-4ba5-b9b9-da6a5e3e768f', '8c42848c-6123-4438-9f23-8f5c07680371', NULL, NULL, NULL, NULL, 1, 'Staff Member', 50.00, NULL, 0, '{\"monday\":{\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"is_working\":true},\"tuesday\":{\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"is_working\":true},\"wednesday\":{\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"is_working\":true},\"thursday\":{\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"is_working\":true},\"friday\":{\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"is_working\":true},\"saturday\":{\"start_time\":\"10:00\",\"end_time\":\"16:00\",\"is_working\":true},\"sunday\":{\"start_time\":\"10:00\",\"end_time\":\"16:00\",\"is_working\":false}}', NULL, 1, '2025-06-03 09:58:48', '2025-06-03 09:58:48'),
('63ddf464-2b52-4ecc-a096-1a57832109da', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, NULL, NULL, NULL, 1, 'Staff Member', 50.00, NULL, 0, '{\"monday\":{\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"is_working\":true},\"tuesday\":{\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"is_working\":true},\"wednesday\":{\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"is_working\":true},\"thursday\":{\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"is_working\":true},\"friday\":{\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"is_working\":true},\"saturday\":{\"start_time\":\"10:00\",\"end_time\":\"16:00\",\"is_working\":true},\"sunday\":{\"start_time\":\"10:00\",\"end_time\":\"16:00\",\"is_working\":false}}', NULL, 1, '2025-06-03 09:58:48', '2025-06-03 09:58:48');

-- --------------------------------------------------------

--
-- Table structure for table `staff_specialties`
--

CREATE TABLE `staff_specialties` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `service_id` varchar(36) NOT NULL,
  `proficiency_level` enum('BEGINNER','INTERMEDIATE','ADVANCED','EXPERT') DEFAULT 'INTERMEDIATE',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `staff_specialties`
--

INSERT INTO `staff_specialties` (`id`, `user_id`, `service_id`, `proficiency_level`, `created_at`, `updated_at`) VALUES
('005c9d77-861a-4379-90b6-22b1874ac57b', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', '389f948c-141b-4fa3-9ec1-c1887d9ed745', 'INTERMEDIATE', '2025-06-03 09:58:48', '2025-06-03 09:58:48'),
('03f82138-a3cd-471a-b59d-ea19ec59594b', '8c42848c-6123-4438-9f23-8f5c07680371', '386ff31e-0274-443f-8807-53202fa0b7fe', 'INTERMEDIATE', '2025-06-03 09:58:48', '2025-06-03 09:58:48'),
('3728611b-e2f5-4267-812e-8b1f6fc42b89', '8c42848c-6123-4438-9f23-8f5c07680371', '389f948c-141b-4fa3-9ec1-c1887d9ed745', 'INTERMEDIATE', '2025-06-03 09:58:48', '2025-06-03 09:58:48'),
('463feffb-99a9-4802-94ee-1bd697e989c1', '8c42848c-6123-4438-9f23-8f5c07680371', '3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', 'INTERMEDIATE', '2025-06-03 09:58:48', '2025-06-03 09:58:48'),
('88d3486a-c8f3-4125-ae12-cad382436fe2', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', '3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', 'INTERMEDIATE', '2025-06-03 09:58:48', '2025-06-03 09:58:48'),
('fc09a828-358e-4c03-baf0-ee9c4cbdf670', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', '386ff31e-0274-443f-8807-53202fa0b7fe', 'INTERMEDIATE', '2025-06-03 09:58:48', '2025-06-03 09:58:48');

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `system_settings`
--

INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `updated_at`) VALUES
(1, 'points_migration_completed', '2025-06-02 19:19:03', '2025-06-02 16:19:03'),
(2, 'last_reminder_check', '2025-06-04 00:18:29', '2025-06-03 21:18:29');

-- --------------------------------------------------------

--
-- Stand-in structure for view `upcoming_appointments`
-- (See below for the actual view)
--
CREATE TABLE `upcoming_appointments` (
`id` varchar(36)
,`user_id` varchar(36)
,`staff_id` varchar(36)
,`date` date
,`start_time` time
,`status` enum('PENDING','CONFIRMED','IN_PROGRESS','COMPLETED','CANCELLED','NO_SHOW','EXPIRED')
,`appointment_datetime` varchar(21)
,`minutes_until` bigint(21)
,`customer_name` varchar(255)
,`customer_email` varchar(255)
,`customer_phone` varchar(20)
,`service_name` varchar(255)
,`staff_name` varchar(255)
,`package_name` varchar(255)
);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `role` enum('CUSTOMER','ADMIN','STAFF') DEFAULT 'CUSTOMER',
  `points` int(11) DEFAULT 0,
  `referral_code` varchar(10) DEFAULT NULL,
  `referred_by` varchar(36) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `image`, `password`, `phone`, `date_of_birth`, `role`, `points`, `referral_code`, `referred_by`, `created_at`, `updated_at`, `is_active`) VALUES
('1ec02ea0-8b70-44b0-bf07-c59a279dae8d', 'Craq Venture', '<EMAIL>', NULL, NULL, '$2y$12$SI95UabiDCyFg6Jgkv0ji.Z9HmP/.v8j/X.S8qwv3LXWjL3i2qrGK', '0787574355', NULL, 'CUSTOMER', 0, 'CRA550', NULL, '2025-06-02 22:14:32', '2025-06-02 22:14:32', 1),
('350e5a4d-da0d-4c42-ab08-302eb513c732', 'John Customer', '<EMAIL>', NULL, NULL, '$2y$10$QRoLGv9BUjKACGqU.jpDkOcAC8KzNfR.rkiA/o30KUfBb0L.4CZea', NULL, NULL, 'CUSTOMER', 6, 'JOH001', NULL, '2025-06-01 00:06:01', '2025-06-02 20:52:05', 1),
('3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'chaz vet', '<EMAIL>', NULL, NULL, '$2y$10$N6hEG6AWi4C5mizpmWQ/6eMhGS0EYhAB9jozdvD64BRJvqoUozBt.', '+255622518815', NULL, 'STAFF', 0, NULL, NULL, '2025-06-01 19:12:51', '2025-06-01 22:07:40', 1),
('58cea7e9-024e-45f3-9eff-cd5de4c0480b', 'Emma Davis', '<EMAIL>', NULL, NULL, '$2y$10$tf/JwzOkMZWJtAm7iJwgyeCpNOnvMMUIqs5F8lgP4OM2emovg8ap.', NULL, NULL, 'CUSTOMER', 1102, 'EMM001', NULL, '2025-06-01 00:06:02', '2025-06-01 22:36:38', 1),
('6085b727-0ac8-4d95-b10f-c6e766420e0c', 'Lisa Garcia', '<EMAIL>', NULL, NULL, '$2y$10$sl/.2ndIF2ema243FGTCH.vj2v4/0doORgF50QvcFIL8SuvT9FpqC', NULL, NULL, 'CUSTOMER', 0, 'LIS001', NULL, '2025-06-01 00:06:02', '2025-06-02 20:21:12', 1),
('6a6da56f-e15a-4895-bc08-4148d3eb2127', 'Mike Wilson', '<EMAIL>', NULL, NULL, '$2y$10$9QdLysxf.SRNUoyCnnStXu34OFdqBfBA9Di8cU2jS3FrC5QXVgGiS', NULL, NULL, 'CUSTOMER', 0, 'MIK001', NULL, '2025-06-01 00:06:02', '2025-06-02 20:22:57', 1),
('8c42848c-6123-4438-9f23-8f5c07680371', 'Staff Member', '<EMAIL>', NULL, NULL, '$2y$10$u8.tIkSJT32VptNWfpvaYeetUvB0DB4PZiz/SVY8fZHRMAMGpN7uq', NULL, NULL, 'STAFF', 0, 'STF001', NULL, '2025-06-01 00:06:01', '2025-06-01 18:28:28', 1),
('bd021aa2-59cd-4423-97c2-a4e7698460a3', 'Ailya Hassan', '<EMAIL>', NULL, 'https://img.freepik.com/free-psd/traveler-desert-hot-sun_23-2150177805.jpg?t=st=1748936874~exp=1748940474~hmac=6b09110ebc259ed8688b881bfd83c6b0b9ddca033205da520ce7fdb5713ef4fc&w=1480', '$2y$12$HiJkrbYbCQ30x3KmiP0.heOWHYaENSnn3hJbfZxRrsossBYClyhku', '0787574355', NULL, 'CUSTOMER', 0, 'MMB111', NULL, '2025-06-03 02:13:54', '2025-06-03 07:49:49', 1),
('e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'Flix Admin', '<EMAIL>', NULL, NULL, '$2y$10$YATFTcMpx0zkawHaT1cD5e62A9q7j/pHgsEsgRAuD7YMw03TFjhFa', NULL, NULL, 'ADMIN', 0, 'ADM001', NULL, '2025-06-01 00:06:01', '2025-06-03 08:21:18', 1);

-- --------------------------------------------------------

--
-- Structure for view `upcoming_appointments`
--
DROP TABLE IF EXISTS `upcoming_appointments`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `upcoming_appointments`  AS SELECT `b`.`id` AS `id`, `b`.`user_id` AS `user_id`, `b`.`staff_id` AS `staff_id`, `b`.`date` AS `date`, `b`.`start_time` AS `start_time`, `b`.`status` AS `status`, concat(`b`.`date`,' ',`b`.`start_time`) AS `appointment_datetime`, timestampdiff(MINUTE,current_timestamp(),concat(`b`.`date`,' ',`b`.`start_time`)) AS `minutes_until`, `u`.`name` AS `customer_name`, `u`.`email` AS `customer_email`, `u`.`phone` AS `customer_phone`, `s`.`name` AS `service_name`, `st`.`name` AS `staff_name`, `p`.`name` AS `package_name` FROM ((((`bookings` `b` left join `users` `u` on(`b`.`user_id` = `u`.`id`)) left join `services` `s` on(`b`.`service_id` = `s`.`id`)) left join `users` `st` on(`b`.`staff_id` = `st`.`id` and `st`.`role` = 'STAFF')) left join `packages` `p` on(`b`.`package_id` = `p`.`id`)) WHERE `b`.`status` in ('CONFIRMED','PENDING') AND concat(`b`.`date`,' ',`b`.`start_time`) > current_timestamp() ORDER BY concat(`b`.`date`,' ',`b`.`start_time`) ASC ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `blog`
--
ALTER TABLE `blog`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- Indexes for table `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `service_id` (`service_id`),
  ADD KEY `package_id` (`package_id`),
  ADD KEY `offer_id` (`offer_id`),
  ADD KEY `idx_bookings_user_id` (`user_id`),
  ADD KEY `idx_bookings_date` (`date`),
  ADD KEY `idx_bookings_status` (`status`),
  ADD KEY `bookings_staff_fk` (`staff_id`),
  ADD KEY `idx_bookings_expiration` (`status`,`date`,`start_time`),
  ADD KEY `idx_bookings_status_date` (`status`,`date`),
  ADD KEY `idx_bookings_reminder_check` (`status`,`date`,`start_time`);

--
-- Indexes for table `booking_reminders`
--
ALTER TABLE `booking_reminders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_booking_reminder` (`booking_id`,`reminder_type`),
  ADD KEY `idx_booking_reminders_booking` (`booking_id`),
  ADD KEY `idx_booking_reminders_type` (`reminder_type`),
  ADD KEY `idx_booking_reminders_sent` (`sent_at`);

--
-- Indexes for table `booking_status_log`
--
ALTER TABLE `booking_status_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `booking_id` (`booking_id`),
  ADD KEY `changed_by` (`changed_by`);

--
-- Indexes for table `cms_content`
--
ALTER TABLE `cms_content`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `customer_messages`
--
ALTER TABLE `customer_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_customer_messages_customer` (`customer_id`),
  ADD KEY `idx_customer_messages_admin` (`admin_id`),
  ADD KEY `idx_customer_messages_created` (`created_at`);

--
-- Indexes for table `gallery`
--
ALTER TABLE `gallery`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `newsletter`
--
ALTER TABLE `newsletter`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_notifications_user_id` (`user_id`),
  ADD KEY `idx_notifications_read` (`is_read`),
  ADD KEY `idx_notifications_category` (`category`),
  ADD KEY `idx_notifications_type` (`type`),
  ADD KEY `idx_notifications_priority` (`priority`),
  ADD KEY `idx_notifications_created_at` (`created_at`),
  ADD KEY `idx_notifications_expires_at` (`expires_at`);

--
-- Indexes for table `offers`
--
ALTER TABLE `offers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `packages`
--
ALTER TABLE `packages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `package_services`
--
ALTER TABLE `package_services`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_package_service` (`package_id`,`service_id`),
  ADD KEY `service_id` (`service_id`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `booking_id` (`booking_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `point_transactions`
--
ALTER TABLE `point_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `reviews`
--
ALTER TABLE `reviews`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `booking_id` (`booking_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `reviews_staff_fk` (`staff_id`);

--
-- Indexes for table `rewards`
--
ALTER TABLE `rewards`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_services_category` (`category`),
  ADD KEY `idx_services_active` (`is_active`);

--
-- Indexes for table `service_categories`
--
ALTER TABLE `service_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `session_token` (`session_token`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_setting` (`category`,`setting_key`);

--
-- Indexes for table `staff_schedules`
--
ALTER TABLE `staff_schedules`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `staff_specialties`
--
ALTER TABLE `staff_specialties`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_service` (`user_id`,`service_id`),
  ADD KEY `service_id` (`service_id`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `referral_code` (`referral_code`),
  ADD KEY `referred_by` (`referred_by`),
  ADD KEY `idx_users_email` (`email`),
  ADD KEY `idx_users_role` (`role`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `bookings`
--
ALTER TABLE `bookings`
  ADD CONSTRAINT `bookings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `bookings_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bookings_ibfk_3` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bookings_ibfk_5` FOREIGN KEY (`offer_id`) REFERENCES `offers` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bookings_staff_fk` FOREIGN KEY (`staff_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `booking_reminders`
--
ALTER TABLE `booking_reminders`
  ADD CONSTRAINT `booking_reminders_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `booking_status_log`
--
ALTER TABLE `booking_status_log`
  ADD CONSTRAINT `booking_status_log_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `booking_status_log_ibfk_2` FOREIGN KEY (`changed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `customer_messages`
--
ALTER TABLE `customer_messages`
  ADD CONSTRAINT `customer_messages_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_messages_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `package_services`
--
ALTER TABLE `package_services`
  ADD CONSTRAINT `package_services_ibfk_1` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `package_services_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `point_transactions`
--
ALTER TABLE `point_transactions`
  ADD CONSTRAINT `point_transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `reviews`
--
ALTER TABLE `reviews`
  ADD CONSTRAINT `reviews_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `reviews_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `reviews_staff_fk` FOREIGN KEY (`staff_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `sessions`
--
ALTER TABLE `sessions`
  ADD CONSTRAINT `sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `staff_schedules`
--
ALTER TABLE `staff_schedules`
  ADD CONSTRAINT `staff_schedules_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `staff_specialties`
--
ALTER TABLE `staff_specialties`
  ADD CONSTRAINT `staff_specialties_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `staff_specialties_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`referred_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
