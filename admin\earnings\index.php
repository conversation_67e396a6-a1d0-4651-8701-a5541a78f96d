<?php
/**
 * Admin Earnings & Revenue Dashboard
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/earnings_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get filters
$range = $_GET['range'] ?? 'month';
$startDate = $_GET['start_date'] ?? null;
$endDate = $_GET['end_date'] ?? null;

// Get earnings data
$earnings = getEarningsData($range, $startDate, $endDate);

// Ensure we have default values for display
$earnings['totalRevenue'] = $earnings['totalRevenue'] ?? 0;
$earnings['periodRevenue'] = $earnings['periodRevenue'] ?? 0;
$earnings['revenueGrowth'] = $earnings['revenueGrowth'] ?? 0;
$earnings['totalTransactions'] = $earnings['totalTransactions'] ?? 0;
$earnings['averageOrderValue'] = $earnings['averageOrderValue'] ?? 0;
$earnings['topServices'] = $earnings['topServices'] ?? [];
$earnings['monthlyData'] = $earnings['monthlyData'] ?? [];
$earnings['paymentMethods'] = $earnings['paymentMethods'] ?? [];
$earnings['staffEarnings'] = $earnings['staffEarnings'] ?? [];
$earnings['recentTransactions'] = $earnings['recentTransactions'] ?? [];

$pageTitle = "Earnings & Revenue";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white">Earnings & Revenue</h1>
                    <p class="mt-2 text-gray-400">Financial analytics and revenue tracking</p>
                </div>
                <div class="mt-4 sm:mt-0">
                    <div class="flex items-center space-x-4">
                        <!-- Date Range Filter -->
                        <form method="GET" class="flex items-center space-x-2">
                            <select name="range" onchange="this.form.submit()" 
                                    class="px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="today" <?= $range === 'today' ? 'selected' : '' ?>>Today</option>
                                <option value="week" <?= $range === 'week' ? 'selected' : '' ?>>This Week</option>
                                <option value="month" <?= $range === 'month' ? 'selected' : '' ?>>This Month</option>
                                <option value="year" <?= $range === 'year' ? 'selected' : '' ?>>This Year</option>
                            </select>
                        </form>
                        
                        <!-- Export Button -->
                        <button onclick="exportReport()" 
                                class="bg-salon-gold hover:bg-yellow-500 text-black px-6 py-2 rounded-lg font-semibold transition-colors">
                            <i class="fas fa-download mr-2"></i>Export Report
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Metrics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Revenue -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-400 mb-1">Total Revenue</p>
                        <p class="text-2xl font-bold text-white"><?= formatCurrency($earnings['totalRevenue']) ?></p>
                        <div class="flex items-center mt-1">
                            <?php if ($earnings['revenueGrowth'] >= 0): ?>
                                <i class="fas fa-trending-up text-green-500 mr-1"></i>
                                <span class="text-green-500 text-sm">+<?= number_format($earnings['revenueGrowth'], 1) ?>%</span>
                            <?php else: ?>
                                <i class="fas fa-trending-down text-red-500 mr-1"></i>
                                <span class="text-red-500 text-sm"><?= number_format($earnings['revenueGrowth'], 1) ?>%</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-dollar-sign text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Period Revenue -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-400 mb-1"><?= ucfirst($range) ?> Revenue</p>
                        <p class="text-2xl font-bold text-white"><?= formatCurrency($earnings['periodRevenue']) ?></p>
                        <p class="text-sm text-gray-400 mt-1">Current period</p>
                    </div>
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Transactions -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-400 mb-1">Total Transactions</p>
                        <p class="text-2xl font-bold text-white"><?= number_format($earnings['totalTransactions']) ?></p>
                        <p class="text-sm text-gray-400 mt-1">Completed bookings</p>
                    </div>
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-receipt text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Average Order Value -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-400 mb-1">Average Order Value</p>
                        <p class="text-2xl font-bold text-white"><?= formatCurrency($earnings['averageOrderValue']) ?></p>
                        <p class="text-sm text-gray-400 mt-1">Per transaction</p>
                    </div>
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-calculator text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Monthly Revenue Chart -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Revenue Trend (Last 12 Months)</h3>
                    <div class="flex items-center space-x-2 text-sm text-gray-400">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-salon-gold rounded-full mr-2"></div>
                            <span>Revenue</span>
                        </div>
                        <div class="flex items-center ml-4">
                            <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                            <span>Bookings</span>
                        </div>
                    </div>
                </div>
                <div class="h-80 relative">
                    <canvas id="revenueChart" class="w-full h-full"></canvas>
                </div>

                <!-- Summary Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-4 border-t border-secondary-700">
                    <div class="text-center">
                        <p class="text-2xl font-bold text-salon-gold"><?= formatCurrency(array_sum(array_column($earnings['monthlyData'], 'revenue'))) ?></p>
                        <p class="text-xs text-gray-400">Total (12 months)</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-blue-400"><?= number_format(array_sum(array_column($earnings['monthlyData'], 'bookings'))) ?></p>
                        <p class="text-xs text-gray-400">Total Bookings</p>
                    </div>
                    <div class="text-center">
                        <?php
                        $avgRevenue = count($earnings['monthlyData']) > 0 ? array_sum(array_column($earnings['monthlyData'], 'revenue')) / count($earnings['monthlyData']) : 0;
                        ?>
                        <p class="text-2xl font-bold text-green-400"><?= formatCurrency($avgRevenue) ?></p>
                        <p class="text-xs text-gray-400">Avg/Month</p>
                    </div>
                    <div class="text-center">
                        <?php
                        $recentMonths = array_slice($earnings['monthlyData'], -2);
                        $growth = 0;
                        if (count($recentMonths) >= 2 && $recentMonths[0]['revenue'] > 0) {
                            $growth = (($recentMonths[1]['revenue'] - $recentMonths[0]['revenue']) / $recentMonths[0]['revenue']) * 100;
                        }
                        ?>
                        <p class="text-2xl font-bold <?= $growth >= 0 ? 'text-green-400' : 'text-red-400' ?>">
                            <?= $growth >= 0 ? '+' : '' ?><?= number_format($growth, 1) ?>%
                        </p>
                        <p class="text-xs text-gray-400">Month Growth</p>
                    </div>
                </div>
            </div>

            <!-- Top Services by Revenue -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Top Revenue Services</h3>
                <div class="space-y-4">
                    <?php foreach ($earnings['topServices'] as $index => $service): ?>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 rounded-full bg-salon-gold flex items-center justify-center">
                                    <span class="text-black font-medium text-sm"><?= $index + 1 ?></span>
                                </div>
                                <div>
                                    <p class="font-medium text-white"><?= htmlspecialchars($service['name']) ?></p>
                                    <p class="text-sm text-gray-400"><?= $service['bookings'] ?> bookings</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-white"><?= formatCurrency($service['revenue']) ?></p>
                                <p class="text-sm text-gray-400">
                                    <?= $earnings['totalRevenue'] > 0 ? number_format(($service['revenue'] / $earnings['totalRevenue']) * 100, 1) : 0 ?>%
                                </p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Payment Methods & Staff Earnings -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Payment Methods Breakdown -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Payment Methods</h3>
                <div class="space-y-4">
                    <?php foreach ($earnings['paymentMethods'] as $method): ?>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-3 h-3 rounded-full bg-salon-gold"></div>
                                <span class="text-white"><?= $method['method'] ?></span>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-white"><?= formatCurrency($method['amount']) ?></p>
                                <p class="text-sm text-gray-400"><?= $method['percentage'] ?>%</p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Staff Earnings -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Staff Performance</h3>
                <div class="space-y-4">
                    <?php if (empty($earnings['staffEarnings'])): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-400">No staff earnings data available</p>
                            <p class="text-sm text-gray-500">Staff performance will appear here once bookings are completed</p>
                        </div>
                    <?php else: ?>
                        <?php foreach (array_slice($earnings['staffEarnings'], 0, 5) as $staff): ?>
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-white"><?= htmlspecialchars($staff['staffName'] ?? 'Unknown') ?></p>
                                    <p class="text-sm text-gray-400"><?= $staff['completedBookings'] ?? 0 ?> completed bookings</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-semibold text-white"><?= formatCurrency($staff['staffCommission'] ?? 0) ?></p>
                                    <p class="text-sm text-gray-400">Commission</p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="bg-secondary-800 rounded-lg overflow-hidden">
            <div class="p-6 border-b border-secondary-700">
                <h3 class="text-lg font-semibold text-white">Recent Transactions</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-secondary-700">
                    <thead class="bg-secondary-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Customer</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Service</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Payment</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-secondary-800 divide-y divide-secondary-700">
                        <?php if (empty($earnings['recentTransactions'])): ?>
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center text-gray-400">
                                    <i class="fas fa-receipt text-4xl mb-4"></i>
                                    <p class="text-lg">No transactions found</p>
                                    <p class="text-sm">Transactions will appear here once bookings are completed</p>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($earnings['recentTransactions'] as $transaction): ?>
                                <tr class="hover:bg-secondary-700 transition-colors">
                                    <td class="px-6 py-4 text-sm text-white">
                                        <?= date('M j, Y', strtotime($transaction['date'])) ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-white">
                                        <?= htmlspecialchars($transaction['customerName']) ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-white">
                                        <?= htmlspecialchars($transaction['serviceName']) ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm font-semibold text-white">
                                        <?= formatCurrency($transaction['amount']) ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-400">
                                        <?= $transaction['paymentMethod'] ?>
                                    </td>
                                    <td class="px-6 py-4">
                                        <?php 
                                        $statusClass = $transaction['status'] === 'COMPLETED' 
                                            ? 'bg-green-100 text-green-800' 
                                            : 'bg-yellow-100 text-yellow-800';
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                            <?= $transaction['status'] ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
// Initialize Revenue Chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('revenueChart').getContext('2d');
    const monthlyData = <?= json_encode($earnings['monthlyData']) ?>;

    // Prepare chart data
    const labels = monthlyData.map(item => item.month);
    const revenueData = monthlyData.map(item => item.revenue);
    const bookingsData = monthlyData.map(item => item.bookings);

    // Create gradient for revenue line
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, 'rgba(255, 193, 7, 0.3)');
    gradient.addColorStop(1, 'rgba(255, 193, 7, 0.05)');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Revenue (TSH)',
                data: revenueData,
                borderColor: '#FFC107',
                backgroundColor: gradient,
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#FFC107',
                pointBorderColor: '#1F2937',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8,
                yAxisID: 'y'
            }, {
                label: 'Bookings',
                data: bookingsData,
                borderColor: '#3B82F6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4,
                pointBackgroundColor: '#3B82F6',
                pointBorderColor: '#1F2937',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false // We have custom legend
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(31, 41, 55, 0.9)',
                    titleColor: '#FFC107',
                    bodyColor: '#E5E7EB',
                    borderColor: '#FFC107',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return 'Revenue: TSH ' + context.parsed.y.toLocaleString();
                            } else {
                                return 'Bookings: ' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        color: 'rgba(75, 85, 99, 0.3)',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#9CA3AF',
                        font: {
                            size: 12
                        }
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    grid: {
                        color: 'rgba(75, 85, 99, 0.3)',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#9CA3AF',
                        font: {
                            size: 12
                        },
                        callback: function(value) {
                            return 'TSH ' + value.toLocaleString();
                        }
                    },
                    title: {
                        display: true,
                        text: 'Revenue (TSH)',
                        color: '#FFC107',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        color: '#9CA3AF',
                        font: {
                            size: 12
                        }
                    },
                    title: {
                        display: true,
                        text: 'Bookings',
                        color: '#3B82F6',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    }
                }
            },
            elements: {
                point: {
                    hoverBorderWidth: 3
                }
            }
        }
    });
});

function exportReport() {
    // Create CSV content
    const csvContent = generateCSVReport();

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `earnings-report-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function generateCSVReport() {
    const data = <?= json_encode($earnings) ?>;
    let csv = 'Flix Salonce - Earnings Report\n';
    csv += 'Generated: ' + new Date().toLocaleDateString() + '\n\n';

    // Summary
    csv += 'SUMMARY\n';
    csv += 'Total Revenue,TSH ' + data.totalRevenue.toLocaleString() + '\n';
    csv += 'Period Revenue,TSH ' + data.periodRevenue.toLocaleString() + '\n';
    csv += 'Total Transactions,' + data.totalTransactions.toLocaleString() + '\n';
    csv += 'Average Order Value,TSH ' + data.averageOrderValue.toLocaleString() + '\n\n';

    // Monthly Data
    csv += 'MONTHLY REVENUE DATA\n';
    csv += 'Month,Revenue (TSH),Bookings\n';
    data.monthlyData.forEach(month => {
        csv += month.month + ',TSH ' + month.revenue.toLocaleString() + ',' + month.bookings + '\n';
    });
    csv += '\n';

    // Top Services
    csv += 'TOP SERVICES BY REVENUE\n';
    csv += 'Service Name,Revenue (TSH),Bookings\n';
    data.topServices.forEach(service => {
        csv += '"' + service.name + '",TSH ' + service.revenue.toLocaleString() + ',' + service.bookings + '\n';
    });
    csv += '\n';

    // Staff Performance
    csv += 'STAFF PERFORMANCE\n';
    csv += 'Staff Name,Completed Bookings,Commission (TSH)\n';
    data.staffEarnings.forEach(staff => {
        csv += '"' + (staff.staffName || 'Unknown') + '",' + staff.completedBookings + ',TSH ' + staff.staffCommission.toLocaleString() + '\n';
    });

    return csv;
}

// Auto-refresh data every 5 minutes
setInterval(() => {
    location.reload();
}, 300000);
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
