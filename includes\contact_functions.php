<?php
/**
 * Contact Management Functions
 * Flix Salonce - PHP Version
 */

/**
 * Create a new contact message
 */
function createContactMessage($data) {
    global $database;
    
    $id = generateUUID();
    
    $sql = "INSERT INTO contact_messages (id, name, email, phone, subject, message, status) 
            VALUES (?, ?, ?, ?, ?, ?, 'NEW')";
    
    $result = $database->query($sql, [
        $id,
        $data['name'],
        $data['email'],
        $data['phone'] ?? null,
        $data['subject'] ?? null,
        $data['message']
    ]);
    
    if ($result) {
        // Create notification for new contact message (admin notification)
        // Get first admin user for notification
        $adminUser = $database->fetch("SELECT id FROM users WHERE role = 'ADMIN' LIMIT 1");
        $adminUserId = $adminUser ? $adminUser['id'] : null;

        if ($adminUserId) {
            createNotification(
                $adminUserId, // Admin user ID
                'New Contact Message',
                "New message from {$data['name']} ({$data['email']})",
                'COMPLAINT_NEW',
                [
                    'category' => 'FEEDBACK',
                    'priority' => 'MEDIUM',
                    'metadata' => json_encode(['contact_id' => $id, 'subject' => $data['subject']])
                ]
            );
        }

        return $id;
    }
    
    return false;
}

/**
 * Get contact message by ID
 */
function getContactMessage($id) {
    global $database;
    
    return $database->fetch(
        "SELECT * FROM contact_messages WHERE id = ?",
        [$id]
    );
}

/**
 * Get all contact messages with pagination and filtering
 */
function getContactMessages($filters = [], $page = 1, $limit = 20) {
    global $database;
    
    $conditions = [];
    $params = [];
    
    // Status filter
    if (!empty($filters['status'])) {
        $conditions[] = "status = ?";
        $params[] = $filters['status'];
    }
    
    // Search filter
    if (!empty($filters['search'])) {
        $searchTerm = '%' . $filters['search'] . '%';
        $conditions[] = "(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }
    
    // Date range filter
    if (!empty($filters['date_from'])) {
        $conditions[] = "created_at >= ?";
        $params[] = $filters['date_from'] . ' 00:00:00';
    }
    
    if (!empty($filters['date_to'])) {
        $conditions[] = "created_at <= ?";
        $params[] = $filters['date_to'] . ' 23:59:59';
    }
    
    // Subject filter
    if (!empty($filters['subject'])) {
        $conditions[] = "subject = ?";
        $params[] = $filters['subject'];
    }
    
    $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
    
    // Get total count
    $totalSql = "SELECT COUNT(*) as count FROM contact_messages $whereClause";
    $total = $database->fetch($totalSql, $params)['count'];
    
    // Get paginated results
    $offset = ($page - 1) * $limit;
    $sql = "SELECT * FROM contact_messages $whereClause 
            ORDER BY created_at DESC 
            LIMIT $limit OFFSET $offset";
    
    $messages = $database->fetchAll($sql, $params);
    
    return [
        'messages' => $messages,
        'total' => $total,
        'pages' => ceil($total / $limit),
        'current_page' => $page
    ];
}

/**
 * Update contact message status
 */
function updateContactMessageStatus($id, $status) {
    global $database;
    
    $validStatuses = ['NEW', 'READ', 'REPLIED', 'ARCHIVED'];
    if (!in_array($status, $validStatuses)) {
        return false;
    }
    
    return $database->query(
        "UPDATE contact_messages SET status = ?, updated_at = NOW() WHERE id = ?",
        [$status, $id]
    );
}

/**
 * Delete contact message
 */
function deleteContactMessage($id) {
    global $database;
    
    return $database->query(
        "DELETE FROM contact_messages WHERE id = ?",
        [$id]
    );
}

/**
 * Bulk update contact messages
 */
function bulkUpdateContactMessages($ids, $action) {
    global $database;
    
    if (empty($ids) || !is_array($ids)) {
        return false;
    }
    
    $placeholders = str_repeat('?,', count($ids) - 1) . '?';
    
    switch ($action) {
        case 'mark_read':
            $sql = "UPDATE contact_messages SET status = 'READ', updated_at = NOW() WHERE id IN ($placeholders)";
            return $database->query($sql, $ids);
            
        case 'archive':
            $sql = "UPDATE contact_messages SET status = 'ARCHIVED', updated_at = NOW() WHERE id IN ($placeholders)";
            return $database->query($sql, $ids);
            
        case 'delete':
            $sql = "DELETE FROM contact_messages WHERE id IN ($placeholders)";
            return $database->query($sql, $ids);
            
        default:
            return false;
    }
}

/**
 * Get contact message statistics
 */
function getContactMessageStats() {
    global $database;
    
    $stats = [];
    
    // Total messages
    $stats['total'] = $database->fetch("SELECT COUNT(*) as count FROM contact_messages")['count'];
    
    // New messages
    $stats['new'] = $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'NEW'")['count'];
    
    // Read messages
    $stats['read'] = $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'READ'")['count'];
    
    // Replied messages
    $stats['replied'] = $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'REPLIED'")['count'];
    
    // Archived messages
    $stats['archived'] = $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'ARCHIVED'")['count'];
    
    // Messages this month
    $stats['this_month'] = $database->fetch(
        "SELECT COUNT(*) as count FROM contact_messages WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())"
    )['count'];
    
    // Messages today
    $stats['today'] = $database->fetch(
        "SELECT COUNT(*) as count FROM contact_messages WHERE DATE(created_at) = CURDATE()"
    )['count'];
    
    return $stats;
}

/**
 * Get unique subjects for filtering
 */
function getContactSubjects() {
    global $database;
    
    return $database->fetchAll(
        "SELECT DISTINCT subject FROM contact_messages WHERE subject IS NOT NULL AND subject != '' ORDER BY subject"
    );
}

/**
 * Mark contact message as replied
 */
function markContactMessageReplied($id, $replyMessage = null) {
    global $database;
    
    $result = updateContactMessageStatus($id, 'REPLIED');
    
    if ($result && $replyMessage) {
        // Get contact message details
        $contact = getContactMessage($id);
        
        if ($contact) {
            // Create notification for reply (admin notification)
            // Get first admin user for notification
            $adminUser = $database->fetch("SELECT id FROM users WHERE role = 'ADMIN' LIMIT 1");
            $adminUserId = $adminUser ? $adminUser['id'] : null;

            if ($adminUserId) {
                createNotification(
                    $adminUserId, // Admin user ID
                    'Contact Message Replied',
                    "Replied to message from {$contact['name']}",
                    'GENERAL',
                    [
                        'category' => 'FEEDBACK',
                        'priority' => 'LOW',
                        'metadata' => json_encode(['contact_id' => $id, 'reply' => $replyMessage])
                    ]
                );
            }
        }
    }
    
    return $result;
}

/**
 * Validate contact form data
 */
function validateContactData($data) {
    $errors = [];
    
    // Required fields
    if (empty($data['name'])) {
        $errors[] = 'Name is required';
    }
    
    if (empty($data['email'])) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address';
    }
    
    if (empty($data['message'])) {
        $errors[] = 'Message is required';
    }
    
    // Phone number validation removed as requested
    
    return $errors;
}

/**
 * Sanitize contact form data
 */
function sanitizeContactData($data) {
    return [
        'name' => sanitize($data['name'] ?? ''),
        'email' => sanitize($data['email'] ?? ''),
        'phone' => sanitize($data['phone'] ?? ''),
        'subject' => sanitize($data['subject'] ?? ''),
        'message' => sanitize($data['message'] ?? '')
    ];
}
