<?php
/**
 * Admin Staff API
 * Handle staff-related API requests
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_functions.php';
require_once __DIR__ . '/../../includes/staff_schedule_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$requestUri = $_SERVER['REQUEST_URI'];

// Remove query string if present
$requestUri = strtok($requestUri, '?');
$pathParts = explode('/', trim($requestUri, '/'));

// Extract staff ID from URL if present
$staffId = null;

// Debug logging
error_log("API Request URI: " . $requestUri);
error_log("Path parts: " . print_r($pathParts, true));

// Try different ways to extract staff ID
if (count($pathParts) >= 4) {
    // URL like: /flix-php/api/admin/staff/staff-id
    if ($pathParts[2] === 'admin' && $pathParts[3] === 'staff' && isset($pathParts[4])) {
        $staffId = $pathParts[4];
    }
    // URL like: /api/admin/staff/staff-id
    elseif ($pathParts[1] === 'admin' && $pathParts[2] === 'staff' && isset($pathParts[3])) {
        $staffId = $pathParts[3];
    }
}

// Also check for staff ID in query parameters as fallback
if (!$staffId && isset($_GET['id'])) {
    $staffId = $_GET['id'];
}

error_log("Extracted staff ID: " . ($staffId ?? 'null'));

try {
    switch ($method) {
        case 'GET':
            if ($staffId) {
                handleGetStaff($staffId);
            } else {
                handleGetAllStaff();
            }
            break;
            
        case 'POST':
            handleCreateStaff();
            break;
            
        case 'PUT':
        case 'PATCH':
            if ($staffId) {
                handleUpdateStaff($staffId);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Staff ID is required for update']);
            }
            break;
            
        case 'DELETE':
            if ($staffId) {
                handleDeleteStaff($staffId);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Staff ID is required for deletion']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetStaff($staffId) {
    global $database;

    try {
        // Get staff member from users table
        $staff = $database->fetch(
            "SELECT u.*, ss.role as staff_role, ss.hourly_rate, ss.bio, ss.experience, ss.is_active as schedule_active
             FROM users u
             LEFT JOIN staff_schedules ss ON u.id = ss.user_id
             WHERE u.id = ? AND u.role = 'STAFF'",
            [$staffId]
        );

        if (!$staff) {
            http_response_code(404);
            echo json_encode(['error' => 'Staff member not found']);
            return;
        }

        // Get staff specialties (services they can perform) from staff_specialties table
        $specialties = [];
        $specialtyRecords = $database->fetchAll(
            "SELECT service_id, proficiency_level
             FROM staff_specialties
             WHERE user_id = ?",
            [$staffId]
        );

        foreach ($specialtyRecords as $specialty) {
            $specialties[] = $specialty['service_id'];
        }

        // Get staff schedule
        $schedule = null;
        $scheduleRecord = $database->fetch(
            "SELECT schedule FROM staff_schedules WHERE user_id = ?",
            [$staffId]
        );

        if ($scheduleRecord && !empty($scheduleRecord['schedule'])) {
            $schedule = json_decode($scheduleRecord['schedule'], true);
        }
        
        // Format response
        $response = [
            'id' => $staff['id'],
            'name' => $staff['name'],
            'email' => $staff['email'],
            'phone' => $staff['phone'],
            'is_active' => (bool)$staff['is_active'],
            'staff_role' => $staff['staff_role'] ?? 'Staff Member',
            'hourly_rate' => (int)($staff['hourly_rate'] ?? 500000),
            'specialties' => $specialties,
            'bio' => $staff['bio'],
            'experience' => (int)($staff['experience'] ?? 0),
            'schedule' => $schedule,
            'schedule_active' => (bool)($staff['schedule_active'] ?? true),
            'created_at' => $staff['created_at']
        ];
        
        echo json_encode([
            'success' => true,
            'data' => $response
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch staff member: ' . $e->getMessage()]);
    }
}

function handleGetAllStaff() {
    global $database;
    
    try {
        $staff = $database->fetchAll(
            "SELECT u.*, ss.role as staff_role, ss.hourly_rate, ss.specialties 
             FROM users u 
             LEFT JOIN staff_schedules ss ON u.id = ss.user_id 
             WHERE u.role = 'STAFF' 
             ORDER BY u.name ASC"
        );
        
        echo json_encode([
            'success' => true,
            'data' => $staff
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch staff members: ' . $e->getMessage()]);
    }
}

function handleCreateStaff() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    $result = createStaffMember($input);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'data' => ['id' => $result['id']]
        ]);
    } else {
        http_response_code(400);
        echo json_encode(['error' => $result['error']]);
    }
}

function handleUpdateStaff($staffId) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    $result = updateStaffMember($staffId, $input);
    
    if ($result['success']) {
        echo json_encode(['success' => true]);
    } else {
        http_response_code(400);
        echo json_encode(['error' => $result['error']]);
    }
}

function handleDeleteStaff($staffId) {
    $result = toggleStaffStatus($staffId); // Deactivate instead of delete
    
    if ($result['success']) {
        echo json_encode(['success' => true]);
    } else {
        http_response_code(400);
        echo json_encode(['error' => $result['error']]);
    }
}
?>
