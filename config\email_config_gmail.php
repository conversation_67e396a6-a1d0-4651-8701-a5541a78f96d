<?php
/**
 * Alternative Email Configuration - Gmail SMTP
 * Use this configuration if the original SMTP server is not working
 * 
 * To use this configuration:
 * 1. Create a Gmail account or use existing one
 * 2. Enable 2-factor authentication
 * 3. Generate an App Password: https://support.google.com/accounts/answer/185833
 * 4. Replace the credentials below
 * 5. Update the main config/app.php to use these settings
 */

// Gmail SMTP Configuration
define('SMTP_HOST_GMAIL', 'smtp.gmail.com');
define('SMTP_PORT_GMAIL', 587);
define('SMTP_USERNAME_GMAIL', '<EMAIL>'); // Replace with your Gmail
define('SMTP_PASSWORD_GMAIL', 'your-app-password'); // Replace with your App Password
define('SMTP_SECURE_GMAIL', 'tls'); // Use TLS for port 587
define('SMTP_FROM_NAME_GMAIL', 'Flix Salon & SPA');
define('SMTP_FROM_EMAIL_GMAIL', SMTP_USERNAME_GMAIL);

/**
 * Instructions to set up Gmail SMTP:
 * 
 * 1. Go to your Google Account settings
 * 2. Navigate to Security
 * 3. Enable 2-Step Verification if not already enabled
 * 4. Go to App passwords
 * 5. Select "Mail" and your device
 * 6. Copy the generated 16-character password
 * 7. Use this password in SMTP_PASSWORD_GMAIL above
 * 
 * Alternative ports for Gmail:
 * - Port 587 with TLS (recommended)
 * - Port 465 with SSL
 * - Port 25 with TLS (may be blocked)
 */

/**
 * To switch to Gmail configuration, replace these lines in config/app.php:
 * 
 * // Replace these lines:
 * define('SMTP_HOST', 'mail.barberianramadhancup.co.tz');
 * define('SMTP_PORT', 465);
 * define('SMTP_USERNAME', '<EMAIL>');
 * define('SMTP_PASSWORD', 'UVutmmRceu37zK6');
 * define('SMTP_SECURE', 'ssl');
 * 
 * // With these:
 * define('SMTP_HOST', 'smtp.gmail.com');
 * define('SMTP_PORT', 587);
 * define('SMTP_USERNAME', '<EMAIL>');
 * define('SMTP_PASSWORD', 'your-app-password');
 * define('SMTP_SECURE', 'tls');
 */

/**
 * Other reliable SMTP providers:
 * 
 * Outlook/Hotmail:
 * Host: smtp-mail.outlook.com
 * Port: 587
 * Security: STARTTLS
 * 
 * Yahoo:
 * Host: smtp.mail.yahoo.com
 * Port: 587 or 465
 * Security: TLS or SSL
 * 
 * SendGrid (API-based):
 * Host: smtp.sendgrid.net
 * Port: 587
 * Username: apikey
 * Password: your-api-key
 * 
 * Mailgun:
 * Host: smtp.mailgun.org
 * Port: 587
 * Username: your-mailgun-username
 * Password: your-mailgun-password
 */
?>
