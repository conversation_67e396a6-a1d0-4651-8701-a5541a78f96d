<?php
/**
 * Admin Services Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Require admin authentication
$auth->requireRole('ADMIN');


// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
            $result = createService($_POST);
            if ($result['success']) {
                $_SESSION['success'] = 'Service created successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
            
        case 'update':
            $result = updateService($_POST['id'], $_POST);
            if ($result['success']) {
                $_SESSION['success'] = 'Service updated successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
            
        case 'delete':
            $result = deleteService($_POST['id']);
            if ($result['success']) {
                $_SESSION['success'] = 'Service deleted successfully!';
            } else {
                // Store detailed error information for JavaScript to handle
                $_SESSION['delete_error'] = json_encode($result);
                $_SESSION['error'] = $result['error'];
            }
            break;
    }
    
    redirect('/admin/services');
}

// Get all services with pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 10;
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$category = sanitize($_GET['category'] ?? '');

$whereClause = "WHERE 1=1";
$params = [];

if ($search) {
    $whereClause .= " AND (name LIKE ? OR description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($category) {
    $whereClause .= " AND category = ?";
    $params[] = $category;
}

$services = $database->fetchAll(
    "SELECT * FROM services $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset",
    $params
);

$totalServices = $database->fetch(
    "SELECT COUNT(*) as count FROM services $whereClause",
    $params
)['count'];

$totalPages = ceil($totalServices / $limit);

// Get categories for filter from the service_categories table
$categories = getActiveServiceCategories();

// Clean up any package-related session messages that might interfere with services page
// Only remove messages that contain "Package" or "package" (not "Service" messages)
if (isset($_SESSION['success']) && (strpos($_SESSION['success'], 'Package') !== false || strpos($_SESSION['success'], 'package') !== false)) {
    unset($_SESSION['success']);
}
if (isset($_SESSION['error']) && (strpos($_SESSION['error'], 'Package') !== false || strpos($_SESSION['error'], 'package') !== false)) {
    unset($_SESSION['error']);
}
if (isset($_SESSION['delete_error'])) {
    $deleteError = json_decode($_SESSION['delete_error'], true);
    if ($deleteError && isset($deleteError['error']) && (strpos($deleteError['error'], 'Package') !== false || strpos($deleteError['error'], 'package') !== false)) {
        unset($_SESSION['delete_error']);
    }
}

// Handle messages for display
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Services Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-white">Services Management</h1>
                            <p class="mt-1 text-sm text-gray-300">Manage your salon services, pricing, and categories</p>
                        </div>
                        <div class="mt-4 sm:mt-0 flex gap-3">
                            <button onclick="openCategoryModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                Manage Categories
                            </button>
                            <button onclick="openCreateModal()" class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Add Service
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Message Display -->
                <?php if ($message): ?>
                    <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <form method="GET" class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                                   placeholder="Search services..." 
                                   class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>
                        <div>
                            <select name="category" class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?= htmlspecialchars($cat['name']) ?>" <?= $category === $cat['name'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($cat['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Filter
                        </button>
                        <?php if ($search || $category): ?>
                            <a href="<?= getBasePath() ?>/admin/services" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                Clear
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Services Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                    <?php foreach ($services as $service): ?>
                        <div class="bg-secondary-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                            <div class="aspect-w-16 aspect-h-9 bg-secondary-700">
                                <?php if ($service['image']): ?>
                                    <?php
                                    // Check if image is a URL or uploaded file
                                    $imageSrc = filter_var($service['image'], FILTER_VALIDATE_URL)
                                        ? $service['image']
                                        : getBasePath() . '/uploads/' . $service['image'];
                                    ?>
                                    <img src="<?= htmlspecialchars($imageSrc) ?>" alt="<?= htmlspecialchars($service['name']) ?>"
                                         class="w-full h-48 object-cover">
                                <?php else: ?>
                                    <div class="w-full h-48 bg-secondary-700 flex items-center justify-center">
                                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="p-6">
                                <div class="flex items-start justify-between mb-2">
                                    <h3 class="text-lg font-semibold text-white"><?= htmlspecialchars($service['name']) ?></h3>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $service['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                        <?= $service['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </div>
                                <p class="text-gray-300 text-sm mb-3 line-clamp-2"><?= htmlspecialchars($service['description']) ?></p>
                                <div class="flex items-center justify-between mb-4">
                                    <span class="text-2xl font-bold text-salon-gold"><?= formatCurrency($service['price']) ?></span>
                                    <span class="text-sm text-gray-400"><?= $service['duration'] ?> min</span>
                                </div>
                                <?php if ($service['category']): ?>
                                    <div class="mb-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <?= htmlspecialchars($service['category']) ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                                <div class="flex gap-2">
                                    <button onclick="editService('<?= $service['id'] ?>')" 
                                            class="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                                        Edit
                                    </button>
                                    <button onclick="deleteService('<?= $service['id'] ?>', '<?= htmlspecialchars($service['name']) ?>')" 
                                            class="flex-1 bg-red-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors">
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="bg-secondary-800 px-4 py-3 flex items-center justify-between border-t border-secondary-700 sm:px-6 rounded-lg">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>" 
                                   class="relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                    Previous
                                </a>
                            <?php endif; ?>
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>" 
                                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                    Next
                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-300">
                                    Showing <span class="font-medium"><?= $offset + 1 ?></span> to <span class="font-medium"><?= min($offset + $limit, $totalServices) ?></span> of <span class="font-medium"><?= $totalServices ?></span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>" 
                                           class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?= $i === $page ? 'z-10 bg-salon-gold border-salon-gold text-black' : 'bg-secondary-700 border-secondary-600 text-gray-300 hover:bg-secondary-600' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>
                                </nav>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Create/Edit Service Modal -->
<div id="serviceModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-screen overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 id="modalTitle" class="text-xl font-bold text-white">Add Service</h2>
            <button onclick="closeModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="serviceForm" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="action" id="formAction" value="create">
            <input type="hidden" name="id" id="serviceId">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Service Name *</label>
                    <input type="text" name="name" id="serviceName" required 
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Category</label>
                    <select name="category" id="serviceCategory"
                            class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        <option value="">Select Category</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?= htmlspecialchars($cat['name']) ?>">
                                <?= htmlspecialchars($cat['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <textarea name="description" id="serviceDescription" rows="3" 
                          class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"></textarea>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Price (TSH) *</label>
                    <input type="number" name="price" id="servicePrice" step="1" min="0" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Duration (minutes) *</label>
                    <input type="number" name="duration" id="serviceDuration" min="1" required 
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Service Image</label>

                <!-- Image Type Selection -->
                <div class="mb-3">
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="radio" name="image_type" value="upload" id="imageTypeUpload" checked
                                   class="text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                            <span class="ml-2 text-sm text-gray-300">Upload File</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="image_type" value="url" id="imageTypeUrl"
                                   class="text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                            <span class="ml-2 text-sm text-gray-300">Image URL</span>
                        </label>
                    </div>
                </div>

                <!-- File Upload Option -->
                <div id="imageUploadSection" class="mb-3">
                    <input type="file" name="image" id="serviceImage" accept="image/*"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-salon-gold file:text-black hover:file:bg-gold-light">
                    <p class="text-xs text-gray-400 mt-1">Upload an image file (JPEG, PNG, GIF, WebP - max 5MB)</p>
                </div>

                <!-- URL Input Option -->
                <div id="imageUrlSection" class="mb-3 hidden">
                    <input type="url" name="image_url" id="serviceImageUrl" placeholder="https://example.com/image.jpg"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    <p class="text-xs text-gray-400 mt-1">Enter a direct URL to an image</p>
                </div>

                <!-- Image Preview -->
                <div id="imagePreview" class="hidden mt-3">
                    <p class="text-sm text-gray-300 mb-2">Preview:</p>
                    <img id="previewImg" src="" alt="Image preview" class="w-32 h-32 object-cover rounded-lg">
                </div>
            </div>
            
            <div class="mb-6">
                <label class="flex items-center">
                    <input type="checkbox" name="is_active" id="serviceActive" value="1" checked 
                           class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                    <span class="ml-2 text-sm text-gray-300">Service is active</span>
                </label>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="flex-1 bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    Save Service
                </button>
                <button type="button" onclick="closeModal()" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Custom Alert Modal -->
<div id="customAlert" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4 border border-secondary-600">
        <div class="flex items-center mb-4">
            <div id="alertIcon" class="flex-shrink-0 mr-3">
                <!-- Icon will be inserted here -->
            </div>
            <h3 id="alertTitle" class="text-lg font-semibold text-white"></h3>
        </div>
        <div id="alertMessage" class="text-gray-300 mb-6"></div>
        <div class="flex gap-3 justify-end">
            <button id="alertCancel" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors hidden">
                Cancel
            </button>
            <button id="alertConfirm" class="px-4 py-2 bg-salon-gold text-black rounded-lg hover:bg-gold-light transition-colors font-semibold">
                OK
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-lg mx-4 border border-red-600">
        <div class="flex items-center mb-4">
            <div class="flex-shrink-0 mr-3">
                <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-white">Delete Service</h3>
        </div>
        <div id="deleteMessage" class="text-gray-300 mb-6"></div>
        <div class="flex gap-3 justify-end">
            <button id="deleteCancel" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                Cancel
            </button>
            <button id="deleteConfirm" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-semibold">
                Delete
            </button>
        </div>
    </div>
</div>

<!-- Category Management Modal -->
<div id="categoryModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-4xl mx-4 max-h-screen overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-white">Manage Service Categories</h2>
            <button onclick="closeCategoryModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Add New Category Form -->
        <div class="bg-secondary-700 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-semibold text-white mb-4">Add New Category</h3>
            <form id="categoryForm" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Category Name *</label>
                    <input type="text" id="categoryName" required
                           class="w-full px-3 py-2 bg-secondary-600 border border-secondary-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                    <input type="text" id="categoryDescription"
                           class="w-full px-3 py-2 bg-secondary-600 border border-secondary-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                        Add Category
                    </button>
                </div>
            </form>
        </div>

        <!-- Categories List -->
        <div class="bg-secondary-700 rounded-lg overflow-hidden">
            <div class="px-4 py-3 bg-secondary-600 border-b border-secondary-500">
                <h3 class="text-lg font-semibold text-white">Existing Categories</h3>
            </div>
            <div id="categoriesTableContainer" class="max-h-96 overflow-y-auto">
                <table class="w-full">
                    <thead class="bg-secondary-600 sticky top-0">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Name</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Description</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Services</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="categoriesTableBody" class="bg-secondary-700 divide-y divide-secondary-600">
                        <!-- Categories will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Clear any cached alert content on page load
document.addEventListener('DOMContentLoaded', function() {
    // Clear any existing alert modals
    const alertModal = document.getElementById('customAlert');
    if (alertModal) {
        alertModal.classList.add('hidden');
    }

    // Clear any alert content that might be cached
    const alertTitle = document.getElementById('alertTitle');
    const alertMessage = document.getElementById('alertMessage');
    if (alertTitle) alertTitle.textContent = '';
    if (alertMessage) alertMessage.innerHTML = '';
});

// Modal functions
function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Add Service';
    document.getElementById('formAction').value = 'create';
    document.getElementById('serviceForm').reset();
    document.getElementById('serviceActive').checked = true;

    // Reset image sections
    const currentImageDiv = document.getElementById('currentImageDisplay');
    if (currentImageDiv) {
        currentImageDiv.remove();
    }

    // Reset to upload option
    document.getElementById('imageTypeUpload').checked = true;
    if (typeof toggleImageSections === 'function') {
        toggleImageSections();
    }

    document.getElementById('serviceModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('serviceModal').classList.add('hidden');
}

// Category Management Functions
function openCategoryModal() {
    document.getElementById('categoryModal').classList.remove('hidden');
    loadCategories();
}

function closeCategoryModal() {
    document.getElementById('categoryModal').classList.add('hidden');
}

function loadCategories() {
    fetch(`<?= getBasePath() ?>/api/admin/service_categories.php?with_count=true`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayCategories(data.categories);
            } else {
                showAlert('error', 'Error Loading Categories', 'Failed to load categories');
            }
        })
        .catch(error => {
            console.error('Error loading categories:', error);
            showAlert('error', 'Error Loading Categories', 'Failed to load categories');
        });
}

function displayCategories(categories) {
    const tbody = document.getElementById('categoriesTableBody');
    tbody.innerHTML = '';

    categories.forEach(category => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-secondary-600';

        const statusBadge = category.is_active
            ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>'
            : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>';

        row.innerHTML = `
            <td class="px-4 py-3 text-sm text-white font-medium">${escapeHtml(category.name)}</td>
            <td class="px-4 py-3 text-sm text-gray-300">${escapeHtml(category.description || '')}</td>
            <td class="px-4 py-3 text-sm text-gray-300">${category.service_count || 0}</td>
            <td class="px-4 py-3 text-sm">${statusBadge}</td>
            <td class="px-4 py-3 text-sm">
                <div class="flex gap-2">
                    <button onclick="editCategory('${category.id}')"
                            class="text-blue-400 hover:text-blue-300 font-medium">
                        Edit
                    </button>
                    <button onclick="toggleCategoryStatus('${category.id}', ${category.is_active})"
                            class="text-yellow-400 hover:text-yellow-300 font-medium">
                        ${category.is_active ? 'Deactivate' : 'Activate'}
                    </button>
                    <button onclick="deleteCategory('${category.id}', '${escapeHtml(category.name)}')"
                            class="text-red-400 hover:text-red-300 font-medium">
                        Delete
                    </button>
                </div>
            </td>
        `;

        tbody.appendChild(row);
    });
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function editService(serviceId) {
    // Fetch service data and populate form
    fetch(`<?= getBasePath() ?>/api/admin/services/get.php?id=${serviceId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(service => {
            document.getElementById('modalTitle').textContent = 'Edit Service';
            document.getElementById('formAction').value = 'update';
            document.getElementById('serviceId').value = service.id;
            document.getElementById('serviceName').value = service.name;
            document.getElementById('serviceCategory').value = service.category || '';
            document.getElementById('serviceDescription').value = service.description || '';
            document.getElementById('servicePrice').value = service.price;
            document.getElementById('serviceDuration').value = service.duration;
            document.getElementById('serviceActive').checked = service.is_active == 1;

            // Handle image display if exists
            const currentImageDiv = document.getElementById('currentImageDisplay');
            if (currentImageDiv) {
                currentImageDiv.remove();
            }

            if (service.image) {
                const imageDisplay = document.createElement('div');
                imageDisplay.id = 'currentImageDisplay';
                imageDisplay.className = 'mb-2';

                // Check if image is a URL or uploaded file
                const imageSrc = service.image.startsWith('http') ? service.image : `<?= getBasePath() ?>/uploads/${service.image}`;

                imageDisplay.innerHTML = `
                    <p class="text-sm text-gray-300 mb-2">Current image:</p>
                    <img src="${imageSrc}" alt="Current service image" class="w-32 h-32 object-cover rounded-lg">
                `;
                document.getElementById('serviceImage').parentNode.insertBefore(imageDisplay, document.getElementById('serviceImage'));

                // If current image is a URL, pre-select URL option and populate field
                if (service.image.startsWith('http')) {
                    document.getElementById('imageTypeUrl').checked = true;
                    document.getElementById('serviceImageUrl').value = service.image;
                    toggleImageSections();
                }
            }

            document.getElementById('serviceModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error fetching service:', error);
            showAlert('error', 'Error Loading Service', 'Failed to load service data: ' + error.message);
        });
}

function deleteService(serviceId, serviceName) {
    showDeleteConfirmation(
        `Are you sure you want to delete "${serviceName}"?`,
        'This action cannot be undone. The service will be permanently removed from the system.',
        () => {
            // Show loading state
            showAlert('info', 'Deleting Service', 'Please wait while we delete the service...');

            // Perform delete request
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="id" value="${serviceId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    );
}

// Category form submission
document.addEventListener('DOMContentLoaded', function() {
    const categoryForm = document.getElementById('categoryForm');
    categoryForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const name = document.getElementById('categoryName').value.trim();
        const description = document.getElementById('categoryDescription').value.trim();

        if (!name) {
            showAlert('error', 'Validation Error', 'Category name is required');
            return;
        }

        const data = {
            name: name,
            description: description,
            is_active: true
        };

        fetch(`<?= getBasePath() ?>/api/admin/service_categories.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'Success', 'Category created successfully');
                categoryForm.reset();
                loadCategories();
                // Refresh the service form categories
                refreshServiceCategories();
            } else {
                showAlert('error', 'Error', data.error || 'Failed to create category');
            }
        })
        .catch(error => {
            console.error('Error creating category:', error);
            showAlert('error', 'Error', 'Failed to create category');
        });
    });
});

function editCategory(categoryId) {
    // For now, we'll use a simple prompt. In a full implementation, you'd want a proper edit modal
    fetch(`<?= getBasePath() ?>/api/admin/service_categories.php?id=${categoryId}`)
        .then(response => response.json())
        .then(category => {
            const newName = prompt('Edit category name:', category.name);
            if (newName && newName.trim() !== category.name) {
                updateCategory(categoryId, {
                    name: newName.trim(),
                    description: category.description,
                    is_active: category.is_active
                });
            }
        })
        .catch(error => {
            console.error('Error fetching category:', error);
            showAlert('error', 'Error', 'Failed to load category data');
        });
}

function updateCategory(categoryId, data) {
    fetch(`<?= getBasePath() ?>/api/admin/service_categories.php?id=${categoryId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Success', 'Category updated successfully');
            loadCategories();
            refreshServiceCategories();
        } else {
            showAlert('error', 'Error', data.error || 'Failed to update category');
        }
    })
    .catch(error => {
        console.error('Error updating category:', error);
        showAlert('error', 'Error', 'Failed to update category');
    });
}

function toggleCategoryStatus(categoryId, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    const confirmMessage = `Are you sure you want to ${action} this category?`;

    if (confirm(confirmMessage)) {
        fetch(`<?= getBasePath() ?>/api/admin/service_categories.php?id=${categoryId}`, {
            method: 'PATCH'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'Success', `Category ${action}d successfully`);
                loadCategories();
                refreshServiceCategories();
            } else {
                showAlert('error', 'Error', data.error || `Failed to ${action} category`);
            }
        })
        .catch(error => {
            console.error('Error toggling category status:', error);
            showAlert('error', 'Error', `Failed to ${action} category`);
        });
    }
}

function deleteCategory(categoryId, categoryName) {
    showDeleteConfirmation(
        `Are you sure you want to delete "${categoryName}"?`,
        'This action cannot be undone. Make sure no services are using this category.',
        () => {
            fetch(`<?= getBasePath() ?>/api/admin/service_categories.php?id=${categoryId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', 'Success', 'Category deleted successfully');
                    loadCategories();
                    refreshServiceCategories();
                } else {
                    // Show detailed error for category deletion
                    showDeleteError(data);
                }
            })
            .catch(error => {
                console.error('Error deleting category:', error);
                showAlert('error', 'Error', 'Failed to delete category');
            });
        }
    );
}

function refreshServiceCategories() {
    // Refresh the category dropdown in the service form
    fetch(`<?= getBasePath() ?>/api/admin/service_categories.php?active_only=true`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('serviceCategory');
                const currentValue = select.value;

                // Clear existing options except the first one
                select.innerHTML = '<option value="">Select Category</option>';

                // Add new options
                data.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.name;
                    option.textContent = category.name;
                    if (category.name === currentValue) {
                        option.selected = true;
                    }
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error refreshing service categories:', error);
        });
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
        closeCategoryModal();
    }
});

// Close modal on backdrop click
document.getElementById('serviceModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

document.getElementById('categoryModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCategoryModal();
    }
});

// Global variables for image handling
let imageTypeUpload, imageTypeUrl, imageUploadSection, imageUrlSection, serviceImageUrl, serviceImage, imagePreview, previewImg;

// Global function for toggling image sections
function toggleImageSections() {
    if (imageTypeUpload.checked) {
        imageUploadSection.classList.remove('hidden');
        imageUrlSection.classList.add('hidden');
        serviceImageUrl.value = '';
        hidePreview();
    } else {
        imageUploadSection.classList.add('hidden');
        imageUrlSection.classList.remove('hidden');
        serviceImage.value = '';
        hidePreview();
    }
}

// Image type switching functionality
document.addEventListener('DOMContentLoaded', function() {
    imageTypeUpload = document.getElementById('imageTypeUpload');
    imageTypeUrl = document.getElementById('imageTypeUrl');
    imageUploadSection = document.getElementById('imageUploadSection');
    imageUrlSection = document.getElementById('imageUrlSection');
    serviceImageUrl = document.getElementById('serviceImageUrl');
    serviceImage = document.getElementById('serviceImage');
    imagePreview = document.getElementById('imagePreview');
    previewImg = document.getElementById('previewImg');

    // Make these functions global
    window.hidePreview = function() {
        imagePreview.classList.add('hidden');
        previewImg.src = '';
    }

    window.showPreview = function(src) {
        previewImg.src = src;
        imagePreview.classList.remove('hidden');
    }

    imageTypeUpload.addEventListener('change', toggleImageSections);
    imageTypeUrl.addEventListener('change', toggleImageSections);

    // Handle file upload preview
    serviceImage.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                showPreview(e.target.result);
            };
            reader.readAsDataURL(file);
        } else {
            hidePreview();
        }
    });

    // Handle URL input preview
    serviceImageUrl.addEventListener('input', function(e) {
        const url = e.target.value.trim();
        if (url && isValidImageUrl(url)) {
            // Test if image loads
            const testImg = new Image();
            testImg.onload = function() {
                showPreview(url);
            };
            testImg.onerror = function() {
                hidePreview();
            };
            testImg.src = url;
        } else {
            hidePreview();
        }
    });

    function isValidImageUrl(url) {
        try {
            new URL(url);
            return /\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i.test(url);
        } catch {
            return false;
        }
    }

    // Check for delete error from PHP session
    <?php if (isset($_SESSION['delete_error'])): ?>
        const deleteErrorData = <?= $_SESSION['delete_error'] ?>;
        showDeleteError(deleteErrorData);
        <?php unset($_SESSION['delete_error']); ?>
    <?php endif; ?>
});

// Custom Alert System
function showAlert(type, title, message, callback = null) {
    const alertModal = document.getElementById('customAlert');
    const alertIcon = document.getElementById('alertIcon');
    const alertTitle = document.getElementById('alertTitle');
    const alertMessage = document.getElementById('alertMessage');
    const alertConfirm = document.getElementById('alertConfirm');
    const alertCancel = document.getElementById('alertCancel');

    // Check if category modal is open
    const categoryModal = document.getElementById('categoryModal');
    const wasCategoryModalOpen = !categoryModal.classList.contains('hidden');

    // Set icon based on type
    const icons = {
        success: '<svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
        error: '<svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
        warning: '<svg class="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>',
        info: '<svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
    };

    alertIcon.innerHTML = icons[type] || icons.info;
    alertTitle.textContent = title;
    alertMessage.innerHTML = message;

    // Handle buttons
    alertCancel.classList.add('hidden');
    alertConfirm.onclick = () => {
        alertModal.classList.add('hidden');
        // Restore category modal if it was open
        if (wasCategoryModalOpen) {
            categoryModal.classList.remove('hidden');
        }
        if (callback) callback();
    };

    alertModal.classList.remove('hidden');
}

function showDeleteConfirmation(title, message, onConfirm) {
    const deleteModal = document.getElementById('deleteModal');
    const deleteMessage = document.getElementById('deleteMessage');
    const deleteConfirm = document.getElementById('deleteConfirm');
    const deleteCancel = document.getElementById('deleteCancel');

    // Check if category modal is open
    const categoryModal = document.getElementById('categoryModal');
    const wasCategoryModalOpen = !categoryModal.classList.contains('hidden');

    deleteMessage.innerHTML = `<p class="mb-2 font-semibold">${title}</p><p class="text-sm">${message}</p>`;

    deleteConfirm.onclick = () => {
        deleteModal.classList.add('hidden');
        // Restore category modal if it was open
        if (wasCategoryModalOpen) {
            categoryModal.classList.remove('hidden');
        }
        onConfirm();
    };

    deleteCancel.onclick = () => {
        deleteModal.classList.add('hidden');
        // Restore category modal if it was open
        if (wasCategoryModalOpen) {
            categoryModal.classList.remove('hidden');
        }
    };

    deleteModal.classList.remove('hidden');
}

// Enhanced error display for delete operations
function showDeleteError(errorData) {
    let message = `<p class="font-semibold text-red-400 mb-3">${errorData.error}</p>`;

    if (errorData.details) {
        message += `<p class="mb-3">${errorData.details}</p>`;
    }

    if (errorData.bookings && errorData.bookings.length > 0) {
        message += '<div class="bg-secondary-700 p-3 rounded-lg mb-3"><p class="font-medium mb-2">Active Bookings:</p>';
        errorData.bookings.forEach(booking => {
            message += `<p class="text-sm text-gray-300">${booking}</p>`;
        });
        message += '</div>';
    }

    if (errorData.suggestions && errorData.suggestions.length > 0) {
        message += '<div class="bg-secondary-700 p-3 rounded-lg"><p class="font-medium mb-2">Suggestions:</p><ul class="text-sm text-gray-300">';
        errorData.suggestions.forEach(suggestion => {
            message += `<li class="mb-1">• ${suggestion}</li>`;
        });
        message += '</ul></div>';
    }

    showAlert('error', 'Cannot Delete Service', message);
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
