<?php
/**
 * Debug endpoint to check booking data
 */

header('Content-Type: application/json');
require_once __DIR__ . '/../../config/app.php';

// Check if user is logged in
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$bookingId = $_GET['booking_id'] ?? '';
$userId = $_SESSION['user_id'];

if (empty($bookingId)) {
    echo json_encode(['error' => 'Booking ID required']);
    exit;
}

try {
    global $database;
    
    // Get booking by ID only
    $booking = $database->fetch("
        SELECT id, user_id, status, total_amount, date, start_time, created_at, updated_at
        FROM bookings 
        WHERE id = ?
    ", [$bookingId]);
    
    // Get all bookings for this user
    $userBookings = $database->fetchAll("
        SELECT id, status, total_amount, date, start_time
        FROM bookings 
        WHERE user_id = ?
        OR<PERSON><PERSON> BY created_at DESC
        LIMIT 5
    ", [$userId]);
    
    // Check if payment already exists
    $existingPayment = $database->fetch("
        SELECT id, status, amount, created_at
        FROM payments 
        WHERE booking_id = ?
    ", [$bookingId]);
    
    // Get eligible bookings using the function
    $eligibleBookings = getEligibleBookingsForPayment($userId);
    
    $response = [
        'success' => true,
        'debug_info' => [
            'session_user_id' => $userId,
            'session_user_role' => $_SESSION['user_role'],
            'requested_booking_id' => $bookingId,
            'booking_found' => $booking ? true : false,
            'booking_data' => $booking,
            'user_bookings_count' => count($userBookings),
            'user_bookings' => $userBookings,
            'existing_payment' => $existingPayment,
            'eligible_bookings_count' => count($eligibleBookings),
            'eligible_bookings' => $eligibleBookings
        ]
    ];
    
    // Additional checks
    if ($booking) {
        $response['debug_info']['booking_checks'] = [
            'user_id_match' => $booking['user_id'] === $userId,
            'booking_user_id' => $booking['user_id'],
            'session_user_id' => $userId,
            'status_eligible' => in_array($booking['status'], ['CONFIRMED', 'COMPLETED']),
            'current_status' => $booking['status'],
            'has_existing_payment' => $existingPayment ? true : false,
            'payment_status' => $existingPayment['status'] ?? 'none'
        ];
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug_info' => [
            'session_user_id' => $userId,
            'requested_booking_id' => $bookingId
        ]
    ]);
}
