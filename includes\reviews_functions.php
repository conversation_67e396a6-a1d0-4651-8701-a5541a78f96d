<?php
/**
 * Reviews System Functions
 * Flix Salon & SPA - PHP Version
 */

/**
 * Get reviews with filtering and pagination
 */
function getReviews($filters = [], $page = 1, $limit = 12) {
    global $database;
    
    $offset = ($page - 1) * $limit;
    $whereConditions = ["r.status = 'verified'"];
    $params = [];
    
    // Build WHERE conditions based on filters
    if (!empty($filters['rating'])) {
        $whereConditions[] = "r.rating = ?";
        $params[] = $filters['rating'];
    }
    
    if (!empty($filters['service_id'])) {
        $whereConditions[] = "r.service_id = ?";
        $params[] = $filters['service_id'];
    }
    
    if (!empty($filters['package_id'])) {
        $whereConditions[] = "r.package_id = ?";
        $params[] = $filters['package_id'];
    }
    
    if (isset($filters['is_verified']) && $filters['is_verified'] !== '') {
        $whereConditions[] = "r.is_verified = ?";
        $params[] = $filters['is_verified'] ? 1 : 0;
    }
    
    if (!empty($filters['search'])) {
        $whereConditions[] = "(r.title LIKE ? OR r.comment LIKE ? OR u.name LIKE ?)";
        $searchTerm = '%' . $filters['search'] . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    $sql = "
        SELECT
            r.*,
            u.name as customer_name,
            s.name as service_name,
            p.name as package_name
        FROM reviews r
        JOIN users u ON r.customer_id = u.id
        LEFT JOIN services s ON r.service_id = s.id
        LEFT JOIN packages p ON r.package_id = p.id
        WHERE {$whereClause}
        ORDER BY r.is_featured DESC, r.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    return $database->fetchAll($sql, $params);
}

/**
 * Get total count of reviews for pagination
 */
function getReviewsCount($filters = []) {
    global $database;
    
    $whereConditions = ["r.status = 'verified'"];
    $params = [];
    
    // Build WHERE conditions (same as getReviews)
    if (!empty($filters['rating'])) {
        $whereConditions[] = "r.rating = ?";
        $params[] = $filters['rating'];
    }
    
    if (!empty($filters['service_id'])) {
        $whereConditions[] = "r.service_id = ?";
        $params[] = $filters['service_id'];
    }
    
    if (!empty($filters['package_id'])) {
        $whereConditions[] = "r.package_id = ?";
        $params[] = $filters['package_id'];
    }
    
    if (isset($filters['is_verified']) && $filters['is_verified'] !== '') {
        $whereConditions[] = "r.is_verified = ?";
        $params[] = $filters['is_verified'] ? 1 : 0;
    }
    
    if (!empty($filters['search'])) {
        $whereConditions[] = "(r.title LIKE ? OR r.comment LIKE ? OR u.name LIKE ?)";
        $searchTerm = '%' . $filters['search'] . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    $sql = "
        SELECT COUNT(*) as total
        FROM reviews r
        JOIN users u ON r.customer_id = u.id
        LEFT JOIN services s ON r.service_id = s.id
        LEFT JOIN packages p ON r.package_id = p.id
        WHERE {$whereClause}
    ";
    
    $result = $database->fetch($sql, $params);
    return $result['total'] ?? 0;
}

/**
 * Get review statistics
 */
function getReviewStats() {
    global $database;
    
    $sql = "
        SELECT
            COUNT(*) as total_reviews,
            AVG(rating) as average_rating,
            SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
            SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
            SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
            SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
            SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star,
            SUM(CASE WHEN is_verified = 1 THEN 1 ELSE 0 END) as verified_reviews
        FROM reviews
        WHERE status = 'verified'
    ";
    
    $stats = $database->fetch($sql);
    
    if ($stats) {
        $stats['average_rating'] = $stats['average_rating'] !== null ? round((float)$stats['average_rating'], 1) : 0.0;
        $stats['recommendation_rate'] = $stats['total_reviews'] > 0
            ? round((($stats['five_star'] + $stats['four_star']) / $stats['total_reviews']) * 100)
            : 0;
    }
    
    return $stats;
}

/**
 * Get featured reviews for initial display
 */
function getFeaturedReviews($limit = 3) {
    global $database;

    $sql = "
        SELECT
            r.*,
            u.name as customer_name,
            u.email as customer_email,
            s.name as service_name,
            p.name as package_name
        FROM reviews r
        JOIN users u ON r.customer_id = u.id
        LEFT JOIN services s ON r.service_id = s.id
        LEFT JOIN packages p ON r.package_id = p.id
        WHERE r.status = 'verified' AND r.is_featured = 1
        ORDER BY r.created_at DESC
        LIMIT ?
    ";

    return $database->fetchAll($sql, [$limit]);
}

/**
 * Create a new review
 */
function createReview($data) {
    global $database;

    // Generate UUID for new review
    $reviewId = generateUUID();

    $sql = "
        INSERT INTO reviews (
            id, customer_id, service_id, package_id, booking_id,
            rating, title, comment, is_verified, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')
    ";

    $params = [
        $reviewId,
        $data['customer_id'],
        $data['service_id'] ?? null,
        $data['package_id'] ?? null,
        $data['booking_id'] ?? null,
        $data['rating'],
        $data['title'],
        $data['comment'],
        $data['is_verified'] ?? false
    ];

    $result = $database->execute($sql, $params);
    return $result ? $reviewId : false;
}

/**
 * Get services for review form dropdown
 */
function getServicesForReview() {
    global $database;
    
    $sql = "SELECT id, name FROM services WHERE is_active = 1 ORDER BY name";
    return $database->fetchAll($sql);
}

/**
 * Get packages for review form dropdown
 */
function getPackagesForReview() {
    global $database;
    
    $sql = "SELECT id, name FROM packages WHERE is_active = 1 ORDER BY name";
    return $database->fetchAll($sql);
}

/**
 * Check if customer can review a service/package
 */
function canCustomerReview($customerId, $serviceId = null, $packageId = null) {
    global $database;
    
    // Check if customer has completed booking for this service/package
    $sql = "
        SELECT COUNT(*) as count
        FROM bookings b
        WHERE b.user_id = ?
        AND b.status = 'COMPLETED'
        AND (
            (? IS NOT NULL AND b.service_id = ?) OR
            (? IS NOT NULL AND b.package_id = ?)
        )
    ";
    
    $result = $database->fetch($sql, [
        $customerId, 
        $serviceId, $serviceId,
        $packageId, $packageId
    ]);
    
    return $result['count'] > 0;
}

/**
 * Get customer's existing review for service/package
 */
function getCustomerReview($customerId, $serviceId = null, $packageId = null) {
    global $database;

    $sql = "
        SELECT * FROM reviews
        WHERE customer_id = ?
        AND (
            (? IS NOT NULL AND service_id = ?) OR
            (? IS NOT NULL AND package_id = ?)
        )
        ORDER BY created_at DESC
        LIMIT 1
    ";

    return $database->fetch($sql, [
        $customerId,
        $serviceId, $serviceId,
        $packageId, $packageId
    ]);
}

/**
 * Get top-rated verified reviews (excluding specified IDs)
 */
function getTopRatedReviews($limit = 3, $excludeIds = []) {
    global $database;

    $excludeClause = '';
    $params = [];

    if (!empty($excludeIds)) {
        $placeholders = str_repeat('?,', count($excludeIds) - 1) . '?';
        $excludeClause = "AND r.id NOT IN ($placeholders)";
        $params = $excludeIds;
    }

    $params[] = $limit;

    $sql = "
        SELECT
            r.*,
            u.name as customer_name,
            u.email as customer_email,
            s.name as service_name,
            p.name as package_name
        FROM reviews r
        JOIN users u ON r.customer_id = u.id
        LEFT JOIN services s ON r.service_id = s.id
        LEFT JOIN packages p ON r.package_id = p.id
        WHERE r.status = 'verified'
        $excludeClause
        ORDER BY r.rating DESC, r.created_at DESC
        LIMIT ?
    ";

    return $database->fetchAll($sql, $params);
}
