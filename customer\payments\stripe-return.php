<?php
require_once __DIR__ . '/../../config/app.php';

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

$paymentId = $_GET['payment_id'] ?? '';
$paymentIntentId = $_GET['payment_intent'] ?? '';
$paymentIntentClientSecret = $_GET['payment_intent_client_secret'] ?? '';

if (empty($paymentId)) {
    redirect('/customer/payments?error=invalid_payment');
}

global $database;

// Get payment details
$payment = $database->fetch("
    SELECT p.*, b.user_id, b.id as booking_id,
           s.name as service_name, pkg.name as package_name
    FROM payments p
    INNER JOIN bookings b ON p.booking_id = b.id
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN packages pkg ON b.package_id = pkg.id
    WHERE p.id = ? AND b.user_id = ? AND p.payment_gateway = 'STRIPE'
", [$paymentId, $_SESSION['user_id']]);

if (!$payment) {
    redirect('/customer/payments?error=payment_not_found');
}

$serviceName = $payment['service_name'] ?: $payment['package_name'] ?: 'Unknown Service';
$pageTitle = "Payment Result - " . $serviceName;

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<div class="min-h-screen bg-secondary-900 py-8">
    <div class="max-w-md mx-auto px-4">
        <div class="bg-secondary-800 rounded-lg p-6 text-center">
            <div id="payment-status">
                <!-- Status will be populated by JavaScript -->
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-salon-gold mx-auto mb-4"></div>
                <p class="text-white">Verifying your payment...</p>
            </div>
        </div>
        
        <div class="mt-6 text-center">
            <a href="<?= getBasePath() ?>/customer/payments" class="text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Payments
            </a>
        </div>
    </div>
</div>

<script src="https://js.stripe.com/v3/"></script>
<script>
const stripe = Stripe('<?= STRIPE_PUBLIC_KEY ?>');

// Retrieve the payment intent
const clientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');

if (clientSecret) {
    stripe.retrievePaymentIntent(clientSecret).then(({paymentIntent}) => {
        const statusContainer = document.getElementById('payment-status');
        
        switch (paymentIntent.status) {
            case 'succeeded':
                statusContainer.innerHTML = `
                    <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check text-white text-2xl"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-white mb-2">Payment Successful!</h2>
                    <p class="text-gray-400 mb-4">Your payment has been processed successfully.</p>
                    <div class="bg-secondary-700 rounded-lg p-4 mb-4">
                        <p class="text-sm text-gray-400">Payment ID: ${paymentIntent.id}</p>
                        <p class="text-sm text-gray-400">Amount: <?= CURRENCY_SYMBOL ?> <?= number_format($payment['amount']) ?></p>
                    </div>
                `;
                
                // Update payment status on server
                updatePaymentStatus('<?= $paymentId ?>', 'COMPLETED', paymentIntent.id);
                break;
                
            case 'processing':
                statusContainer.innerHTML = `
                    <div class="w-16 h-16 bg-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-clock text-white text-2xl"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-white mb-2">Payment Processing</h2>
                    <p class="text-gray-400 mb-4">Your payment is being processed. You will receive a confirmation email shortly.</p>
                `;
                break;
                
            case 'requires_payment_method':
                statusContainer.innerHTML = `
                    <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-times text-white text-2xl"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-white mb-2">Payment Failed</h2>
                    <p class="text-gray-400 mb-4">Your payment was not successful. Please try again.</p>
                    <a href="<?= getBasePath() ?>/customer/payments/stripe.php?payment_id=<?= $paymentId ?>" 
                       class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        Try Again
                    </a>
                `;
                break;
                
            default:
                statusContainer.innerHTML = `
                    <div class="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-question text-white text-2xl"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-white mb-2">Unknown Status</h2>
                    <p class="text-gray-400 mb-4">Something went wrong. Please contact support.</p>
                `;
                break;
        }
    }).catch(error => {
        document.getElementById('payment-status').innerHTML = `
            <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-exclamation-triangle text-white text-2xl"></i>
            </div>
            <h2 class="text-xl font-semibold text-white mb-2">Error</h2>
            <p class="text-gray-400 mb-4">Failed to retrieve payment status: ${error.message}</p>
        `;
    });
} else {
    document.getElementById('payment-status').innerHTML = `
        <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-exclamation-triangle text-white text-2xl"></i>
        </div>
        <h2 class="text-xl font-semibold text-white mb-2">Invalid Payment</h2>
        <p class="text-gray-400 mb-4">No payment information found.</p>
    `;
}

async function updatePaymentStatus(paymentId, status, stripePaymentId) {
    try {
        const response = await fetch('<?= getBasePath() ?>/api/payments/update-status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                payment_id: paymentId,
                status: status,
                stripe_payment_id: stripePaymentId
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Redirect to success page after a delay
            setTimeout(() => {
                window.location.href = '<?= getBasePath() ?>/customer/payments?success=payment_completed';
            }, 3000);
        }
    } catch (error) {
        console.error('Failed to update payment status:', error);
    }
}
</script>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>
