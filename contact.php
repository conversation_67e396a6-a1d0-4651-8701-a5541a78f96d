<?php
/**
 * Contact Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/contact_functions.php';

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Handle form submission
$message = '';
$messageType = '';
$formData = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            throw new Exception('Invalid security token. Please try again.');
        }

        // Sanitize input data
        $formData = sanitizeContactData($_POST);

        // Validate data
        $errors = validateContactData($formData);

        if (!empty($errors)) {
            throw new Exception(implode('<br>', $errors));
        }

        // Create contact message
        $contactId = createContactMessage($formData);

        if ($contactId) {
            $message = 'Thank you for your message! We\'ll get back to you within 24 hours.';
            $messageType = 'success';

            // Clear form data on success
            $formData = [];

            // Regenerate CSRF token
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        } else {
            throw new Exception('Failed to send message. Please try again.');
        }

    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

$pageTitle = "Contact Us";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Enhanced Contact Page Styles */
.luxury-contact-card {
    background: linear-gradient(135deg, rgba(10, 10, 10, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.luxury-contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    transition: left 0.6s ease;
}

.luxury-contact-card:hover::before {
    left: 100%;
}

.luxury-contact-card:hover {
    transform: translateY(-5px);
    border-color: rgba(212, 175, 55, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1);
}

.contact-form-field {
    transition: all 0.3s ease;
    position: relative;
}

.contact-form-field:focus-within {
    transform: translateY(-2px);
}

.contact-form-field input:focus,
.contact-form-field textarea:focus,
.contact-form-field select:focus {
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.floating-label {
    position: absolute;
    top: 12px;
    left: 16px;
    color: #9CA3AF;
    transition: all 0.3s ease;
    pointer-events: none;
    background: transparent;
}

.floating-label.active {
    top: -8px;
    left: 12px;
    font-size: 0.75rem;
    color: #D4AF37;
    background: #141414;
    padding: 0 4px;
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate {
    opacity: 1;
    transform: translateY(0);
}

.contact-info-item {
    transition: all 0.3s ease;
}

.contact-info-item:hover {
    transform: translateX(10px);
}

.social-icon {
    transition: all 0.3s ease;
}

.social-icon:hover {
    transform: scale(1.2) rotate(10deg);
}

.map-container {
    position: relative;
    overflow: hidden;
}

.map-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(212, 175, 55, 0.1) 0%, transparent 50%, rgba(212, 175, 55, 0.1) 100%);
    pointer-events: none;
    z-index: 1;
}
</style>

    <!-- Enhanced Hero Section -->
    <section class="relative py-32 bg-gradient-to-br from-salon-black via-salon-black to-salon-black overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute top-20 left-10 w-40 h-40 bg-salon-gold/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-20 right-10 w-60 h-60 bg-salon-gold/5 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
        <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/5 via-transparent to-salon-gold/3"></div>

        <!-- Parallax Background -->
        <div class="absolute inset-0 opacity-10">
            <div class="h-full bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-6 text-center">
            <!-- Luxury Badge -->
            <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-salon-gold/20 animate-on-scroll">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                Get In Touch
            </div>

            <h1 class="text-6xl md:text-8xl font-bold font-serif text-white mb-8 leading-tight animate-on-scroll" style="--delay: 0.2s;">
                Contact
                <span class="block md:inline text-transparent bg-clip-text bg-gradient-to-r from-salon-gold via-yellow-400 to-salon-gold">
                    Us
                </span>
            </h1>

            <p class="text-xl md:text-3xl text-gray-300 max-w-5xl mx-auto leading-relaxed mb-12 animate-on-scroll" style="--delay: 0.4s;">
                We'd love to hear from you. Get in touch with our team for appointments, questions, or just to say hello.
                <span class="block mt-4 text-salon-gold text-lg md:text-xl">Your beauty journey starts with a conversation</span>
            </p>

            <!-- Quick Contact Options -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto animate-on-scroll" style="--delay: 0.6s;">
                <a href="tel:+15551234567" class="luxury-contact-card rounded-xl p-6 text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-white font-semibold mb-2">Call Us</h3>
                    <p class="text-gray-400 text-sm">(255) ************</p>
                </a>

                <a href="mailto:<EMAIL>" class="luxury-contact-card rounded-xl p-6 text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-white font-semibold mb-2">Email Us</h3>
                    <p class="text-gray-400 text-sm"><EMAIL></p>
                </a>

                <div class="luxury-contact-card rounded-xl p-6 text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-white font-semibold mb-2">Visit Us</h3>
                    <p class="text-gray-400 text-sm">Downtown District</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Contact Section -->
    <section class="py-32 bg-salon-black relative">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #D4AF37 2px, transparent 2px), radial-gradient(circle at 75% 75%, #D4AF37 2px, transparent 2px); background-size: 50px 50px;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-6 relative">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-20">
                <!-- Enhanced Contact Form -->
                <div class="animate-on-scroll">
                    <div class="luxury-contact-card rounded-3xl p-10">
                        <div class="text-center mb-8">
                            <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                            </div>
                            <h2 class="text-4xl font-bold text-white mb-4">Send us a Message</h2>
                            <p class="text-gray-300">We'll get back to you within 24 hours</p>
                        </div>
                        
                        <?php if ($message): ?>
                            <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-600/20 border border-green-500/30 text-green-300' : 'bg-red-600/20 border border-red-500/30 text-red-300' ?>">
                                <div class="flex items-center">
                                    <i class="<?= $messageType === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle' ?> mr-3"></i>
                                    <div><?= $message ?></div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <form method="POST" class="space-y-8" id="contactForm">
                            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="contact-form-field">
                                    <input type="text" id="name" name="name" required
                                           value="<?= htmlspecialchars($formData['name'] ?? '') ?>"
                                           class="w-full px-6 py-4 bg-secondary-900/80 border border-secondary-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-salon-gold transition-all backdrop-blur-sm"
                                           placeholder="Full Name *">
                                </div>
                                <div class="contact-form-field">
                                    <input type="email" id="email" name="email" required
                                           value="<?= htmlspecialchars($formData['email'] ?? '') ?>"
                                           class="w-full px-6 py-4 bg-secondary-900/80 border border-secondary-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-salon-gold transition-all backdrop-blur-sm"
                                           placeholder="Email Address *">
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="contact-form-field">
                                    <input type="tel" id="phone" name="phone"
                                           value="<?= htmlspecialchars($formData['phone'] ?? '') ?>"
                                           class="w-full px-6 py-4 bg-secondary-900/80 border border-secondary-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-salon-gold transition-all backdrop-blur-sm"
                                           placeholder="Phone Number (Optional)">
                                </div>
                                <div class="contact-form-field">
                                    <select id="subject" name="subject"
                                            class="w-full px-6 py-4 bg-secondary-900/80 border border-secondary-700 rounded-xl text-white focus:outline-none focus:border-salon-gold transition-all backdrop-blur-sm">
                                        <option value="">Select a subject</option>
                                        <option value="appointment" <?= ($formData['subject'] ?? '') === 'appointment' ? 'selected' : '' ?>>Appointment Inquiry</option>
                                        <option value="services" <?= ($formData['subject'] ?? '') === 'services' ? 'selected' : '' ?>>Services Information</option>
                                        <option value="pricing" <?= ($formData['subject'] ?? '') === 'pricing' ? 'selected' : '' ?>>Pricing Questions</option>
                                        <option value="feedback" <?= ($formData['subject'] ?? '') === 'feedback' ? 'selected' : '' ?>>Feedback</option>
                                        <option value="other" <?= ($formData['subject'] ?? '') === 'other' ? 'selected' : '' ?>>Other</option>
                                    </select>
                                </div>
                            </div>

                            <div class="contact-form-field">
                                <textarea id="message" name="message" rows="6" required
                                          class="w-full px-6 py-4 bg-secondary-900/80 border border-secondary-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-salon-gold transition-all resize-none backdrop-blur-sm"
                                          placeholder="Tell us how we can help you... *"><?= htmlspecialchars($formData['message'] ?? '') ?></textarea>
                            </div>

                            <button type="submit" id="submitBtn" class="w-full bg-gradient-to-r from-salon-gold to-yellow-500 hover:from-yellow-500 hover:to-salon-gold text-black font-bold py-4 px-8 rounded-xl transition-all hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                                <span id="submitText">Send Message</span>
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Enhanced Contact Information -->
                <div class="space-y-8 animate-on-scroll" style="--delay: 0.3s;">
                    <!-- Contact Details -->
                    <div class="luxury-contact-card rounded-3xl p-10">
                        <div class="text-center mb-8">
                            <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-3xl font-bold text-white mb-4">Get in Touch</h3>
                            <p class="text-gray-300">Multiple ways to reach us</p>
                        </div>
                        
                        <div class="space-y-8">
                            <div class="contact-info-item flex items-start space-x-6 p-6 rounded-xl bg-secondary-900/50 border border-secondary-700/50">
                                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-xl font-semibold text-white mb-2">Visit Our Salon</h4>
                                    <p class="text-gray-300 leading-relaxed">123 Beauty Street<br>Downtown District<br>City, State 12345</p>
                                    <a href="https://maps.google.com" target="_blank" class="inline-flex items-center text-salon-gold hover:text-yellow-400 transition-colors mt-2">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                                        </svg>
                                        Get Directions
                                    </a>
                                </div>
                            </div>

                            <div class="contact-info-item flex items-start space-x-6 p-6 rounded-xl bg-secondary-900/50 border border-secondary-700/50">
                                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-xl font-semibold text-white mb-2">Call Us</h4>
                                    <p class="text-gray-300">
                                        <a href="tel:+2557875774355" class="text-salon-gold hover:text-yellow-400 transition-colors text-lg font-medium">(255) ************</a><br>
                                        <span class="text-gray-400">Mon-Sat: 9AM-7PM</span>
                                    </p>
                                </div>
                            </div>

                            <div class="contact-info-item flex items-start space-x-6 p-6 rounded-xl bg-secondary-900/50 border border-secondary-700/50">
                                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-xl font-semibold text-white mb-2">Email Us</h4>
                                    <p class="text-gray-300">
                                        <a href="mailto:<EMAIL>" class="text-salon-gold hover:text-yellow-400 transition-colors"><EMAIL></a><br>
                                        <a href="mailto:<EMAIL>" class="text-salon-gold hover:text-yellow-400 transition-colors"><EMAIL></a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Business Hours -->
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-8">
                        <h3 class="text-2xl font-bold text-white mb-6">Business Hours</h3>
                        
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">Monday</span>
                                <span class="text-white font-semibold">9:00 AM - 7:00 PM</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">Tuesday</span>
                                <span class="text-white font-semibold">9:00 AM - 7:00 PM</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">Wednesday</span>
                                <span class="text-white font-semibold">9:00 AM - 7:00 PM</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">Thursday</span>
                                <span class="text-white font-semibold">9:00 AM - 8:00 PM</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">Friday</span>
                                <span class="text-white font-semibold">9:00 AM - 8:00 PM</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">Saturday</span>
                                <span class="text-white font-semibold">8:00 AM - 6:00 PM</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">Sunday</span>
                                <span class="text-red-400 font-semibold">Closed</span>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-8">
                        <h3 class="text-2xl font-bold text-white mb-6">Follow Us</h3>
                        
                        <div class="flex space-x-4">
                            <a href="#" class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center text-salon-gold hover:bg-salon-gold hover:text-black transition-colors">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center text-salon-gold hover:bg-salon-gold hover:text-black transition-colors">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center text-salon-gold hover:bg-salon-gold hover:text-black transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center text-salon-gold hover:bg-salon-gold hover:text-black transition-colors">
                                <i class="fab fa-youtube"></i>
                            </a>
                        </div>
                        
                        <p class="text-gray-300 text-sm mt-4">
                            Stay updated with our latest work, beauty tips, and special offers by following us on social media.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="py-16 bg-salon-black">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-4">
                    Find <span class="text-salon-gold">Our Location</span>
                </h2>
                <p class="text-xl text-gray-300">
                    Located in the heart of downtown, easily accessible by car or public transport.
                </p>
            </div>

            <div class="bg-secondary-900 rounded-2xl p-8 text-center border border-secondary-700">
                <div class="aspect-video bg-gradient-to-br from-salon-gold/20 to-secondary-900 rounded-xl flex items-center justify-center">
                    <div class="text-center">
                        <i class="fas fa-map-marked-alt text-salon-gold/60 text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">Interactive Map</h3>
                        <p class="text-gray-300">123 Beauty Street, Downtown District</p>
                        <a href="https://maps.google.com" target="_blank" class="inline-block mt-4 bg-salon-gold hover:bg-yellow-500 text-black px-6 py-2 rounded-lg font-semibold transition-colors">
                            <i class="fas fa-directions mr-2"></i>Get Directions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

<script>
// Enhanced Contact Page Interactions
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contactForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');

    // Form submission handling
    if (form) {
        form.addEventListener('submit', function(e) {
            // Show loading state
            submitBtn.disabled = true;
            submitText.textContent = 'Sending...';

            // Add a small delay to show the loading state
            setTimeout(() => {
                // Let the form submit normally
                if (!e.defaultPrevented) {
                    // Form will submit normally
                }
            }, 100);
        });

        // Reset button state if form submission fails
        window.addEventListener('pageshow', function() {
            submitBtn.disabled = false;
            submitText.textContent = 'Send Message';
        });
    }

    // Auto-resize textarea
    const messageTextarea = document.getElementById('message');
    if (messageTextarea) {
        messageTextarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    }

    // Scroll animations
    function handleScrollAnimations() {
        const elements = document.querySelectorAll('.animate-on-scroll');

        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;

            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('animate');
            }
        });
    }

    // Intersection Observer for better performance
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, observerOptions);

    // Observe all animated elements
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });

    // Enhanced form field interactions
    document.querySelectorAll('.contact-form-field input, .contact-form-field textarea, .contact-form-field select').forEach(field => {
        field.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
        });

        field.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
        });
    });

    // Social icon hover effects
    document.querySelectorAll('.social-icon').forEach(icon => {
        icon.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.2) rotate(10deg)';
        });

        icon.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // Contact info item hover effects
    document.querySelectorAll('.contact-info-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(10px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });

    // Initial check for elements already in view
    handleScrollAnimations();

    // Add loading animation to page
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';

    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
