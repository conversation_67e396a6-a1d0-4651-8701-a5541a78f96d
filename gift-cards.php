<?php
/**
 * Gift Cards Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "Gift Cards";
include __DIR__ . '/includes/header.php';
?>

<!-- Hero Section -->
<section class="relative bg-salon-black py-24">
    <div class="absolute inset-0 bg-gradient-to-r from-salon-black via-salon-black/90 to-transparent"></div>
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30" 
         style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-3xl">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                Gift <span class="text-salon-gold">Cards</span>
            </h1>
            <p class="text-xl text-gray-300 mb-8">
                Give the gift of beauty and relaxation. Perfect for any occasion, our gift cards never expire and can be used for any service.
            </p>
        </div>
    </div>
</section>

<!-- Gift Card Options -->
<section class="py-20 bg-secondary-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Popular Amounts -->
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-white mb-8">Choose Your Gift Card Amount</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <?php
                $amounts = [
                    ['amount' => 50, 'popular' => false, 'description' => 'Perfect for basic services'],
                    ['amount' => 100, 'popular' => true, 'description' => 'Most popular choice'],
                    ['amount' => 150, 'popular' => false, 'description' => 'Great for packages'],
                    ['amount' => 200, 'popular' => false, 'description' => 'Luxury experience']
                ];
                
                foreach ($amounts as $card): ?>
                    <div class="relative">
                        <?php if ($card['popular']): ?>
                            <div class="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                                <span class="bg-salon-gold text-black px-3 py-1 rounded-full text-sm font-bold">
                                    Most Popular
                                </span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="bg-gradient-to-br from-salon-gold/20 to-secondary-800 border border-salon-gold/30 rounded-lg p-6 hover:scale-105 transition-transform duration-300 cursor-pointer gift-card-option <?= $card['popular'] ? 'ring-2 ring-salon-gold' : '' ?>" 
                             data-amount="<?= $card['amount'] ?>">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-salon-gold mb-2">TSH <?= number_format($card['amount'] * 1000) ?></div>
                                <p class="text-gray-300 text-sm"><?= $card['description'] ?></p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Custom Amount -->
            <div class="bg-secondary-800 rounded-lg p-6 max-w-md mx-auto">
                <h3 class="text-lg font-semibold text-white mb-4">Custom Amount</h3>
                <div class="flex items-center">
                    <span class="text-salon-gold text-xl mr-2">TSH</span>
                    <input type="number" id="customAmount" min="25" max="1000" step="1" placeholder="Enter amount"
                           class="flex-1 px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>
                <p class="text-gray-400 text-sm mt-2">Minimum TSH 25,000, Maximum TSH 1,000,000</p>
            </div>
        </div>

        <!-- Gift Card Design -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Choose Your Design</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <?php
                $designs = [
                    ['id' => 'classic', 'name' => 'Classic Gold', 'preview' => 'bg-gradient-to-br from-salon-gold to-yellow-600'],
                    ['id' => 'elegant', 'name' => 'Elegant Black', 'preview' => 'bg-gradient-to-br from-salon-black to-secondary-800'],
                    ['id' => 'modern', 'name' => 'Modern Silver', 'preview' => 'bg-gradient-to-br from-gray-400 to-gray-600']
                ];
                
                foreach ($designs as $design): ?>
                    <div class="gift-card-design cursor-pointer" data-design="<?= $design['id'] ?>">
                        <div class="<?= $design['preview'] ?> rounded-lg p-8 text-center hover:scale-105 transition-transform duration-300 border-2 border-transparent hover:border-salon-gold">
                            <div class="text-white font-bold text-xl mb-2">Flix Salon & SPA</div>
                            <div class="text-white/80 text-sm mb-4">Gift Card</div>
                            <div class="text-white/60 text-xs">TSH XXX,XXX</div>
                        </div>
                        <p class="text-center text-white mt-3 font-semibold"><?= $design['name'] ?></p>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Purchase Form -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-secondary-800 rounded-lg p-8">
                <h2 class="text-2xl font-bold text-white mb-6 text-center">Gift Card Details</h2>
                
                <form id="giftCardForm" class="space-y-6">
                    <!-- Recipient Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-salon-gold mb-4">Recipient Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-white font-semibold mb-2">Recipient Name</label>
                                <input type="text" required class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold" placeholder="Enter recipient's name">
                            </div>
                            <div>
                                <label class="block text-white font-semibold mb-2">Recipient Email</label>
                                <input type="email" required class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold" placeholder="Enter recipient's email">
                            </div>
                        </div>
                    </div>

                    <!-- Personal Message -->
                    <div>
                        <label class="block text-white font-semibold mb-2">Personal Message (Optional)</label>
                        <textarea rows="3" class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold" placeholder="Add a personal message..."></textarea>
                    </div>

                    <!-- Delivery Options -->
                    <div>
                        <h3 class="text-lg font-semibold text-salon-gold mb-4">Delivery Options</h3>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="radio" name="delivery" value="email" checked class="text-salon-gold focus:ring-salon-gold">
                                <span class="ml-3 text-white">Email Delivery (Instant)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="delivery" value="scheduled" class="text-salon-gold focus:ring-salon-gold">
                                <span class="ml-3 text-white">Schedule for Later</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="delivery" value="physical" class="text-salon-gold focus:ring-salon-gold">
                                <span class="ml-3 text-white">Physical Card (Pick up in salon)</span>
                            </label>
                        </div>
                    </div>

                    <!-- Scheduled Delivery Date -->
                    <div id="scheduledDate" class="hidden">
                        <label class="block text-white font-semibold mb-2">Delivery Date</label>
                        <input type="date" class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <!-- Purchaser Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-salon-gold mb-4">Your Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-white font-semibold mb-2">Your Name</label>
                                <input type="text" required class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold" placeholder="Enter your name">
                            </div>
                            <div>
                                <label class="block text-white font-semibold mb-2">Your Email</label>
                                <input type="email" required class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold" placeholder="Enter your email">
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="bg-secondary-700 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Order Summary</h3>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-300">Gift Card Amount:</span>
                            <span class="text-white font-semibold" id="summaryAmount">TSH 0.00</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-300">Processing Fee:</span>
                            <span class="text-white">TSH 0.00</span>
                        </div>
                        <hr class="border-secondary-600 my-3">
                        <div class="flex justify-between items-center">
                            <span class="text-salon-gold font-semibold">Total:</span>
                            <span class="text-salon-gold font-bold text-xl" id="summaryTotal">TSH 0.00</span>
                        </div>
                    </div>

                    <!-- Purchase Button -->
                    <button type="submit" class="w-full bg-salon-gold hover:bg-yellow-500 text-black py-4 rounded-lg font-bold text-lg transition-colors">
                        Purchase Gift Card
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Gift Card Benefits -->
<section class="py-20 bg-salon-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-white mb-4">Why Choose Our Gift Cards?</h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-infinity text-salon-gold text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Never Expire</h3>
                <p class="text-gray-300 text-sm">Our gift cards never expire, so recipients can use them whenever they're ready.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-spa text-salon-gold text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">All Services</h3>
                <p class="text-gray-300 text-sm">Can be used for any service, package, or product in our salon.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-mobile-alt text-salon-gold text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Digital & Physical</h3>
                <p class="text-gray-300 text-sm">Choose between instant digital delivery or beautiful physical cards.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shield-alt text-salon-gold text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Secure & Safe</h3>
                <p class="text-gray-300 text-sm">Protected by unique codes and can be replaced if lost or stolen.</p>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16 bg-secondary-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-white text-center mb-12">Gift Card FAQ</h2>
        
        <div class="space-y-6">
            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-2">How do I redeem a gift card?</h3>
                <p class="text-gray-300">Simply present your gift card code when booking online or mention it when calling to schedule. The amount will be applied to your service total.</p>
            </div>

            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-2">Can I check my gift card balance?</h3>
                <p class="text-gray-300">Yes! Contact us with your gift card code and we'll check your remaining balance. You can also check online through your account.</p>
            </div>

            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-2">What if my service costs more than the gift card amount?</h3>
                <p class="text-gray-300">You can pay the difference using any of our accepted payment methods. Multiple gift cards can also be combined for larger purchases.</p>
            </div>

            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-2">Can gift cards be refunded?</h3>
                <p class="text-gray-300">Gift cards are non-refundable but never expire. They can be transferred to another person if needed.</p>
            </div>
        </div>
    </div>
</section>

<script>
// Gift card amount selection
document.querySelectorAll('.gift-card-option').forEach(option => {
    option.addEventListener('click', function() {
        // Remove active class from all options
        document.querySelectorAll('.gift-card-option').forEach(opt => {
            opt.classList.remove('ring-2', 'ring-salon-gold');
        });
        
        // Add active class to clicked option
        this.classList.add('ring-2', 'ring-salon-gold');
        
        // Update amount
        const amount = this.dataset.amount;
        document.getElementById('customAmount').value = '';
        updateSummary(amount);
    });
});

// Custom amount input
document.getElementById('customAmount').addEventListener('input', function() {
    // Remove active class from preset options
    document.querySelectorAll('.gift-card-option').forEach(opt => {
        opt.classList.remove('ring-2', 'ring-salon-gold');
    });
    
    updateSummary(this.value);
});

// Delivery option change
document.querySelectorAll('input[name="delivery"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const scheduledDate = document.getElementById('scheduledDate');
        if (this.value === 'scheduled') {
            scheduledDate.classList.remove('hidden');
        } else {
            scheduledDate.classList.add('hidden');
        }
    });
});

// Update order summary
function updateSummary(amount) {
    const amountNum = parseInt(amount) || 0;
    document.getElementById('summaryAmount').textContent = 'TSH ' + amountNum.toLocaleString();
    document.getElementById('summaryTotal').textContent = 'TSH ' + amountNum.toLocaleString();
}

// Form submission
document.getElementById('giftCardForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('Gift card purchase functionality would be implemented here with payment processing.');
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
