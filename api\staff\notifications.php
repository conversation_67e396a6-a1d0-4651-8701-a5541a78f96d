<?php
/**
 * Staff Notifications API
 * Handles notification operations for staff members
 * Flix Salonce - PHP Version
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Check authentication and staff role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$staffId = $_SESSION['user_id'];

try {
    switch ($method) {
        case 'GET':
            // Get notification ID from query string
            $notificationId = $_GET['id'] ?? null;
            
            if ($notificationId) {
                // Get single notification
                $notification = getStaffNotifications($staffId, [
                    'limit' => 1,
                    'offset' => 0,
                    'id' => $notificationId
                ]);
                
                if (empty($notification)) {
                    http_response_code(404);
                    echo json_encode(['error' => 'Notification not found']);
                    exit;
                }
                
                echo json_encode(['data' => ['notification' => $notification[0]]]);
            } else {
                try {
                    // Get all notifications with filters
                    $notifications = getStaffNotifications($staffId, [
                        'limit' => $_GET['limit'] ?? 20,
                        'offset' => $_GET['offset'] ?? 0,
                        'category' => $_GET['category'] ?? 'all',
                        'status' => $_GET['status'] ?? 'all',
                        'search' => $_GET['search'] ?? '',
                        'sort' => $_GET['sort'] ?? 'newest'
                    ]);
                    
                    $totalCount = getStaffNotificationsCount($staffId, [
                        'category' => $_GET['category'] ?? 'all',
                        'status' => $_GET['status'] ?? 'all',
                        'search' => $_GET['search'] ?? ''
                    ]);

                    // Get category counts
                    $categoryCounts = getNotificationCategoryCounts($staffId);
                    
                    echo json_encode([
                        'data' => [
                            'notifications' => $notifications,
                            'counts' => [
                                'total' => $totalCount,
                                'unread' => $categoryCounts['unread'],
                                'read' => $categoryCounts['read'],
                                'categories' => $categoryCounts['categories']
                            ]
                        ]
                    ]);
                } catch (Exception $e) {
                    error_log("Error fetching notifications: " . $e->getMessage());
                    http_response_code(500);
                    echo json_encode([
                        'error' => 'Failed to fetch notifications',
                        'data' => [
                            'notifications' => [],
                            'counts' => [
                                'total' => 0,
                                'unread' => 0,
                                'read' => 0,
                                'categories' => []
                            ]
                        ]
                    ]);
                }
            }
            break;
            
        case 'PUT':
            // Get request body
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($data['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Notification ID is required']);
                exit;
            }
            
            // Mark notification as read
            if (isset($data['is_read'])) {
                $success = markNotificationAsRead($data['id'], $staffId);
                if ($success) {
                    echo json_encode(['data' => ['message' => 'Notification marked as read']]);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'Notification not found']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid request']);
            }
            break;
            
        case 'DELETE':
            // Get notification ID from query string
            $notificationId = $_GET['id'] ?? null;
            
            if ($notificationId) {
                // Delete single notification
                $success = deleteNotification($notificationId, $staffId);
                if ($success) {
                    echo json_encode(['data' => ['message' => 'Notification deleted']]);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'Notification not found']);
                }
            } else {
                // Delete all notifications
                $success = deleteAllNotifications($staffId);
                if ($success) {
                    echo json_encode(['data' => ['message' => 'All notifications deleted']]);
                } else {
                    http_response_code(500);
                    echo json_encode(['error' => 'Failed to delete notifications']);
                }
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    error_log("Error in notifications API: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'data' => [
            'notifications' => [],
            'counts' => [
                'total' => 0,
                'unread' => 0,
                'read' => 0,
                'categories' => []
            ]
        ]
    ]);
}
