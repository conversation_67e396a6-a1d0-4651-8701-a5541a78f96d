<?php
/**
 * Privacy Policy Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "Privacy Policy";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Enhanced Privacy Policy Styles */
.privacy-card {
    background: linear-gradient(135deg, rgba(10, 10, 10, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.privacy-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    transition: left 0.6s ease;
}

.privacy-card:hover::before {
    left: 100%;
}

.privacy-card:hover {
    transform: translateY(-5px);
    border-color: rgba(212, 175, 55, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1);
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate {
    opacity: 1;
    transform: translateY(0);
}

.privacy-section {
    transition: all 0.3s ease;
}

.privacy-section:hover {
    transform: translateX(10px);
}

.privacy-icon {
    transition: all 0.3s ease;
}

.privacy-icon:hover {
    transform: scale(1.1) rotate(5deg);
}

.highlight-box {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
    border: 1px solid rgba(212, 175, 55, 0.2);
    position: relative;
}

.highlight-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #D4AF37, #F59E0B);
}
</style>

<!-- Enhanced Hero Section -->
<section class="relative py-32 bg-gradient-to-br from-salon-black via-salon-black to-salon-black overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute top-20 left-10 w-40 h-40 bg-salon-gold/10 rounded-full blur-3xl animate-pulse"></div>
    <div class="absolute bottom-20 right-10 w-60 h-60 bg-salon-gold/5 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
    <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/5 via-transparent to-salon-gold/3"></div>

    <!-- Parallax Background -->
    <div class="absolute inset-0 opacity-10">
        <div class="h-full bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1556742502-ec7c0e9f34b1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-6 text-center">
        <!-- Luxury Badge -->
        <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-salon-gold/20 animate-on-scroll">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            Data Protection
        </div>

        <h1 class="text-6xl md:text-8xl font-bold font-serif text-white mb-8 leading-tight animate-on-scroll" style="--delay: 0.2s;">
            Privacy
            <span class="block md:inline text-transparent bg-clip-text bg-gradient-to-r from-salon-gold via-yellow-400 to-salon-gold">
                Policy
            </span>
        </h1>

        <p class="text-xl md:text-3xl text-gray-300 max-w-5xl mx-auto leading-relaxed mb-12 animate-on-scroll" style="--delay: 0.4s;">
            We value your privacy and are committed to protecting your personal information.
            <span class="block mt-4 text-salon-gold text-lg md:text-xl">Last updated: <?= date('F d, Y') ?></span>
        </p>

        <!-- Quick Privacy Highlights -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto animate-on-scroll" style="--delay: 0.6s;">
            <div class="privacy-card rounded-xl p-6 text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-white font-semibold mb-2">Data Security</h3>
                <p class="text-gray-400 text-sm">Your information is secure</p>
            </div>

            <div class="privacy-card rounded-xl p-6 text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </div>
                <h3 class="text-white font-semibold mb-2">Transparency</h3>
                <p class="text-gray-400 text-sm">Clear data usage policies</p>
            </div>

            <div class="privacy-card rounded-xl p-6 text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-white font-semibold mb-2">Your Control</h3>
                <p class="text-gray-400 text-sm">Manage your data preferences</p>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Privacy Content -->
<section class="py-32 bg-salon-black relative">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #D4AF37 2px, transparent 2px), radial-gradient(circle at 75% 75%, #D4AF37 2px, transparent 2px); background-size: 50px 50px;"></div>
    </div>

    <div class="max-w-6xl mx-auto px-6 relative">

        <!-- Introduction -->
        <div class="privacy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Introduction</h2>
                <p class="text-gray-300">Our commitment to your privacy</p>
            </div>
            
            <div class="space-y-6 text-gray-300">
                <p>
                    At Flix Salon & SPA, we respect your privacy and are committed to protecting your personal data. This privacy policy will inform you about how we look after your personal data when you visit our website and tell you about your privacy rights and how the law protects you.
                </p>
                <p>
                    This privacy policy aims to give you information on how Flix Salon & SPA collects and processes your personal data through your use of this website, including any data you may provide through this website when you sign up for our newsletter, create an account, book a service, or purchase a product.
                </p>
                <p>
                    It is important that you read this privacy policy together with any other privacy notice or fair processing notice we may provide on specific occasions when we are collecting or processing personal data about you so that you are fully aware of how and why we are using your data.
                </p>
            </div>
        </div>

        <!-- Information We Collect -->
        <div class="privacy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Information We Collect</h2>
                <p class="text-gray-300">Types of data we collect and process</p>
            </div>
            
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-semibold text-white mb-3">Personal Information</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Name, email address, phone number, and contact details</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Account information including username and password</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Booking history and preferences</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Payment information (we do not store complete credit card details)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Communications with us including feedback and reviews</span>
                        </li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-white mb-3">Technical Information</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>IP address and device information</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Browser type and version</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Time zone setting and location</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                            <span>Browsing actions and patterns on our website</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- How We Use Your Information -->
        <div class="privacy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">How We Use Your Information</h2>
                <p class="text-gray-300">Purposes for which we use your data</p>
            </div>
            
            <div class="space-y-6 text-gray-300">
                <p>
                    We use your personal information for the following purposes:
                </p>
                <ul class="space-y-4">
                    <li class="flex items-start">
                        <div class="flex-shrink-0 h-6 w-6 rounded-full bg-salon-gold/20 flex items-center justify-center mt-0.5">
                            <span class="text-salon-gold text-sm font-bold">1</span>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-white font-medium">To provide our services</h4>
                            <p class="text-gray-400 mt-1">Managing your account, processing bookings, and providing customer service</p>
                        </div>
                    </li>
                    <li class="flex items-start">
                        <div class="flex-shrink-0 h-6 w-6 rounded-full bg-salon-gold/20 flex items-center justify-center mt-0.5">
                            <span class="text-salon-gold text-sm font-bold">2</span>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-white font-medium">To improve our services</h4>
                            <p class="text-gray-400 mt-1">Analyzing usage patterns and enhancing user experience</p>
                        </div>
                    </li>
                    <li class="flex items-start">
                        <div class="flex-shrink-0 h-6 w-6 rounded-full bg-salon-gold/20 flex items-center justify-center mt-0.5">
                            <span class="text-salon-gold text-sm font-bold">3</span>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-white font-medium">To communicate with you</h4>
                            <p class="text-gray-400 mt-1">Sending appointment confirmations, reminders, and important updates</p>
                        </div>
                    </li>
                    <li class="flex items-start">
                        <div class="flex-shrink-0 h-6 w-6 rounded-full bg-salon-gold/20 flex items-center justify-center mt-0.5">
                            <span class="text-salon-gold text-sm font-bold">4</span>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-white font-medium">For marketing purposes</h4>
                            <p class="text-gray-400 mt-1">Sending promotional offers and newsletters (with your consent)</p>
                        </div>
                    </li>
                    <li class="flex items-start">
                        <div class="flex-shrink-0 h-6 w-6 rounded-full bg-salon-gold/20 flex items-center justify-center mt-0.5">
                            <span class="text-salon-gold text-sm font-bold">5</span>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-white font-medium">For legal compliance</h4>
                            <p class="text-gray-400 mt-1">Meeting our legal obligations and protecting our rights</p>
                        </div>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Cookies and Tracking -->
        <div class="privacy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Cookies and Tracking</h2>
                <p class="text-gray-300">How we use cookies and similar technologies</p>
            </div>
            
            <div class="space-y-6 text-gray-300">
                <p>
                    Our website uses cookies and similar tracking technologies to distinguish you from other users of our website. This helps us to provide you with a good experience when you browse our website and also allows us to improve our site.
                </p>
                <div class="highlight-box p-6 rounded-xl">
                    <h4 class="text-white font-semibold mb-2">Types of Cookies We Use</h4>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-cookie-bite text-salon-gold mr-2 mt-1"></i>
                            <span><strong class="text-white">Essential Cookies:</strong> Required for the website to function properly</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-cookie-bite text-salon-gold mr-2 mt-1"></i>
                            <span><strong class="text-white">Functional Cookies:</strong> Remember your preferences and settings</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-cookie-bite text-salon-gold mr-2 mt-1"></i>
                            <span><strong class="text-white">Analytical Cookies:</strong> Help us understand how visitors interact with our website</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-cookie-bite text-salon-gold mr-2 mt-1"></i>
                            <span><strong class="text-white">Marketing Cookies:</strong> Track your browsing habits to deliver targeted advertising</span>
                        </li>
                    </ul>
                </div>
                <p>
                    You can set your browser to refuse all or some browser cookies, or to alert you when websites set or access cookies. If you disable or refuse cookies, please note that some parts of this website may become inaccessible or not function properly.
                </p>
            </div>
        </div>

        <!-- Data Sharing and Third Parties -->
        <div class="privacy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Data Sharing and Third Parties</h2>
                <p class="text-gray-300">Who we share your data with</p>
            </div>
            
            <div class="space-y-6 text-gray-300">
                <p>
                    We may share your personal information with the following categories of third parties:
                </p>
                <ul class="space-y-4">
                    <li class="flex items-start">
                        <i class="fas fa-share-alt text-salon-gold mr-2 mt-1"></i>
                        <div>
                            <h4 class="text-white font-medium">Service Providers</h4>
                            <p class="text-gray-400 mt-1">IT and system administration services, payment processors, and other service providers who help us run our business</p>
                        </div>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-share-alt text-salon-gold mr-2 mt-1"></i>
                        <div>
                            <h4 class="text-white font-medium">Professional Advisers</h4>
                            <p class="text-gray-400 mt-1">Lawyers, bankers, auditors, and insurers who provide consultancy, banking, legal, insurance, and accounting services</p>
                        </div>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-share-alt text-salon-gold mr-2 mt-1"></i>
                        <div>
                            <h4 class="text-white font-medium">Regulatory Authorities</h4>
                            <p class="text-gray-400 mt-1">Government bodies that require reporting of processing activities in certain circumstances</p>
                        </div>
                    </li>
                </ul>
                <p class="mt-4">
                    We require all third parties to respect the security of your personal data and to treat it in accordance with the law. We do not allow our third-party service providers to use your personal data for their own purposes and only permit them to process your personal data for specified purposes and in accordance with our instructions.
                </p>
            </div>
        </div>

        <!-- Data Security -->
        <div class="privacy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Data Security</h2>
                <p class="text-gray-300">How we protect your information</p>
            </div>
            
            <div class="space-y-6 text-gray-300">
                <p>
                    We have put in place appropriate security measures to prevent your personal data from being accidentally lost, used, or accessed in an unauthorized way, altered, or disclosed. In addition, we limit access to your personal data to those employees, agents, contractors, and other third parties who have a business need to know.
                </p>
                <p>
                    We have put in place procedures to deal with any suspected personal data breach and will notify you and any applicable regulator of a breach where we are legally required to do so.
                </p>
                <div class="highlight-box p-6 rounded-xl">
                    <h4 class="text-white font-semibold mb-2">Our Security Measures Include</h4>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-shield-alt text-salon-gold mr-2 mt-1"></i>
                            <span>Encryption of sensitive data</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-shield-alt text-salon-gold mr-2 mt-1"></i>
                            <span>Regular security assessments</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-shield-alt text-salon-gold mr-2 mt-1"></i>
                            <span>Secure authentication processes</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-shield-alt text-salon-gold mr-2 mt-1"></i>
                            <span>Staff training on data protection</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Your Rights -->
        <div class="privacy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Your Rights</h2>
                <p class="text-gray-300">Control over your personal data</p>
            </div>
            
            <div class="space-y-6 text-gray-300">
                <p>
                    Under certain circumstances, you have rights under data protection laws in relation to your personal data, including the right to:
                </p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="privacy-card p-6 rounded-xl">
                        <h4 class="text-white font-semibold mb-2">Request access</h4>
                        <p class="text-gray-400">Receive a copy of your personal data</p>
                    </div>
                    <div class="privacy-card p-6 rounded-xl">
                        <h4 class="text-white font-semibold mb-2">Request correction</h4>
                        <p class="text-gray-400">Have incomplete or inaccurate data corrected</p>
                    </div>
                    <div class="privacy-card p-6 rounded-xl">
                        <h4 class="text-white font-semibold mb-2">Request erasure</h4>
                        <p class="text-gray-400">Ask us to delete your personal data</p>
                    </div>
                    <div class="privacy-card p-6 rounded-xl">
                        <h4 class="text-white font-semibold mb-2">Object to processing</h4>
                        <p class="text-gray-400">Ask us to stop using your data</p>
                    </div>
                    <div class="privacy-card p-6 rounded-xl">
                        <h4 class="text-white font-semibold mb-2">Request restriction</h4>
                        <p class="text-gray-400">Ask us to limit how we use your data</p>
                    </div>
                    <div class="privacy-card p-6 rounded-xl">
                        <h4 class="text-white font-semibold mb-2">Data portability</h4>
                        <p class="text-gray-400">Transfer your data to another organization</p>
                    </div>
                </div>
                <p class="mt-4">
                    If you wish to exercise any of these rights, please contact us using the details provided below.
                </p>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="privacy-card rounded-3xl p-10 mb-12 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Contact Us</h2>
                <p class="text-gray-300">How to reach us with privacy questions</p>
            </div>
            
            <div class="space-y-6 text-gray-300">
                <p>
                    If you have any questions about this privacy policy or our privacy practices, please contact us at:
                </p>
                <div class="highlight-box p-6 rounded-xl">
                    <ul class="space-y-4">
                        <li class="flex items-start">
                            <i class="fas fa-building text-salon-gold mr-2 mt-1"></i>
                            <div>
                                <h4 class="text-white font-medium">Flix Salon & SPA</h4>
                                <p class="text-gray-400 mt-1">Upanga, Dar Es Salaam, Tanzania</p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-envelope text-salon-gold mr-2 mt-1"></i>
                            <div>
                                <h4 class="text-white font-medium">Email</h4>
                                <p class="text-gray-400 mt-1"><EMAIL></p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-phone text-salon-gold mr-2 mt-1"></i>
                            <div>
                                <h4 class="text-white font-medium">Phone</h4>
                                <p class="text-gray-400 mt-1">(255) 745 456-789</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Changes to Privacy Policy -->
        <div class="privacy-card rounded-3xl p-10 animate-on-scroll">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Changes to This Privacy Policy</h2>
                <p class="text-gray-300">How we update our policies</p>
            </div>
            
            <div class="space-y-6 text-gray-300">
                <p>
                    We may update this privacy policy from time to time. The updated version will be indicated by an updated "Last updated" date at the top of this page. We encourage you to review this privacy policy frequently to be informed of how we are protecting your information.
                </p>
                <p>
                    If we make material changes to this privacy policy, we may notify you either through the email address you have provided us or by placing a prominent notice on our website.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-20 bg-gradient-to-br from-salon-black to-black relative overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
    </div>
    
    <div class="max-w-5xl mx-auto px-6 text-center relative">
        <h2 class="text-4xl font-bold text-white mb-6 animate-on-scroll">Have Questions About Your Privacy?</h2>
        <p class="text-xl text-gray-300 mb-10 animate-on-scroll" style="--delay: 0.2s;">
            Our team is here to help you understand how we protect your personal information.
        </p>
        <a href="<?= getBasePath() ?>/contact" class="inline-flex items-center px-8 py-4 bg-salon-gold text-black font-semibold rounded-full hover:bg-gold-light transition-colors animate-on-scroll" style="--delay: 0.4s;">
            Contact Us
            <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
            </svg>
        </a>
    </div>
</section>

<script>
// Handle scroll animations
function handleScrollAnimations() {
    const elements = document.querySelectorAll('.animate-on-scroll:not(.animate)');
    
    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementBottom = element.getBoundingClientRect().bottom;
        const windowHeight = window.innerHeight;
        
        if (elementTop < windowHeight * 0.9 && elementBottom > 0) {
            const delay = element.style.getPropertyValue('--delay') || '0s';
            setTimeout(() => {
                element.classList.add('animate');
            }, parseFloat(delay) * 1000);
        }
    });
}

// Set up Intersection Observer for scroll animations
if ('IntersectionObserver' in window) {
    const observer = new IntersectionObserver(entries => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const delay = entry.target.style.getPropertyValue('--delay') || '0s';
                setTimeout(() => {
                    entry.target.classList.add('animate');
                }, parseFloat(delay) * 1000);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    document.querySelectorAll('.animate-on-scroll').forEach(element => {
        observer.observe(element);
    });
} else {
    // Fallback for browsers that don't support Intersection Observer
    window.addEventListener('scroll', handleScrollAnimations);
    handleScrollAnimations();
}

// Check for elements in view on page load
document.addEventListener('DOMContentLoaded', () => {
    handleScrollAnimations();
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>