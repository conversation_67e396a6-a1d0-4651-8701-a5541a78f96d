-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 08, 2025 at 06:08 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `flix_salonce2`
--

-- --------------------------------------------------------

--
-- Table structure for table `blog`
--

CREATE TABLE `blog` (
  `id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `excerpt` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_published` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `blog_posts`
--

CREATE TABLE `blog_posts` (
  `id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `summary` text DEFAULT NULL,
  `full_content` longtext NOT NULL,
  `image_url` varchar(2048) DEFAULT NULL,
  `publish_date` datetime DEFAULT NULL,
  `author_id` varchar(36) DEFAULT NULL,
  `status` enum('draft','published','archived') DEFAULT 'draft',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `blog_posts`
--

INSERT INTO `blog_posts` (`id`, `title`, `slug`, `summary`, `full_content`, `image_url`, `publish_date`, `author_id`, `status`, `created_at`, `updated_at`) VALUES
('0274156e-5f36-4f7b-b95e-12ff75639264', 'Test', 'test', '💇‍♀️ Expert Stylists &amp; Therapists\r\nOur stylists are not only highly skilled, but passionate about bringing out your natural beauty. From modern cuts to classic styles, from precision coloring to deep hair repair — you’ll leave Flix looking radiant and feeling rejuvenated.', '✨ Experience the Flix Difference\r\nAt Flix, we do more than just hair and skin — we create moments of transformation. Whether you\'re prepping for a big event or simply need a self-care day, our team of trained professionals is here to make you feel your absolute best.\r\n\r\n💇‍♀️ Expert Stylists & Therapists\r\nOur stylists are not only highly skilled, but passionate about bringing out your natural beauty. From modern cuts to classic styles, from precision coloring to deep hair repair — you’ll leave Flix looking radiant and feeling rejuvenated.\r\n\r\n🌿 Premium Products, Gentle Touch\r\nWe use only the best in beauty — premium, skin-safe, and eco-conscious products that nourish while they enhance. Your hair, skin, and nails will thank you.', '937f919f-f658-4a98-82a0-dae38ea1074a.jpg', '2025-06-07 00:01:00', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'published', '2025-06-06 21:36:03', '2025-06-06 21:55:27'),
('81818206-7cf3-4d17-8530-832afe9dad24', '💖 Unwind, Refresh; Shine at Flix Salon &amp; SPA', 'unwind-refresh-shine-at-flix-salon-amp-spa', 'Welcome to Flix Salon &amp;amp; SPA, where beauty meets relaxation and confidence begins. Nestled in the heart of [Your Location], our salon and spa is your personal haven for expert beauty care, luxurious treatments, and a touch of indulgence you truly deserve.', '✨ Experience the Flix Difference\r\nAt Flix, we do more than just hair and skin — we create moments of transformation. Whether you\'re prepping for a big event or simply need a self-care day, our team of trained professionals is here to make you feel your absolute best.\r\n\r\n💇‍♀️ Expert Stylists & Therapists\r\nOur stylists are not only highly skilled, but passionate about bringing out your natural beauty. From modern cuts to classic styles, from precision coloring to deep hair repair — you’ll leave Flix looking radiant and feeling rejuvenated.\r\n\r\n🌿 Premium Products, Gentle Touch\r\nWe use only the best in beauty — premium, skin-safe, and eco-conscious products that nourish while they enhance. Your hair, skin, and nails will thank you.', 'https://img.freepik.com/free-photo/side-view-woman-talking-video-call_23-2150812833.jpg', '2025-06-05 12:01:00', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'published', '2025-06-04 01:47:20', '2025-06-06 11:01:30'),
('b6df4e5f-6952-4771-adde-87fd95074c4c', 'Test 2', 'test-2', '🌿 Premium Products, Gentle Touch\r\nWe use only the best in beauty — premium, skin-safe, and eco-conscious products that nourish while they enhance. Your hair, skin, and nails will thank you.', '🌿 Premium Products, Gentle Touch\r\nWe use only the best in beauty — premium, skin-safe, and eco-conscious products that nourish while they enhance. Your hair, skin, and nails will thank you.', 'afd8e663-998a-4d82-bfe2-2f8093de26a3.jpg', '2025-06-07 23:23:00', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'published', '2025-06-06 21:49:00', '2025-06-06 21:55:43');

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE `bookings` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `service_id` varchar(36) DEFAULT NULL,
  `package_id` varchar(36) DEFAULT NULL,
  `staff_id` varchar(36) DEFAULT NULL,
  `offer_id` varchar(36) DEFAULT NULL,
  `date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `status` enum('PENDING','CONFIRMED','IN_PROGRESS','COMPLETED','CANCELLED','NO_SHOW','EXPIRED') DEFAULT 'PENDING',
  `total_amount` int(11) NOT NULL,
  `points_used` int(11) DEFAULT 0,
  `points_earned` int(11) DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `bookings`
--

INSERT INTO `bookings` (`id`, `user_id`, `service_id`, `package_id`, `staff_id`, `offer_id`, `date`, `start_time`, `end_time`, `status`, `total_amount`, `points_used`, `points_earned`, `notes`, `created_at`, `updated_at`) VALUES
('0f8dabae-940a-45ac-962a-f0a44711506a', '4544f774-891d-4333-85c4-e78c998882cf', 'cf8fe43b-9e8e-4d0b-a5c9-bfed2c9f5816', NULL, '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, '2025-06-08', '14:21:00', '14:51:00', 'PENDING', 25, 0, 0, '', '2025-06-08 10:50:10', '2025-06-08 10:50:10');

-- --------------------------------------------------------

--
-- Table structure for table `booking_reminders`
--

CREATE TABLE `booking_reminders` (
  `id` varchar(36) NOT NULL,
  `booking_id` varchar(36) NOT NULL,
  `reminder_type` enum('24_HOURS','1_DAY','30_MINUTES','AT_TIME') NOT NULL,
  `priority` enum('LOW','MEDIUM','HIGH','URGENT') NOT NULL DEFAULT 'MEDIUM',
  `status` enum('PENDING','SENT','FAILED','SKIPPED') NOT NULL DEFAULT 'PENDING',
  `scheduled_time` datetime NOT NULL,
  `sent_time` datetime DEFAULT NULL,
  `attempts` int(11) DEFAULT 0,
  `max_attempts` int(11) DEFAULT 3,
  `customer_email_sent` tinyint(1) DEFAULT 0,
  `staff_email_sent` tinyint(1) DEFAULT 0,
  `customer_email_status` enum('PENDING','SENT','FAILED') DEFAULT 'PENDING',
  `staff_email_status` enum('PENDING','SENT','FAILED') DEFAULT 'PENDING',
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `booking_reminders`
--

INSERT INTO `booking_reminders` (`id`, `booking_id`, `reminder_type`, `priority`, `status`, `scheduled_time`, `sent_time`, `attempts`, `max_attempts`, `customer_email_sent`, `staff_email_sent`, `customer_email_status`, `staff_email_status`, `error_message`, `created_at`, `updated_at`) VALUES
('22dd7b86-e693-4e33-9197-89eeced42edb', '0f8dabae-940a-45ac-962a-f0a44711506a', 'AT_TIME', 'URGENT', 'SENT', '2025-06-08 14:21:00', '2025-06-08 14:16:09', 1, 2, 1, 1, 'SENT', 'SENT', NULL, '2025-06-08 10:50:10', '2025-06-08 11:16:09'),
('bbf83d8f-1ec4-415f-b2ef-7b15749f8921', '0f8dabae-940a-45ac-962a-f0a44711506a', '30_MINUTES', 'HIGH', 'SENT', '2025-06-08 13:51:00', '2025-06-08 13:50:36', 1, 3, 1, 1, 'SENT', 'SENT', NULL, '2025-06-08 10:50:10', '2025-06-08 10:50:36'),
('ce876528-4637-463f-b8e4-8d99d7a17a5c', '0f8dabae-940a-45ac-962a-f0a44711506a', '', 'HIGH', 'SENT', '2025-06-08 14:06:00', '2025-06-08 14:02:23', 1, 2, 1, 1, 'SENT', 'SENT', NULL, '2025-06-08 10:50:36', '2025-06-08 11:02:23');

-- --------------------------------------------------------

--
-- Table structure for table `booking_status_log`
--

CREATE TABLE `booking_status_log` (
  `id` varchar(36) NOT NULL,
  `booking_id` varchar(36) NOT NULL,
  `old_status` varchar(20) NOT NULL,
  `new_status` varchar(20) NOT NULL,
  `changed_by` varchar(36) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cms_content`
--

CREATE TABLE `cms_content` (
  `id` varchar(36) NOT NULL,
  `section` varchar(100) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `order_index` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cms_content`
--

INSERT INTO `cms_content` (`id`, `section`, `title`, `content`, `image`, `order_index`, `is_active`, `created_at`, `updated_at`) VALUES
('1614ac99-e2f0-4a1b-90d8-292b34ab11e9', 'hero', 'Welcome to Flix Salonce', 'Experience luxury and elegance at our premium beauty salon', NULL, 1, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('5cb3b7c9-110e-498d-9544-3229756b3f48', 'mission', 'Our Mission', 'To enhance natural beauty and boost confidence through professional beauty services', NULL, 1, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('a92aa99e-c89b-43d5-a1b5-5c3230200795', 'about', 'About Our Salon', 'We are dedicated to providing exceptional beauty services in a luxurious environment', NULL, 1, 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02');

-- --------------------------------------------------------

--
-- Table structure for table `contact_messages`
--

CREATE TABLE `contact_messages` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `status` enum('NEW','READ','REPLIED','ARCHIVED') DEFAULT 'NEW',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `contact_messages`
--

INSERT INTO `contact_messages` (`id`, `name`, `email`, `phone`, `subject`, `message`, `status`, `created_at`, `updated_at`) VALUES
('8ca776f5-168e-4955-b2c3-2c80edbe3d73', 'Craq Venture', '<EMAIL>', '(255) 765-348321', 'appointment', 'cdcd', 'READ', '2025-06-06 11:58:51', '2025-06-06 12:21:51'),
('d9f44fc8-1394-4b06-8913-583314cd3da5', 'Craq Venture', '<EMAIL>', '0787574355', 'appointment', 'cvdv', 'NEW', '2025-06-06 21:41:45', '2025-06-06 21:41:45'),
('e5fc7b08-fe68-4a1b-9602-812c9fddafb2', 'Charles Changawa', '<EMAIL>', '078454889949', 'pricing', 'Pumguza', 'NEW', '2025-06-06 12:34:06', '2025-06-06 12:34:06');

-- --------------------------------------------------------

--
-- Table structure for table `customer_messages`
--

CREATE TABLE `customer_messages` (
  `id` varchar(36) NOT NULL,
  `customer_id` varchar(36) NOT NULL,
  `admin_id` varchar(36) NOT NULL,
  `message_type` enum('email','sms','both') NOT NULL DEFAULT 'email',
  `subject` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `status` enum('SENT','DELIVERED','FAILED','PENDING') NOT NULL DEFAULT 'PENDING',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `customer_messages`
--

INSERT INTO `customer_messages` (`id`, `customer_id`, `admin_id`, `message_type`, `subject`, `message`, `status`, `created_at`, `updated_at`) VALUES
('84cfd37a-b2f9-479e-b44e-a8eb6cd17f63', '4544f774-891d-4333-85c4-e78c998882cf', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'email', 'Test Message from Flix Salon & SPA', 'Hello chare!\n\nThis is a test message to verify our new email system is working correctly.\n\nYour current loyalty points: 0\n\nThank you for being a valued customer at Flix Salonce!\n\nBest regards,\nThe Flix Team', 'SENT', '2025-06-08 00:13:20', '2025-06-08 00:13:25'),
('b5e93ce9-df1e-4757-a751-82c4d40f2e05', '4544f774-891d-4333-85c4-e78c998882cf', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'email', 'Happy Birthday from Flix Salonce!', 'Happy Birthday chare! 🎉\r\n\r\nWishing you a wonderful day filled with joy and beauty.\r\n\r\nAs a birthday gift, enjoy [special offer] on your next visit!\r\n\r\nCelebrate with us at Flix Salonce!\r\n\r\nBest wishes,\r\nFlix Salonce Team', 'SENT', '2025-06-08 00:20:52', '2025-06-08 00:20:57'),
('d050b641-454c-48f4-9741-e718e7ee68d8', '4544f774-891d-4333-85c4-e78c998882cf', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'email', 'Test Message from Flix Salon & SPA', 'Hello chare!\n\nThis is a test message to verify our new email system is working correctly.\n\nYour current loyalty points: 0\n\nThank you for being a valued customer at Flix Salonce!\n\nBest regards,\nThe Flix Team', 'SENT', '2025-06-08 08:43:31', '2025-06-08 08:43:36'),
('d397a13a-88ec-4aa5-8fb2-290ffcc4a6a4', '4544f774-891d-4333-85c4-e78c998882cf', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'email', 'Appointment Reminder - Flix Salonce', 'Hi chare,\r\n\r\nThis is a friendly reminder about your upcoming appointment at Flix Salonce.\r\n\r\nWe look forward to seeing you!\r\n\r\nBest regards,\r\nFlix Salonce Team', 'SENT', '2025-06-08 00:18:43', '2025-06-08 00:18:47');

-- --------------------------------------------------------

--
-- Table structure for table `email_logs`
--

CREATE TABLE `email_logs` (
  `id` int(11) NOT NULL,
  `recipient` varchar(255) NOT NULL,
  `subject` varchar(500) NOT NULL,
  `status` enum('SENT','FAILED','PENDING') DEFAULT 'PENDING',
  `details` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `email_logs`
--

INSERT INTO `email_logs` (`id`, `recipient`, `subject`, `status`, `details`, `created_at`) VALUES
(1, '<EMAIL>', 'Email Configuration Test - Flix Salon & SPA', 'FAILED', '', '2025-06-07 22:02:27'),
(2, '<EMAIL>', 'Welcome to Flix Salon & SPA!', 'FAILED', '', '2025-06-07 22:07:21'),
(3, '<EMAIL>', 'Welcome to Flix Salon & SPA!', 'SENT', '', '2025-06-07 22:22:20'),
(4, '<EMAIL>', 'Booking Confirmation - Facial Treatment', 'SENT', '', '2025-06-07 22:29:57'),
(5, '<EMAIL>', 'Booking Confirmation - Facial Treatment', 'SENT', '', '2025-06-07 22:37:32'),
(6, '<EMAIL>', 'Booking Confirmed - Flix Salon & SPA', 'SENT', '', '2025-06-07 22:37:36'),
(7, '<EMAIL>', 'Booking Confirmed - Flix Salon & SPA', 'SENT', '', '2025-06-07 22:37:41'),
(8, '<EMAIL>', 'Booking Cancellation - Facial Treatment', 'SENT', '', '2025-06-07 22:41:04'),
(9, '<EMAIL>', 'Booking Cancelled - Flix Salon & SPA', 'SENT', '', '2025-06-07 22:41:08'),
(10, '<EMAIL>', 'Booking Cancelled - Flix Salon & SPA', 'SENT', '', '2025-06-07 22:41:13'),
(11, '<EMAIL>', 'Appointment Reminder - Facial Treatment', 'SENT', '', '2025-06-07 23:29:24'),
(12, '<EMAIL>', 'Booking Confirmation - Gel Manicure', 'SENT', '', '2025-06-07 23:35:05'),
(15, '<EMAIL>', 'Test Message from Flix Salon & SPA', 'SENT', '', '2025-06-08 00:13:25'),
(16, '<EMAIL>', 'Direct SMTP Test - Flix Salon & SPA', 'SENT', '', '2025-06-08 00:13:30'),
(17, '<EMAIL>', 'Appointment Reminder - Flix Salonce', 'SENT', '', '2025-06-08 00:18:47'),
(18, '<EMAIL>', 'Happy Birthday from Flix Salonce!', 'SENT', '', '2025-06-08 00:20:57'),
(19, '<EMAIL>', 'Appointment Reminder - Gel Manicure', 'SENT', '', '2025-06-08 07:56:51'),
(20, '<EMAIL>', 'Appointment Reminder - Gel Manicure', 'SENT', '', '2025-06-08 07:56:52'),
(21, '<EMAIL>', 'Upcoming Appointment - Flix Salon & SPA', 'SENT', '', '2025-06-08 07:56:57'),
(22, '<EMAIL>', 'Upcoming Appointment - Flix Salon & SPA', 'SENT', '', '2025-06-08 07:56:59'),
(23, '<EMAIL>', 'Appointment Reminder - Gel Manicure', 'SENT', '', '2025-06-08 07:57:01'),
(24, '<EMAIL>', 'Appointment Reminder - Gel Manicure', 'SENT', '', '2025-06-08 07:57:03'),
(25, '<EMAIL>', 'Upcoming Appointment - Flix Salon & SPA', 'SENT', '', '2025-06-08 07:57:06'),
(26, '<EMAIL>', 'Upcoming Appointment - Flix Salon & SPA', 'SENT', '', '2025-06-08 07:57:11'),
(27, '<EMAIL>', 'Appointment Reminder - Gel Manicure', 'SENT', '', '2025-06-08 07:57:11'),
(28, '<EMAIL>', 'Upcoming Appointment - Flix Salon & SPA', 'SENT', '', '2025-06-08 07:57:16'),
(29, '<EMAIL>', 'Booking Confirmation - Makeup Application', 'SENT', '', '2025-06-08 08:16:42'),
(30, '<EMAIL>', 'Booking Confirmed - Flix Salon & SPA', 'SENT', '', '2025-06-08 08:16:46'),
(31, '<EMAIL>', 'Booking Confirmed - Flix Salon & SPA', 'SENT', '', '2025-06-08 08:16:51'),
(32, '<EMAIL>', 'Test Message from Flix Salon & SPA', 'SENT', '', '2025-06-08 08:43:36'),
(33, '<EMAIL>', 'Direct SMTP Test - Flix Salon & SPA', 'SENT', '', '2025-06-08 08:43:40'),
(34, '<EMAIL>', 'Booking Confirmation - Craq Venture2', 'SENT', '', '2025-06-08 08:47:47'),
(35, '<EMAIL>', 'Booking Confirmed - Flix Salon & SPA', 'SENT', '', '2025-06-08 08:48:04'),
(36, '<EMAIL>', 'Booking Confirmed - Flix Salon & SPA', 'SENT', '', '2025-06-08 08:48:16'),
(37, '<EMAIL>', 'Appointment Reminder - Craq Venture2', 'SENT', '', '2025-06-08 08:48:51'),
(38, '<EMAIL>', 'Appointment Reminder - Craq Venture2', 'SENT', '', '2025-06-08 08:52:18'),
(39, '<EMAIL>', 'Upcoming Appointment - Flix Salon & SPA', 'SENT', '', '2025-06-08 08:52:24'),
(40, '<EMAIL>', 'Appointment Reminder - Craq Venture2', 'SENT', '', '2025-06-08 09:04:39'),
(41, '<EMAIL>', 'Upcoming Appointment - Flix Salon & SPA', 'SENT', '', '2025-06-08 09:04:45'),
(42, '<EMAIL>', 'Appointment Reminder - Craq Venture2', 'SENT', '', '2025-06-08 09:18:43'),
(43, '<EMAIL>', 'Upcoming Appointment - Flix Salon & SPA', 'SENT', '', '2025-06-08 09:18:47'),
(44, '<EMAIL>', 'Appointment Reminder - Eyebrow Shaping', 'SENT', '', '2025-06-08 10:50:28'),
(45, '<EMAIL>', 'Upcoming Appointment - Flix Salon & SPA', 'SENT', '', '2025-06-08 10:50:36'),
(46, '<EMAIL>', 'Appointment Reminder - Eyebrow Shaping', 'SENT', '', '2025-06-08 11:02:16'),
(47, '<EMAIL>', 'Upcoming Appointment - Flix Salon & SPA', 'SENT', '', '2025-06-08 11:02:23'),
(48, '<EMAIL>', 'Appointment Reminder - Eyebrow Shaping', 'SENT', '', '2025-06-08 11:16:05'),
(49, '<EMAIL>', 'Upcoming Appointment - Flix Salon & SPA', 'SENT', '', '2025-06-08 11:16:09'),
(50, '<EMAIL>', 'Password Reset Code - Flix Salon & SPA', 'SENT', '', '2025-06-08 15:47:26'),
(51, '<EMAIL>', 'Password Reset Code - Flix Salon & SPA', 'SENT', '', '2025-06-08 15:51:00');

-- --------------------------------------------------------

--
-- Table structure for table `gallery`
--

CREATE TABLE `gallery` (
  `id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image_url` varchar(255) NOT NULL,
  `category` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `gallery`
--

INSERT INTO `gallery` (`id`, `title`, `description`, `image_url`, `category`, `is_active`, `created_at`, `updated_at`) VALUES
('203ba46b-f6f9-40ae-82be-bc70d7af0d5f', 'Beauty Treatment', 'Luxury beauty service', 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=600', 'Beauty', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('3092b133-ccdc-43e9-8b47-197c866354b9', 'Facial Treatment', 'Relaxing facial session', 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=600', 'Facial', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('46b0e2ae-0076-4419-b6d4-3b2207a5ca9a', 'Makeup Application', 'Professional makeup', 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=600', 'Beauty', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('7245c1a5-f08e-4a83-a9b1-ffa3d2d25c07', 'Salon Interior', 'Modern salon space', 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=600', 'Interior', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('8d967d81-4c0c-4529-8ac5-9fd501bc6f23', 'Hair Coloring', 'Professional hair coloring', 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=600', 'Hair', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('b46b7efc-41e9-4f53-a2f3-b1924989b237', 'Nail Art Design', 'Creative nail art', 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=600', 'Nails', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('da84b87c-552e-4ba3-bc7f-574ff41fe3c1', 'Massage Therapy', 'Therapeutic massage session', 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=600', 'Massage', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('f4c968f3-40ae-45b2-a1df-0f78a85e1af9', 'Hair Styling Work', 'Beautiful hair transformation', 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=600', 'Hair', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02');

-- --------------------------------------------------------

--
-- Table structure for table `newsletter`
--

CREATE TABLE `newsletter` (
  `id` varchar(36) NOT NULL,
  `email` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `newsletter`
--

INSERT INTO `newsletter` (`id`, `email`, `is_active`, `created_at`) VALUES
('', '<EMAIL>', 1, '2025-06-01 01:51:19'),
('2bba5856-37f8-4c86-a3aa-fa1147359853', '<EMAIL>', 1, '2025-06-06 11:26:52'),
('5ba535e5-f905-4cf3-bf57-5fbc2afbbc19', '<EMAIL>', 1, '2025-06-06 11:29:23');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('BOOKING_NEW','BOOKING_CONFIRMED','BOOKING_CANCELLED','BOOKING_COMPLETED','BOOKING_REMINDER','BOOKING_EXPIRED','BOOKING_NO_SHOW','BOOKING_REMINDER_24H','BOOKING_REMINDER_5H','BOOKING_REMINDER_30M','BOOKING_REMINDER_NOW','CUSTOMER_NEW','CUSTOMER_BIRTHDAY','NEWSLETTER_SUBSCRIBER','PAYMENT_SUCCESS','PAYMENT_FAILED','REFUND_PROCESSED','STAFF_NEW','STAFF_SCHEDULE_CHANGE','STAFF_LEAVE_REQUEST','SYSTEM_MAINTENANCE','SYSTEM_UPDATE','SYSTEM_BACKUP','PROMOTION_NEW','OFFER_EXPIRING','LOYALTY_MILESTONE','REVIEW_NEW','COMPLAINT_NEW','GENERAL','ADMIN_STAFF_ASSIGNED','ADMIN_STAFF_UNASSIGNED','ADMIN_BOOKING_UPDATED','ADMIN_STATUS_CHANGED','ADMIN_BOOKING_RESCHEDULED','ADMIN_BOOKING_DETAILS_CHANGED') NOT NULL,
  `category` enum('BOOKING','CUSTOMER','STAFF','PAYMENT','SYSTEM','MARKETING','FEEDBACK') NOT NULL,
  `priority` enum('LOW','MEDIUM','HIGH','URGENT') DEFAULT 'MEDIUM',
  `is_read` tinyint(1) DEFAULT 0,
  `action_url` varchar(500) DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `category`, `priority`, `is_read`, `action_url`, `metadata`, `expires_at`, `created_at`, `updated_at`) VALUES
('07f1736f-d2a4-4526-8e0b-eb607be57895', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with chare for Makeup Application on 2025-06-08 at 12:01:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-08', '{\"booking_id\":\"2251e91d-c17b-41ed-87e8-2f2253e1c90c\",\"customer_name\":\"chare\",\"service_name\":\"Makeup Application\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-08\",\"booking_time\":\"12:01:00\"}', NULL, '2025-06-08 08:16:20', '2025-06-08 08:16:20'),
('21b21061-cc77-4eec-a376-c8b5790609cf', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with chare for Craq Venture2 on 2025-06-08 at 12:23:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-08', '{\"booking_id\":\"95fe59e1-7826-472c-8baa-540901a766bb\",\"customer_name\":\"chare\",\"service_name\":\"Craq Venture2\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-08\",\"booking_time\":\"12:23:00\"}', NULL, '2025-06-08 08:47:10', '2025-06-08 08:47:10'),
('49c9afa4-da7b-4877-a2c4-9bdd2f8d27a0', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with chare for Eyebrow Shaping on 2025-06-08 at 14:21:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-08', '{\"booking_id\":\"0f8dabae-940a-45ac-962a-f0a44711506a\",\"customer_name\":\"chare\",\"service_name\":\"Eyebrow Shaping\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-08\",\"booking_time\":\"14:21:00\"}', NULL, '2025-06-08 10:50:10', '2025-06-08 10:50:10'),
('a49b009f-7c2e-40b3-b2ff-e7ba8de0691f', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'New Booking Assignment', 'You have been assigned a new booking with chare for Gel Manicure on 2025-06-08 at 11:00:00', 'BOOKING_NEW', 'BOOKING', 'HIGH', 0, '/staff/appointments?date=2025-06-08', '{\"booking_id\":\"8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb\",\"customer_name\":\"chare\",\"service_name\":\"Gel Manicure\",\"staff_name\":\"chaz vet\",\"booking_date\":\"2025-06-08\",\"booking_time\":\"11:00:00\"}', NULL, '2025-06-07 23:34:41', '2025-06-07 23:34:41');

-- --------------------------------------------------------

--
-- Table structure for table `offers`
--

CREATE TABLE `offers` (
  `id` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `discount` int(11) NOT NULL,
  `code` varchar(50) NOT NULL,
  `valid_from` datetime NOT NULL,
  `valid_to` datetime NOT NULL,
  `max_usage` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `offers`
--

INSERT INTO `offers` (`id`, `title`, `description`, `discount`, `code`, `valid_from`, `valid_to`, `max_usage`, `is_active`, `image`, `created_at`, `updated_at`) VALUES
('b93eead3-2c1b-46cc-bc52-4003ea3b8ab7', 'New Customer Special', 'Get 20% off your first visits', 5, 'WELCOME20', '2025-06-06 00:00:00', '2025-09-01 00:00:00', 100, 1, 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400', '2025-06-01 00:06:02', '2025-06-06 13:06:41'),
('c259412c-36cf-4138-ad4f-746eb31add9b', 'Referral Bonus', 'Refer a friend and both get 15% off', 15, 'REFER15', '2025-06-01 03:06:02', '2025-12-01 03:06:02', NULL, 1, 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400', '2025-06-01 00:06:02', '2025-06-05 22:32:28'),
('cf2926c4-21f0-4bd3-8ad4-93fa338810e3', 'Summer Glow Package', 'Special summer facial and hair treatment combo', 25, 'SUMMER25', '2025-06-01 03:06:02', '2025-08-01 03:06:02', 50, 1, 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400', '2025-06-01 00:06:02', '2025-06-01 00:06:02');

-- --------------------------------------------------------

--
-- Table structure for table `packages`
--

CREATE TABLE `packages` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` int(11) NOT NULL,
  `discount` int(11) DEFAULT 0,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `packages`
--

INSERT INTO `packages` (`id`, `name`, `description`, `price`, `discount`, `image`, `is_active`, `created_at`, `updated_at`) VALUES
('97a321b1-9598-4831-b4eb-ea1f92a70903', 'Test Package with Image', 'Test package for debugging image functionality', 500000, 0, 'https://img.freepik.com/free-photo/make-up-artist-getting-model-ready-photoshootin_23-2149305147.jpg', 1, '2025-06-01 16:18:06', '2025-06-05 21:00:12'),
('a0f1c3f8-f561-4aa8-af25-59d8ac475cce', 'Hair Transformation', 'Complete hair makeover with cut, color, and styling', 160000, 10, 'https://img.freepik.com/free-photo/make-up-artist-getting-model-ready-photoshootin_23-2149305132.jpg', 1, '2025-06-01 00:06:02', '2025-06-05 23:14:05'),
('b7306f8c-e63e-4fa2-a761-2b47a94e9034', 'chaz', 'dcdsc', 200000, 0, 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?fm=jpg&amp;amp;amp;q=60&amp;amp;amp;w=3000&amp;amp;amp;ixlib=rb-4.1.0&amp;amp;amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D', 1, '2025-06-01 15:56:06', '2025-06-05 21:00:31');

-- --------------------------------------------------------

--
-- Table structure for table `package_services`
--

CREATE TABLE `package_services` (
  `id` varchar(36) NOT NULL,
  `package_id` varchar(36) NOT NULL,
  `service_id` varchar(36) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `package_services`
--

INSERT INTO `package_services` (`id`, `package_id`, `service_id`, `created_at`) VALUES
('2fa862c6-a398-49d7-ba13-594cd199e597', 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', '386ff31e-0274-443f-8807-53202fa0b7fe', '2025-06-05 23:14:05'),
('54a7f459-5c9b-4d1e-8632-d1ec84eee84c', '97a321b1-9598-4831-b4eb-ea1f92a70903', '386ff31e-0274-443f-8807-53202fa0b7fe', '2025-06-05 21:00:12'),
('69839d8f-7c5f-477c-b159-1444720b8d58', 'b7306f8c-e63e-4fa2-a761-2b47a94e9034', '7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', '2025-06-05 21:00:31'),
('730aba05-8d85-4f7a-ac32-697fdde3c1f1', '97a321b1-9598-4831-b4eb-ea1f92a70903', '3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', '2025-06-05 21:00:12'),
('851f99de-2496-47fa-92fd-2312dbda3dc5', 'b7306f8c-e63e-4fa2-a761-2b47a94e9034', '389f948c-141b-4fa3-9ec1-c1887d9ed745', '2025-06-05 21:00:31'),
('9b391928-4d7c-4d19-97e1-d4e3c78b75f7', 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', 'cf8fe43b-9e8e-4d0b-a5c9-bfed2c9f5816', '2025-06-05 23:14:05'),
('aa8673ed-251e-46b8-9389-5df3d4e109b2', 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', '7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', '2025-06-05 23:14:05'),
('f3eed933-133d-4f92-99e5-c75261c7ad60', 'b7306f8c-e63e-4fa2-a761-2b47a94e9034', 'cf8fe43b-9e8e-4d0b-a5c9-bfed2c9f5816', '2025-06-05 21:00:31');

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `id` varchar(36) NOT NULL,
  `booking_id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `amount` int(11) NOT NULL,
  `currency` varchar(3) DEFAULT 'USD',
  `status` enum('PENDING','COMPLETED','FAILED','REFUNDED') DEFAULT 'PENDING',
  `payment_method` varchar(50) DEFAULT 'card',
  `stripe_payment_id` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `point_transactions`
--

CREATE TABLE `point_transactions` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `points` int(11) NOT NULL,
  `type` enum('EARNED','REDEMPTION','BONUS','REFUND') NOT NULL,
  `description` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `booking_id` varchar(36) DEFAULT NULL,
  `reward_id` varchar(36) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `point_transactions`
--

INSERT INTO `point_transactions` (`id`, `user_id`, `points`, `type`, `description`, `created_at`, `booking_id`, `reward_id`) VALUES
('89c81480-f2bb-4573-9e69-6f05fb5ac367', '1ec02ea0-8b70-44b0-bf07-c59a279dae8d', -950, '', 'Points removed due to booking expiration', '2025-06-07 22:04:53', 'd0572cef-f40a-44f4-b75a-70a5b6899e78', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `reminder_logs`
--

CREATE TABLE `reminder_logs` (
  `id` int(11) NOT NULL,
  `reminder_id` varchar(36) NOT NULL,
  `booking_id` varchar(36) NOT NULL,
  `action` enum('CREATED','SENT','FAILED','RETRY','SKIPPED') NOT NULL,
  `recipient_type` enum('CUSTOMER','STAFF','BOTH') NOT NULL,
  `recipient_email` varchar(255) DEFAULT NULL,
  `details` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `reminder_logs`
--

INSERT INTO `reminder_logs` (`id`, `reminder_id`, `booking_id`, `action`, `recipient_type`, `recipient_email`, `details`, `created_at`) VALUES
(1, '39a71393-fb85-4351-9074-d94e51aa9751', 'a60757d5-a9be-41c6-b78c-9ea28ea5b0ed', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-10 20:34:00', '2025-06-07 22:55:35'),
(2, 'e239288c-d8f8-4c43-bdf7-266f62ad3718', 'a60757d5-a9be-41c6-b78c-9ea28ea5b0ed', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-11 20:04:00', '2025-06-07 22:55:35'),
(3, '8a8a3bb9-3c1b-4389-92cc-780940aaafed', 'a60757d5-a9be-41c6-b78c-9ea28ea5b0ed', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-11 20:34:00', '2025-06-07 22:55:35'),
(4, 'f8c27e02-cbaa-4464-8673-9c1e6557db59', '501ee60b-a6de-422c-90a9-23265d4e0b35', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-11 22:00:00', '2025-06-07 22:55:35'),
(5, 'c3128994-d60c-44c4-b9f0-339ea4fb144e', '501ee60b-a6de-422c-90a9-23265d4e0b35', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-12 21:30:00', '2025-06-07 22:55:35'),
(6, '77702070-5b7d-4a91-a911-2ed2ad2a7968', '501ee60b-a6de-422c-90a9-23265d4e0b35', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-12 22:00:00', '2025-06-07 22:55:35'),
(7, '9af22e41-d9b5-4bd1-875d-6ac28bd4d3b6', '2f04c209-766c-4ec0-9184-d01091f8afd2', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-19 20:54:00', '2025-06-07 22:55:35'),
(8, 'be172c5e-c1de-4206-b797-ea1711d2007d', '2f04c209-766c-4ec0-9184-d01091f8afd2', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-20 20:24:00', '2025-06-07 22:55:35'),
(9, '54337cde-774b-438e-af04-a50d2a890e4f', '2f04c209-766c-4ec0-9184-d01091f8afd2', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-20 20:54:00', '2025-06-07 22:55:35'),
(10, '72d1776a-1949-4816-84e8-a4adc72c1acf', '501ee60b-a6de-422c-90a9-23265d4e0b35', 'CREATED', 'BOTH', '', 'Reminder scheduled for 24_HOURS at 2025-06-11 22:00:00', '2025-06-07 23:07:52'),
(11, '95b7dd1c-01f5-4625-8451-ee6b475a490a', '501ee60b-a6de-422c-90a9-23265d4e0b35', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-12 21:30:00', '2025-06-07 23:07:52'),
(12, 'b58c3455-b98c-4d5f-b4ae-894708619a7b', '501ee60b-a6de-422c-90a9-23265d4e0b35', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-12 22:00:00', '2025-06-07 23:07:52'),
(13, 'fc03d17e-de45-45fe-9acd-2a03a8928ce3', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 11:40:00', '2025-06-07 23:08:24'),
(14, '6322e5fa-1fbe-405f-83ba-88a680967e3f', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 12:10:00', '2025-06-07 23:08:24'),
(15, '0286f791-3fe5-41b7-9f25-9e83bda1ff50', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:36'),
(16, '0c717671-e419-4a3a-b78a-8169e590efa2', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:36'),
(17, '473ae346-3957-4006-91e9-ec888eb3724d', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:38'),
(18, 'cb755348-e413-4485-9384-9f94429c789f', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:38'),
(19, 'ae760e92-a006-451c-b944-70012e9cf3e2', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:38'),
(20, '8591126e-3b7d-4efa-938a-0895b9ffac34', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:38'),
(21, '23d74828-293b-4471-85c8-1350ba29641f', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:38'),
(22, '3aab5667-92c2-40fd-a3da-74e98d8eecc0', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:38'),
(23, '0b1cea99-38aa-45eb-9012-0e3071d9e71f', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:39'),
(24, '6e53e3ad-af1e-416f-a412-ebe1cc9856b3', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:39'),
(25, '8c377d80-d23d-42fc-941e-db70edac989b', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:39'),
(26, 'ddbd63bf-9e70-443e-9604-bb01455abd4b', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:39'),
(27, '8c5cabd5-8cbe-46bf-aaa5-ecadcf612274', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:39'),
(28, '31e4dcb9-e027-4e55-a12a-db244973ad63', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:39'),
(29, 'fae809fb-c0b1-44eb-a5c1-9d38100b8e05', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:39'),
(30, 'd991ad63-7ed0-474b-8296-2cb7eda7ca8f', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:39'),
(31, '3765bfdd-145d-443c-a2fb-6eebeeb450cc', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:39'),
(32, '32b74f97-4f30-4238-bd65-51d1324ef0de', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:39'),
(33, '7c3459a9-c96c-4cd6-8711-ae62ceceac64', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:40'),
(34, '96333d63-179a-4a55-b8df-00d11a3d2b7c', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:40'),
(35, 'f30a21b6-f139-4025-8b13-6eba1283097b', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:40'),
(36, 'aec056db-1068-4cee-8d6e-cc4591853da1', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:40'),
(37, '347b6e85-3a01-4e97-9567-fb215f0727ea', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:45'),
(38, '513e6145-8050-4aac-84ef-f7b4a738c03c', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:45'),
(39, 'ec158994-392e-406b-b0cc-b72c53f995c4', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:45'),
(40, '9a497434-d7fe-42dc-bf08-c5fb48e8e980', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:45'),
(41, 'b707de85-8903-4dcc-b39a-585ec9c5e4fe', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:45'),
(42, 'd8d708e9-ca72-4226-9fe5-1616ca94c6ad', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:45'),
(43, 'be787aa1-4644-4ecf-b343-3ef73d16cf7a', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:11:49'),
(44, 'e165b7b6-ac22-43d9-a2ed-e2ce38e39de2', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:11:49'),
(45, 'c3251b5e-23f1-4ecc-b1b7-18f675ee7a35', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:12:01'),
(46, 'b213720b-ad3d-4329-97d5-a1ce862f7346', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:12:01'),
(47, '952894ef-dba4-4cc9-bf57-04b2a089686c', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:12:02'),
(48, '68634687-ff85-45fc-8df9-16711700a00c', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:12:02'),
(49, '04adc599-b650-4f65-a582-8e45b8f2aa8f', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 02:15:00', '2025-06-07 23:12:05'),
(50, '256e4306-22f5-43ce-8a60-26d6d2a7b722', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:12:05'),
(51, '8a1b6eae-99ff-4174-9584-15182db4d177', 'c278e06b-a4aa-4e6f-ab5c-1df48138c90b', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 02:45:00', '2025-06-07 23:26:47'),
(52, '775b7935-ea52-4c2c-bf90-4243d941026b', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 10:30:00', '2025-06-07 23:35:13'),
(53, 'e74674cb-0704-44a7-87ed-dd661b523bce', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 11:00:00', '2025-06-07 23:35:13'),
(54, 'e74674cb-0704-44a7-87ed-dd661b523bce', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 07:56:57'),
(55, 'e74674cb-0704-44a7-87ed-dd661b523bce', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 07:56:59'),
(56, '4495954d-2a5b-41b3-b49f-f9a82e2ce6d6', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'CREATED', 'BOTH', '', 'Additional high priority reminder scheduled', '2025-06-08 07:57:06'),
(57, '775b7935-ea52-4c2c-bf90-4243d941026b', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 07:57:06'),
(58, '775b7935-ea52-4c2c-bf90-4243d941026b', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 07:57:11'),
(59, '4495954d-2a5b-41b3-b49f-f9a82e2ce6d6', '8bfbc9b8-8c59-44f0-bbc6-4c74c541a9fb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 07:57:16'),
(60, '7808a2e4-0e51-42d6-a2eb-e1b22b18ab97', '2251e91d-c17b-41ed-87e8-2f2253e1c90c', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 11:31:00', '2025-06-08 08:16:20'),
(61, '263ecf68-9de0-493c-8e0c-31ac1073b27e', '2251e91d-c17b-41ed-87e8-2f2253e1c90c', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 12:01:00', '2025-06-08 08:16:20'),
(62, '8c4e8e7a-e625-4e9f-b506-602e289c5994', '2251e91d-c17b-41ed-87e8-2f2253e1c90c', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 11:31:00', '2025-06-08 08:16:51'),
(63, '99f0ded2-b3a6-4be4-a220-6c9113ff7fd8', '2251e91d-c17b-41ed-87e8-2f2253e1c90c', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 12:01:00', '2025-06-08 08:16:51'),
(64, '7808a2e4-0e51-42d6-a2eb-e1b22b18ab97', '2251e91d-c17b-41ed-87e8-2f2253e1c90c', 'RETRY', 'BOTH', '', 'Retry scheduled for failed reminder', '2025-06-08 08:26:21'),
(65, '7808a2e4-0e51-42d6-a2eb-e1b22b18ab97', '2251e91d-c17b-41ed-87e8-2f2253e1c90c', 'RETRY', 'BOTH', '', 'Retry scheduled for failed reminder', '2025-06-08 08:26:54'),
(66, '4c4516ee-bf12-47c8-ae2e-66831a07d0c8', '95fe59e1-7826-472c-8baa-540901a766bb', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 11:53:00', '2025-06-08 08:47:10'),
(67, 'a79fb8b1-d37b-469a-98bd-5a73f93b3b87', '95fe59e1-7826-472c-8baa-540901a766bb', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 12:23:00', '2025-06-08 08:47:10'),
(68, '66391444-2cc2-4288-b05d-d77b144bacfd', '95fe59e1-7826-472c-8baa-540901a766bb', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 11:53:00', '2025-06-08 08:48:16'),
(69, 'ff6ea595-9c3d-4c41-86e0-1c1a5a094a3b', '95fe59e1-7826-472c-8baa-540901a766bb', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 12:23:00', '2025-06-08 08:48:16'),
(70, 'ccc0e3f2-eb83-411a-94fd-2daed41c521e', '95fe59e1-7826-472c-8baa-540901a766bb', 'CREATED', 'BOTH', '', 'Additional high priority reminder scheduled', '2025-06-08 08:52:24'),
(71, '4c4516ee-bf12-47c8-ae2e-66831a07d0c8', '95fe59e1-7826-472c-8baa-540901a766bb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 08:52:24'),
(72, 'ccc0e3f2-eb83-411a-94fd-2daed41c521e', '95fe59e1-7826-472c-8baa-540901a766bb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 09:04:45'),
(73, 'a79fb8b1-d37b-469a-98bd-5a73f93b3b87', '95fe59e1-7826-472c-8baa-540901a766bb', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 09:18:47'),
(74, 'bbf83d8f-1ec4-415f-b2ef-7b15749f8921', '0f8dabae-940a-45ac-962a-f0a44711506a', 'CREATED', 'BOTH', '', 'Reminder scheduled for 30_MINUTES at 2025-06-08 13:51:00', '2025-06-08 10:50:10'),
(75, '22dd7b86-e693-4e33-9197-89eeced42edb', '0f8dabae-940a-45ac-962a-f0a44711506a', 'CREATED', 'BOTH', '', 'Reminder scheduled for AT_TIME at 2025-06-08 14:21:00', '2025-06-08 10:50:10'),
(76, 'ce876528-4637-463f-b8e4-8d99d7a17a5c', '0f8dabae-940a-45ac-962a-f0a44711506a', 'CREATED', 'BOTH', '', 'Additional high priority reminder scheduled', '2025-06-08 10:50:36'),
(77, 'bbf83d8f-1ec4-415f-b2ef-7b15749f8921', '0f8dabae-940a-45ac-962a-f0a44711506a', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 10:50:36'),
(78, 'ce876528-4637-463f-b8e4-8d99d7a17a5c', '0f8dabae-940a-45ac-962a-f0a44711506a', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 11:02:23'),
(79, '22dd7b86-e693-4e33-9197-89eeced42edb', '0f8dabae-940a-45ac-962a-f0a44711506a', 'SENT', 'BOTH', '', 'Reminder sent successfully', '2025-06-08 11:16:09');

-- --------------------------------------------------------

--
-- Table structure for table `reminder_rate_limit`
--

CREATE TABLE `reminder_rate_limit` (
  `booking_id` varchar(36) NOT NULL,
  `reminder_type` varchar(20) NOT NULL,
  `last_sent` timestamp NOT NULL DEFAULT current_timestamp(),
  `send_count` int(11) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reviews`
--

CREATE TABLE `reviews` (
  `id` varchar(36) NOT NULL,
  `customer_id` varchar(36) NOT NULL,
  `service_id` varchar(36) DEFAULT NULL,
  `package_id` varchar(36) DEFAULT NULL,
  `booking_id` varchar(36) DEFAULT NULL,
  `rating` int(11) NOT NULL CHECK (`rating` >= 1 and `rating` <= 5),
  `title` varchar(255) NOT NULL,
  `comment` text NOT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `is_featured` tinyint(1) DEFAULT 0,
  `status` enum('pending','verified','rejected') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `reviews`
--

INSERT INTO `reviews` (`id`, `customer_id`, `service_id`, `package_id`, `booking_id`, `rating`, `title`, `comment`, `is_verified`, `is_featured`, `status`, `created_at`, `updated_at`) VALUES
('16aef166-5cbf-42d7-8ee9-07b3e0a40a8b', '6085b727-0ac8-4d95-b10f-c6e766420e0c', NULL, '97a321b1-9598-4831-b4eb-ea1f92a70903', NULL, 5, 'Wonderful Facial Treatment', 'My skin has never looked better! The facial treatment was customized to my skin type and the results are visible immediately. The staff explained each step of the process.', 1, 1, 'verified', '2025-05-09 01:37:53', '2025-06-07 02:46:00'),
('347ddf5d-0cad-45ec-a32d-16c9c2906fb1', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', NULL, 'b7306f8c-e63e-4fa2-a761-2b47a94e9034', NULL, 4, 'Great Hair Color Service', 'The hair colorist did an amazing job with my highlights. The color turned out exactly as I envisioned and the hair feels healthy. Professional and skilled team.', 1, 0, 'verified', '2025-05-07 01:37:53', '2025-06-07 02:45:45'),
('4300760d-4c9e-4fda-a472-58b8463d8b62', '1ec02ea0-8b70-44b0-bf07-c59a279dae8d', NULL, '97a321b1-9598-4831-b4eb-ea1f92a70903', NULL, 5, 'Absolutely Amazing Experience!', 'I had the most wonderful facial treatment here. The staff was incredibly professional and the atmosphere was so relaxing. My skin feels amazing and looks radiant. I will definitely be coming back!', 1, 1, 'verified', '2025-05-10 01:37:53', '2025-06-07 02:45:55'),
('51ad4902-b389-44a4-ab16-9b9cc88004bf', '1ec02ea0-8b70-44b0-bf07-c59a279dae8d', NULL, 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', NULL, 3, 'Good Service Overall', 'Had a pleasant experience overall. The service was good and the staff was friendly. The only minor issue was the wait time, but the quality made up for it.', 1, 0, 'verified', '2025-04-15 01:37:53', '2025-06-07 01:37:53'),
('61ed6ccc-deed-412c-925d-837a7c54b10b', '1ec02ea0-8b70-44b0-bf07-c59a279dae8d', NULL, 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', NULL, 5, 'Excellent Package Deal', 'Booked the spa package and it was worth every penny. Multiple treatments in one session and all were executed perfectly. Great value for money and exceptional service.', 1, 0, 'verified', '2025-04-24 01:37:53', '2025-06-07 02:45:47'),
('68db1b92-cbfb-4119-b891-72f28854b02d', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', '6000b747-3dae-4dcf-bb6f-e05e9fa0b10f', NULL, NULL, 5, 'Test', 'dvsd', 1, 0, 'verified', '2025-06-07 02:32:44', '2025-06-07 02:45:32'),
('8d60d527-84c9-4c7d-8360-e723a4b2811b', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', NULL, 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', NULL, 4, 'Relaxing Massage Therapy', 'The massage was incredibly relaxing and therapeutic. The therapist was skilled and professional. The ambiance of the spa really helped me unwind after a stressful week.', 1, 1, 'verified', '2025-04-07 01:37:53', '2025-06-07 02:44:50'),
('ea1c8ae2-0fac-45f8-8cac-99790aa3f95b', '350e5a4d-da0d-4c42-ab08-302eb513c732', '7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', NULL, NULL, 4, 'Great Manicure Service', 'Love the attention to detail in their manicure service. The nail technician was very careful and the results lasted for weeks. Clean environment and friendly staff.', 1, 0, 'verified', '2025-04-28 01:37:53', '2025-06-07 01:37:53');

-- --------------------------------------------------------

--
-- Table structure for table `rewards`
--

CREATE TABLE `rewards` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `points_required` int(11) NOT NULL,
  `value` int(11) NOT NULL,
  `type` enum('discount','service','product','bonus') DEFAULT 'discount',
  `is_active` tinyint(1) DEFAULT 1,
  `max_usage` int(11) DEFAULT NULL,
  `usage_count` int(11) DEFAULT 0,
  `image` varchar(255) DEFAULT NULL,
  `terms_conditions` text DEFAULT NULL,
  `valid_from` date DEFAULT NULL,
  `valid_to` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `rewards`
--

INSERT INTO `rewards` (`id`, `name`, `description`, `points_required`, `value`, `type`, `is_active`, `max_usage`, `usage_count`, `image`, `terms_conditions`, `valid_from`, `valid_to`, `created_at`, `updated_at`) VALUES
('reward-1', 'TSH 5,000 Service Discount', 'Get TSH 5,000 off any service booking', 500, 5000, 'discount', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-2', 'TSH 10,000 Service Discount', 'Get TSH 10,000 off any service booking', 1000, 10000, 'discount', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-3', 'Free Basic Manicure', 'Complimentary basic manicure service', 1500, 25000, 'service', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-5', 'Free Hair Wash & Blow Dry', 'Complimentary hair wash and blow dry service', 800, 15000, 'service', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-6', 'VIP Treatment Upgrade', 'Upgrade any service to VIP treatment', 1200, 30000, 'bonus', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44'),
('reward-7', 'Birthday Special Package', 'Special birthday package with multiple services', 3000, 50000, 'service', 1, NULL, 0, NULL, NULL, NULL, NULL, '2025-06-02 15:29:44', '2025-06-02 15:29:44');

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `price` int(11) NOT NULL,
  `duration` int(11) NOT NULL COMMENT 'Duration in minutes',
  `category` varchar(100) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `services`
--

INSERT INTO `services` (`id`, `name`, `description`, `price`, `duration`, `category`, `image`, `is_active`, `created_at`, `updated_at`) VALUES
('3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', 'Pedicure new', 'Relaxing pedicure service', 450000, 60, 'Nails', 'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400', 1, '2025-06-01 00:06:02', '2025-06-05 22:07:02'),
('386ff31e-0274-443f-8807-53202fa0b7fe', 'Facial Treatment', 'Deep cleansing facial treatment', 65000, 75, 'Facial', 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400', 1, '2025-06-01 00:06:02', '2025-06-05 22:08:51'),
('389f948c-141b-4fa3-9ec1-c1887d9ed745', 'Craq Venture2', 'ca', 343000, 22, 'Hair', 'https://img.freepik.com/free-photo/make-up-artist-getting-model-ready-photoshootin_23-2149305116.jpg?t=st=1748796051~exp=1748799651~hmac=a3c787643f154275d3422dfa773b6f63174ba9741dac774fd9e45b5cf466084a', 1, '2025-06-01 14:46:57', '2025-06-05 22:01:34'),
('5ffce5a5-f93e-4fdd-bb66-7c54376f0fef', 'Hair Highlights', 'Professional highlighting service', 950000, 150, 'Hair', 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=400', 1, '2025-06-01 00:06:02', '2025-06-05 22:13:42'),
('6000b747-3dae-4dcf-bb6f-e05e9fa0b10f', 'Gel Manicure', 'Long-lasting gel manicure', 550000, 60, 'Nails', 'https://images.unsplash.com/photo-1610992015732-2449b76344bc?w=400', 1, '2025-06-01 00:06:02', '2025-06-05 22:09:36'),
('7ee5fc72-49d7-4ee9-9b71-1e18ebba274b', 'Deep Tissue Massage', 'Therapeutic deep tissue massage', 95, 75, 'Massage', 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('7f42c734-47e2-4e32-8608-eb5ea2fecbfa', 'Test Service', '', 50000, 60, '', NULL, 1, '2025-06-08 09:39:23', '2025-06-08 09:39:23'),
('958b58c2-c476-463b-b7f6-12b82fda1930', 'Makeup Application', 'Professional makeup service', 75, 60, 'Beauty', 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('9a169567-0b73-45a4-bc1f-5aa9677c6f73', 'Swedish Massage', 'Relaxing full body massage', 80, 60, 'Massage', 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('bde7fc78-ba62-4f92-9e2a-81214bb5a247', 'Hair Coloring', 'Full hair coloring service', 85, 120, 'Hair', 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('c001781b-954d-4ca6-a48a-66e20b6b1ed7', 'Hair Cut & Style', 'Professional haircut with styling', 45, 60, 'Hair', 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('cf8fe43b-9e8e-4d0b-a5c9-bfed2c9f5816', 'Eyebrow Shaping', 'Professional eyebrow shaping', 25, 30, 'Beauty', 'https://images.unsplash.com/photo-1588681664899-f142ff2dc9b1?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('d4d35cfc-2eb6-474c-9b67-6775aadad633', 'Manicure', 'Classic manicure service', 35, 45, 'Nails', 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('dc20dff0-0f57-4389-953f-69e348b40a74', 'Anti-Aging Facial', 'Advanced anti-aging facial', 95, 90, 'Facial', 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400', 1, '2025-06-01 00:06:02', '2025-06-01 00:06:02');

-- --------------------------------------------------------

--
-- Table structure for table `service_categories`
--

CREATE TABLE `service_categories` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `service_categories`
--

INSERT INTO `service_categories` (`id`, `name`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
('037c4a11-9a00-4c86-be46-2f15237f20b3', 'Beauty', 'Comprehensive beauty treatments and cosmetic services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('4a24a5b0-9682-4940-be09-e17034f6ddd2', 'Hair Removal', 'Professional hair removal services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('5335ca5a-8253-498c-9ab8-94eae3321c9e', 'Makeup', 'Professional makeup application and styling services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('69910667-eb76-46b6-8d51-d093fae52683', 'Eyebrows', 'Professional eyebrow shaping and styling services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('69b5a253-aa59-4f3c-8206-0889880e4544', 'Massage', 'Relaxing therapeutic massage and wellness services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('7dba5069-0c5c-451b-b081-61474a750e85', 'Lashes', 'Eyelash extensions and enhancement services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('8768082b-b036-4d2e-9e51-48385da7e4c2', 'Facial', 'Rejuvenating facial treatments and skincare services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('87e28781-979b-4259-94a0-cd2deeac8c0d', 'Hair', 'Professional hair cutting, styling, and treatment services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('bd15df65-bc22-4def-a42c-45367426bf75', 'Body Treatments', 'Full body treatments and wellness services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35'),
('e85a3548-eb2f-45d5-b3d9-113d39d7865b', 'Nails', 'Complete nail care including manicure and pedicure services', 1, '2025-06-03 08:34:35', '2025-06-03 08:34:35');

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(36) NOT NULL,
  `session_token` varchar(255) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `expires` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `session_token`, `user_id`, `expires`, `created_at`) VALUES
('15b0b0ba-e749-45e3-bd91-5aca56564843', '501814c56796d6a25b8c506751b3b97a107ee33c4df2d4531ce8c1af560b3994', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', '2025-06-21 03:34:12', '2025-06-07 00:34:12'),
('1ad90198-2632-40ae-ae5c-ef347349c9b9', '4b1d72b16fe1bf57a3cde311d915514ccdca12de48dc7e3711c9a0225f501a07', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2025-06-22 18:30:57', '2025-06-07 23:47:18'),
('3bead923-efcc-4a80-a540-0481673637a5', 'e5b6260bbc7cb9dd28c8930ab3787386973ad6eb13e4c279da9b5e9f78b21060', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', '2025-06-21 11:32:15', '2025-06-07 08:32:15'),
('43513b99-1d25-438f-a62d-4af0a9b4130e', 'fb2fcc555c8afd4a3462213c67b4385dd36a3bda4b0f2bc78394d8b3b351e61b', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', '2025-07-03 05:14:10', '2025-06-03 02:14:10'),
('52aa720b-b115-46c6-adec-4a22916b0ea7', '407620d9c9f0f565de4102f01fdb137b4f1c148946c6cbbacc3eedfb9f70bf4a', '4544f774-891d-4333-85c4-e78c998882cf', '2025-06-22 18:53:21', '2025-06-08 15:53:21'),
('5935e5ca-dc41-484f-bf9c-6dbc8482ea24', '928a4806c57310141ea8cb79e5981732221b35482f35a00919ade0c7a0ead6d7', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', '2025-06-17 11:03:51', '2025-06-03 08:03:51'),
('af33851a-2a90-4338-ae57-ab72bea1abe7', 'cf08c0262ebe4b804e038b56d6207331007d5acf2ede9310e5ca67ba9df21317', '350e5a4d-da0d-4c42-ab08-302eb513c732', '2025-07-03 03:31:10', '2025-06-03 00:31:10'),
('af762139-a450-4d62-96a9-6d9953a32eff', 'e426047cae0e467a9cd181b2a3c1c9ccff9f923219e47283b75295d49fc1f254', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2025-07-02 20:23:35', '2025-06-02 17:23:35'),
('c3ad8c61-2d27-4fed-973b-bc7578a80c77', '2833ce9bf5cc2c1f2f5f430d847de62e96217a05575a05db12275846af10ee27', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2025-07-03 05:00:17', '2025-06-03 02:00:17'),
('c3b4c13d-3d8a-411c-9f8a-3ebdb748e3c9', 'e79b3662368702373d003d79048fddd99b8f4175f4537ca76e72ca2de6939e34', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2025-07-03 02:04:10', '2025-06-02 23:04:10'),
('d7b87513-6aa7-4d6d-ad1d-7e320e5d1695', '6547fd06a9c46a2cb31e8e34cea2b739b152ad5fb53bc5a83db78f8bc7668294', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', '2025-07-02 19:33:14', '2025-06-02 16:33:14'),
('f0ec934a-1635-4dbc-b2df-a0dd5201d0c7', 'b651008520bca33caabcf8641056e59a6c08c1b5ff90a465bc9a59e28f2877c7', 'bd021aa2-59cd-4423-97c2-a4e7698460a3', '2025-07-03 10:43:44', '2025-06-03 07:43:44'),
('fbaac6b8-f403-4780-908a-2a6c8e6d28b3', '877974ac679b6b330edad1e162f399119a24e19067205339ebe48f42496a1cee', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', '2025-06-21 16:10:35', '2025-06-07 13:10:35');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` varchar(36) NOT NULL,
  `category` varchar(100) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` longtext NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `category`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
('', 'business', 'timezone', '{\"monday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"tuesday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"wednesday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"thursday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"friday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"saturday\":{\"open\":\"10:00\",\"close\":\"16:00\",\"closed\":false},\"sunday\":{\"open\":\"10:00\",\"close\":\"16:00\",\"closed\":true}}', '2025-06-04 01:56:29', '2025-06-04 01:59:05'),
('009b951d-fc8e-4bb6-a7ac-31a168420107', 'business', 'address', 'Upanga, Dar Es Salaam, Tanzania', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('1a50d335-dd0a-474b-bb01-4f2a02401720', 'business', 'email', '<EMAIL>', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('28d52ba6-e191-44f0-bf19-61a226700947', 'business', 'phone', '(255) 745 456-789', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('34789dcc-40e7-11f0-a274-84a93e04fdad', 'business', 'currency_code', 'TZS', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34789ee0-40e7-11f0-a274-84a93e04fdad', 'business', 'currency_symbol', 'TSH', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3478f8f9-40e7-11f0-a274-84a93e04fdad', 'booking', 'default_duration', '60', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34790560-40e7-11f0-a274-84a93e04fdad', 'booking', 'buffer_time', '15', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('347905bc-40e7-11f0-a274-84a93e04fdad', 'booking', 'advance_booking_days', '30', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('347905f3-40e7-11f0-a274-84a93e04fdad', 'booking', 'min_advance_hours', '2', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34790815-40e7-11f0-a274-84a93e04fdad', 'booking', 'cancellation_fee', '25', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34790859-40e7-11f0-a274-84a93e04fdad', 'booking', 'auto_expire_enabled', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34790889-40e7-11f0-a274-84a93e04fdad', 'booking', 'expire_after_hours', '2', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34794ccd-40e7-11f0-a274-84a93e04fdad', 'notification', 'email_booking_confirmation', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34795700-40e7-11f0-a274-84a93e04fdad', 'notification', 'email_booking_reminder', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34795766-40e7-11f0-a274-84a93e04fdad', 'notification', 'email_booking_cancellation', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479579b-40e7-11f0-a274-84a93e04fdad', 'notification', 'email_promotional', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('347957d7-40e7-11f0-a274-84a93e04fdad', 'notification', 'admin_new_booking', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34795808-40e7-11f0-a274-84a93e04fdad', 'notification', 'admin_booking_cancellation', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34795834-40e7-11f0-a274-84a93e04fdad', 'notification', 'admin_daily_summary', '0', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34795861-40e7-11f0-a274-84a93e04fdad', 'notification', 'reminder_24h', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479588d-40e7-11f0-a274-84a93e04fdad', 'notification', 'reminder_5h', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('347958bd-40e7-11f0-a274-84a93e04fdad', 'notification', 'reminder_30m', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('347958e9-40e7-11f0-a274-84a93e04fdad', 'notification', 'reminder_at_time', '0', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479894e-40e7-11f0-a274-84a93e04fdad', 'points', 'welcome_bonus', '100', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('347989c8-40e7-11f0-a274-84a93e04fdad', 'points', 'referral_bonus', '500', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34798a25-40e7-11f0-a274-84a93e04fdad', 'points', 'birthday_bonus', '200', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34798a77-40e7-11f0-a274-84a93e04fdad', 'points', 'review_bonus', '50', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34798c76-40e7-11f0-a274-84a93e04fdad', 'points', 'points_value', '10', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34798ce4-40e7-11f0-a274-84a93e04fdad', 'points', 'max_redemption_percent', '50', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('34798d47-40e7-11f0-a274-84a93e04fdad', 'points', 'points_expiry_days', '365', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479bf26-40e7-11f0-a274-84a93e04fdad', 'system', 'max_file_size', '10', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479c8f9-40e7-11f0-a274-84a93e04fdad', 'system', 'allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479c979-40e7-11f0-a274-84a93e04fdad', 'system', 'maintenance_mode', '0', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479c9d7-40e7-11f0-a274-84a93e04fdad', 'system', 'maintenance_message', 'We are currently performing maintenance. Please check back soon.', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479ca2e-40e7-11f0-a274-84a93e04fdad', 'system', 'session_timeout', '120', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479ca81-40e7-11f0-a274-84a93e04fdad', 'system', 'password_min_length', '8', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479cad6-40e7-11f0-a274-84a93e04fdad', 'system', 'login_attempts', '5', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479cb2b-40e7-11f0-a274-84a93e04fdad', 'system', 'lockout_duration', '15', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479cb75-40e7-11f0-a274-84a93e04fdad', 'system', 'cache_enabled', '1', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('3479cbc5-40e7-11f0-a274-84a93e04fdad', 'system', 'cache_duration', '24', '2025-06-04 01:57:01', '2025-06-04 01:57:01'),
('4f40db19-8f13-4a17-9464-d8553c026191', 'points', 'redeem_rate', '100', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('71b96c2e-5914-491a-b699-f214c80001e0', 'email', 'notifications', '1', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('8e8ef51d-57a6-4165-b3e7-459c7bc82de2', 'payment', 'stripe_enabled', '1', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('a1831bd1-e677-4189-b91c-1e7bc2f1b42b', 'business', 'hours', '{\"monday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"tuesday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"wednesday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"thursday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"friday\":{\"open\":\"09:00\",\"close\":\"17:00\",\"closed\":false},\"saturday\":{\"open\":\"10:00\",\"close\":\"16:00\",\"closed\":false},\"sunday\":{\"open\":\"10:00\",\"close\":\"16:00\",\"closed\":true}}', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('b34108c4-68e1-4e5a-b407-117217b54e24', 'booking', 'advance_days', '30', '2025-06-01 00:06:02', '2025-06-01 00:06:02'),
('c41ad510-e282-4790-8f15-ed58b84826f6', 'booking', 'cancellation_hours', '24', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('e415ff38-12a1-4734-bc17-f1f96d9bf964', 'business', 'name', 'Flix Salonce', '2025-06-01 00:06:02', '2025-06-04 01:57:01'),
('e698120e-1b3c-4a9c-a8fb-b05743345cd5', 'points', 'earn_rate', '1', '2025-06-01 00:06:02', '2025-06-04 01:57:01');

-- --------------------------------------------------------

--
-- Table structure for table `staff_schedules`
--

CREATE TABLE `staff_schedules` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `staff_id` varchar(36) DEFAULT NULL,
  `day_of_week` varchar(20) DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `is_working` tinyint(1) DEFAULT 1,
  `role` varchar(100) DEFAULT 'Staff Member',
  `hourly_rate` int(11) DEFAULT 50,
  `bio` text DEFAULT NULL,
  `experience` int(11) DEFAULT 0,
  `schedule` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`schedule`)),
  `specialties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`specialties`)),
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `staff_schedules`
--

INSERT INTO `staff_schedules` (`id`, `user_id`, `staff_id`, `day_of_week`, `start_time`, `end_time`, `is_working`, `role`, `hourly_rate`, `bio`, `experience`, `schedule`, `specialties`, `is_active`, `created_at`, `updated_at`) VALUES
('11f192a6-4858-47ab-96a3-841f6210d4fd', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'monday', '09:00:00', '17:00:00', 1, 'Staff Member', 50, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-07 13:43:44'),
('1a986767-868a-44f5-9e61-32c6ed2b8793', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'wednesday', '09:00:00', '17:00:00', 1, 'Staff Member', 50, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-07 13:43:44'),
('200ae44c-7b50-4ba5-b9b9-da6a5e3e768f', '8c42848c-6123-4438-9f23-8f5c07680371', NULL, NULL, NULL, NULL, 1, 'Staff Member', 50, NULL, 0, '{\"monday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"tuesday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"wednesday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"thursday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"friday\":{\"is_working\":true,\"available\":true,\"start_time\":\"09:00\",\"end_time\":\"17:00\",\"start\":\"09:00\",\"end\":\"17:00\"},\"saturday\":{\"is_working\":true,\"available\":true,\"start_time\":\"08:00\",\"end_time\":\"17:00\",\"start\":\"08:00\",\"end\":\"17:00\"},\"sunday\":{\"is_working\":false,\"available\":false,\"start_time\":\"09:00\",\"end_time\":\"18:00\",\"start\":\"09:00\",\"end\":\"18:00\"}}', NULL, 1, '2025-06-03 09:58:48', '2025-06-07 12:24:51'),
('4e311245-e81b-4d10-84b8-3e6425d54aa4', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'sunday', '07:00:00', '17:00:00', 1, 'Staff Member', 50, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-07 13:43:44'),
('63ddf464-2b52-4ecc-a096-1a57832109da', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, NULL, NULL, NULL, 1, 'Staff Member', 50, NULL, 0, '{\"monday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"tuesday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"wednesday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"thursday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"friday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"17:00:00\"},\"saturday\":{\"is_working\":true,\"start_time\":\"09:00:00\",\"end_time\":\"22:00:00\"},\"sunday\":{\"is_working\":true,\"start_time\":\"07:00:00\",\"end_time\":\"17:00:00\"}}', NULL, 1, '2025-06-03 09:58:48', '2025-06-07 13:43:44'),
('6a96db06-44bf-4725-8041-861460dd5c4b', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'thursday', '09:00:00', '17:00:00', 1, 'Staff Member', 50, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-07 13:43:44'),
('860d5002-d773-456d-95ad-ee1c5cfbc0f6', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'friday', '09:00:00', '17:00:00', 1, 'Staff Member', 50, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-07 13:43:44'),
('8acaa3bb-64e0-403e-9a27-82fa37b6de60', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'tuesday', '09:00:00', '17:00:00', 1, 'Staff Member', 50, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-07 13:43:44'),
('be9c065a-602d-4462-bdb7-591e42eb49e5', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', NULL, 'saturday', '09:00:00', '22:00:00', 1, 'Staff Member', 50, NULL, 0, NULL, NULL, 1, '2025-06-07 13:43:44', '2025-06-07 13:43:44');

-- --------------------------------------------------------

--
-- Table structure for table `staff_specialties`
--

CREATE TABLE `staff_specialties` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `service_id` varchar(36) NOT NULL,
  `proficiency_level` enum('BEGINNER','INTERMEDIATE','ADVANCED','EXPERT') DEFAULT 'INTERMEDIATE',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `staff_specialties`
--

INSERT INTO `staff_specialties` (`id`, `user_id`, `service_id`, `proficiency_level`, `created_at`, `updated_at`) VALUES
('005c9d77-861a-4379-90b6-22b1874ac57b', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', '389f948c-141b-4fa3-9ec1-c1887d9ed745', 'INTERMEDIATE', '2025-06-03 09:58:48', '2025-06-03 09:58:48'),
('80bbbe55-7b68-432a-9b1d-d2dbb3e84269', '8c42848c-6123-4438-9f23-8f5c07680371', '3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', 'INTERMEDIATE', '2025-06-07 12:09:15', '2025-06-07 12:09:15'),
('8143846d-0006-461c-9f0a-abdb946cea66', '8c42848c-6123-4438-9f23-8f5c07680371', '386ff31e-0274-443f-8807-53202fa0b7fe', 'INTERMEDIATE', '2025-06-07 12:09:15', '2025-06-07 12:09:15'),
('88d3486a-c8f3-4125-ae12-cad382436fe2', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', '3348ec93-5fb9-4aaf-a9de-ee1ee4ff4c27', 'INTERMEDIATE', '2025-06-03 09:58:48', '2025-06-03 09:58:48'),
('c5e216a2-f8f0-4fb5-9a8e-5189a256c4ca', '8c42848c-6123-4438-9f23-8f5c07680371', '389f948c-141b-4fa3-9ec1-c1887d9ed745', 'INTERMEDIATE', '2025-06-07 12:09:15', '2025-06-07 12:09:15'),
('fc09a828-358e-4c03-baf0-ee9c4cbdf670', '3d2c13a0-d867-452d-a8e6-87dc6f1433bf', '386ff31e-0274-443f-8807-53202fa0b7fe', 'INTERMEDIATE', '2025-06-03 09:58:48', '2025-06-03 09:58:48');

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `system_settings`
--

INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `updated_at`) VALUES
(1, 'points_migration_completed', '2025-06-02 19:19:03', '2025-06-02 16:19:03'),
(2, 'last_reminder_check', '2025-06-04 00:18:29', '2025-06-03 21:18:29'),
(9, 'last_expiration_check', '2025-06-08 01:04:53', '2025-06-07 22:04:53');

-- --------------------------------------------------------

--
-- Stand-in structure for view `upcoming_appointments`
-- (See below for the actual view)
--
CREATE TABLE `upcoming_appointments` (
`id` varchar(36)
,`user_id` varchar(36)
,`staff_id` varchar(36)
,`date` date
,`start_time` time
,`status` enum('PENDING','CONFIRMED','IN_PROGRESS','COMPLETED','CANCELLED','NO_SHOW','EXPIRED')
,`appointment_datetime` varchar(21)
,`minutes_until` bigint(21)
,`customer_name` varchar(255)
,`customer_email` varchar(255)
,`customer_phone` varchar(20)
,`service_name` varchar(255)
,`staff_name` varchar(255)
,`package_name` varchar(255)
);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `role` enum('CUSTOMER','ADMIN','STAFF') DEFAULT 'CUSTOMER',
  `points` int(11) DEFAULT 0,
  `referral_code` varchar(10) DEFAULT NULL,
  `referred_by` varchar(36) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` tinyint(1) DEFAULT 1,
  `reset_otp` varchar(6) DEFAULT NULL,
  `otp_expires_at` timestamp NULL DEFAULT NULL,
  `otp_attempts` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `image`, `password`, `phone`, `date_of_birth`, `role`, `points`, `referral_code`, `referred_by`, `created_at`, `updated_at`, `is_active`, `reset_otp`, `otp_expires_at`, `otp_attempts`) VALUES
('1ec02ea0-8b70-44b0-bf07-c59a279dae8d', 'Craq Venture', '<EMAIL>', NULL, NULL, '$2y$12$SI95UabiDCyFg6Jgkv0ji.Z9HmP/.v8j/X.S8qwv3LXWjL3i2qrGK', '0787574355', NULL, 'CUSTOMER', 0, 'CRA550', NULL, '2025-06-02 22:14:32', '2025-06-07 22:04:53', 1, NULL, NULL, 0),
('350e5a4d-da0d-4c42-ab08-302eb513c732', 'John Customer', '<EMAIL>', NULL, NULL, '$2y$10$QRoLGv9BUjKACGqU.jpDkOcAC8KzNfR.rkiA/o30KUfBb0L.4CZea', NULL, NULL, 'CUSTOMER', 6, 'JOH001', NULL, '2025-06-01 00:06:01', '2025-06-02 20:52:05', 1, NULL, NULL, 0),
('3d2c13a0-d867-452d-a8e6-87dc6f1433bf', 'chaz vet', '<EMAIL>', NULL, NULL, '$2y$10$N6hEG6AWi4C5mizpmWQ/6eMhGS0EYhAB9jozdvD64BRJvqoUozBt.', '+255622518815', NULL, 'STAFF', 0, NULL, NULL, '2025-06-01 19:12:51', '2025-06-01 22:07:40', 1, NULL, NULL, 0),
('4544f774-891d-4333-85c4-e78c998882cf', 'chare', '<EMAIL>', NULL, NULL, '$2y$12$AED5.P2vDgSn57QL/3f/jeTQM8OBNAbGlYX/La3wUPzpXFzloedJe', '', NULL, 'CUSTOMER', 0, 'CHA038', NULL, '2025-06-07 22:22:15', '2025-06-08 15:52:33', 1, NULL, NULL, 0),
('6085b727-0ac8-4d95-b10f-c6e766420e0c', 'Lisa Garcia', '<EMAIL>', NULL, NULL, '$2y$10$sl/.2ndIF2ema243FGTCH.vj2v4/0doORgF50QvcFIL8SuvT9FpqC', NULL, NULL, 'CUSTOMER', 0, 'LIS001', NULL, '2025-06-01 00:06:02', '2025-06-02 20:21:12', 1, NULL, NULL, 0),
('8c42848c-6123-4438-9f23-8f5c07680371', 'Staff Member', '<EMAIL>', NULL, NULL, '$2y$10$839BNTNhUot2ia5JWqFzeeYW6lWxJqg0/.IdolIJm/c563YEoXM7.', '', NULL, 'STAFF', 0, 'STF001', NULL, '2025-06-01 00:06:01', '2025-06-07 12:09:15', 1, NULL, NULL, 0),
('bd021aa2-59cd-4423-97c2-a4e7698460a3', 'Ailya Hassan', '<EMAIL>', NULL, 'https://img.freepik.com/free-psd/traveler-desert-hot-sun_23-2150177805.jpg?t=st=1748936874~exp=1748940474~hmac=6b09110ebc259ed8688b881bfd83c6b0b9ddca033205da520ce7fdb5713ef4fc&w=1480', '$2y$12$HiJkrbYbCQ30x3KmiP0.heOWHYaENSnn3hJbfZxRrsossBYClyhku', '0787574355', '2004-06-19', 'CUSTOMER', 0, 'MMB111', NULL, '2025-06-03 02:13:54', '2025-06-05 21:29:24', 1, NULL, NULL, 0),
('e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'Flix Admin', '<EMAIL>', NULL, NULL, '$2y$10$YATFTcMpx0zkawHaT1cD5e62A9q7j/pHgsEsgRAuD7YMw03TFjhFa', NULL, NULL, 'ADMIN', 0, 'ADM001', NULL, '2025-06-01 00:06:01', '2025-06-03 08:21:18', 1, NULL, NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `wishlists`
--

CREATE TABLE `wishlists` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `item_type` enum('service','package') NOT NULL,
  `item_id` varchar(36) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `wishlists`
--

INSERT INTO `wishlists` (`id`, `user_id`, `item_type`, `item_id`, `created_at`) VALUES
('3116d3d3-4034-47dd-8a00-9726f2f0b0a2', 'e0a2235d-ded8-4775-96b6-9e8a3cfe3346', 'package', 'a0f1c3f8-f561-4aa8-af25-59d8ac475cce', '2025-06-05 21:35:30');

-- --------------------------------------------------------

--
-- Structure for view `upcoming_appointments`
--
DROP TABLE IF EXISTS `upcoming_appointments`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `upcoming_appointments`  AS SELECT `b`.`id` AS `id`, `b`.`user_id` AS `user_id`, `b`.`staff_id` AS `staff_id`, `b`.`date` AS `date`, `b`.`start_time` AS `start_time`, `b`.`status` AS `status`, concat(`b`.`date`,' ',`b`.`start_time`) AS `appointment_datetime`, timestampdiff(MINUTE,current_timestamp(),concat(`b`.`date`,' ',`b`.`start_time`)) AS `minutes_until`, `u`.`name` AS `customer_name`, `u`.`email` AS `customer_email`, `u`.`phone` AS `customer_phone`, `s`.`name` AS `service_name`, `st`.`name` AS `staff_name`, `p`.`name` AS `package_name` FROM ((((`bookings` `b` left join `users` `u` on(`b`.`user_id` = `u`.`id`)) left join `services` `s` on(`b`.`service_id` = `s`.`id`)) left join `users` `st` on(`b`.`staff_id` = `st`.`id` and `st`.`role` = 'STAFF')) left join `packages` `p` on(`b`.`package_id` = `p`.`id`)) WHERE `b`.`status` in ('CONFIRMED','PENDING') AND concat(`b`.`date`,' ',`b`.`start_time`) > current_timestamp() ORDER BY concat(`b`.`date`,' ',`b`.`start_time`) ASC ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `blog`
--
ALTER TABLE `blog`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- Indexes for table `blog_posts`
--
ALTER TABLE `blog_posts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `author_id` (`author_id`),
  ADD KEY `idx_blog_posts_slug` (`slug`),
  ADD KEY `idx_blog_posts_status` (`status`),
  ADD KEY `idx_blog_posts_publish_date` (`publish_date`);

--
-- Indexes for table `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `service_id` (`service_id`),
  ADD KEY `package_id` (`package_id`),
  ADD KEY `offer_id` (`offer_id`),
  ADD KEY `idx_bookings_user_id` (`user_id`),
  ADD KEY `idx_bookings_date` (`date`),
  ADD KEY `idx_bookings_status` (`status`),
  ADD KEY `bookings_staff_fk` (`staff_id`),
  ADD KEY `idx_bookings_expiration` (`status`,`date`,`start_time`),
  ADD KEY `idx_bookings_status_date` (`status`,`date`),
  ADD KEY `idx_bookings_reminder_check` (`status`,`date`,`start_time`);

--
-- Indexes for table `booking_reminders`
--
ALTER TABLE `booking_reminders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_booking_reminder` (`booking_id`,`reminder_type`),
  ADD KEY `idx_booking_id` (`booking_id`),
  ADD KEY `idx_reminder_type` (`reminder_type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_priority` (`priority`),
  ADD KEY `idx_scheduled_time` (`scheduled_time`),
  ADD KEY `idx_pending_reminders` (`status`,`scheduled_time`),
  ADD KEY `idx_booking_reminder_type` (`booking_id`,`reminder_type`);

--
-- Indexes for table `booking_status_log`
--
ALTER TABLE `booking_status_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `booking_id` (`booking_id`),
  ADD KEY `changed_by` (`changed_by`);

--
-- Indexes for table `cms_content`
--
ALTER TABLE `cms_content`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `contact_messages`
--
ALTER TABLE `contact_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_subject` (`subject`);

--
-- Indexes for table `customer_messages`
--
ALTER TABLE `customer_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_customer_messages_customer` (`customer_id`),
  ADD KEY `idx_customer_messages_admin` (`admin_id`),
  ADD KEY `idx_customer_messages_status` (`status`),
  ADD KEY `idx_customer_messages_created` (`created_at`);

--
-- Indexes for table `email_logs`
--
ALTER TABLE `email_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_recipient` (`recipient`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `gallery`
--
ALTER TABLE `gallery`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `newsletter`
--
ALTER TABLE `newsletter`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_notifications_user_id` (`user_id`),
  ADD KEY `idx_notifications_read` (`is_read`),
  ADD KEY `idx_notifications_category` (`category`),
  ADD KEY `idx_notifications_type` (`type`),
  ADD KEY `idx_notifications_priority` (`priority`),
  ADD KEY `idx_notifications_created_at` (`created_at`),
  ADD KEY `idx_notifications_expires_at` (`expires_at`);

--
-- Indexes for table `offers`
--
ALTER TABLE `offers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `packages`
--
ALTER TABLE `packages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `package_services`
--
ALTER TABLE `package_services`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_package_service` (`package_id`,`service_id`),
  ADD KEY `service_id` (`service_id`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `booking_id` (`booking_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `point_transactions`
--
ALTER TABLE `point_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_reward_id` (`reward_id`);

--
-- Indexes for table `reminder_logs`
--
ALTER TABLE `reminder_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_reminder_id` (`reminder_id`),
  ADD KEY `idx_booking_id` (`booking_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `reminder_rate_limit`
--
ALTER TABLE `reminder_rate_limit`
  ADD PRIMARY KEY (`booking_id`,`reminder_type`),
  ADD KEY `idx_last_sent` (`last_sent`);

--
-- Indexes for table `reviews`
--
ALTER TABLE `reviews`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_customer_id` (`customer_id`),
  ADD KEY `idx_service_id` (`service_id`),
  ADD KEY `idx_package_id` (`package_id`),
  ADD KEY `idx_booking_id` (`booking_id`),
  ADD KEY `idx_rating` (`rating`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_is_verified` (`is_verified`),
  ADD KEY `idx_is_featured` (`is_featured`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `rewards`
--
ALTER TABLE `rewards`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_services_category` (`category`),
  ADD KEY `idx_services_active` (`is_active`);

--
-- Indexes for table `service_categories`
--
ALTER TABLE `service_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `session_token` (`session_token`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_setting` (`category`,`setting_key`);

--
-- Indexes for table `staff_schedules`
--
ALTER TABLE `staff_schedules`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `staff_specialties`
--
ALTER TABLE `staff_specialties`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_service` (`user_id`,`service_id`),
  ADD KEY `service_id` (`service_id`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `referral_code` (`referral_code`),
  ADD KEY `referred_by` (`referred_by`),
  ADD KEY `idx_users_email` (`email`),
  ADD KEY `idx_users_role` (`role`),
  ADD KEY `idx_users_reset_otp` (`reset_otp`),
  ADD KEY `idx_users_otp_expires` (`otp_expires_at`);

--
-- Indexes for table `wishlists`
--
ALTER TABLE `wishlists`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_wishlist_item` (`user_id`,`item_type`,`item_id`),
  ADD KEY `idx_user_wishlist` (`user_id`),
  ADD KEY `idx_item_type` (`item_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `email_logs`
--
ALTER TABLE `email_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=52;

--
-- AUTO_INCREMENT for table `reminder_logs`
--
ALTER TABLE `reminder_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=80;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `blog_posts`
--
ALTER TABLE `blog_posts`
  ADD CONSTRAINT `blog_posts_ibfk_1` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `bookings`
--
ALTER TABLE `bookings`
  ADD CONSTRAINT `bookings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `bookings_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bookings_ibfk_3` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bookings_ibfk_5` FOREIGN KEY (`offer_id`) REFERENCES `offers` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bookings_staff_fk` FOREIGN KEY (`staff_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `booking_status_log`
--
ALTER TABLE `booking_status_log`
  ADD CONSTRAINT `booking_status_log_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `booking_status_log_ibfk_2` FOREIGN KEY (`changed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `customer_messages`
--
ALTER TABLE `customer_messages`
  ADD CONSTRAINT `customer_messages_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_messages_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `package_services`
--
ALTER TABLE `package_services`
  ADD CONSTRAINT `package_services_ibfk_1` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `package_services_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `point_transactions`
--
ALTER TABLE `point_transactions`
  ADD CONSTRAINT `point_transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `sessions`
--
ALTER TABLE `sessions`
  ADD CONSTRAINT `sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `staff_schedules`
--
ALTER TABLE `staff_schedules`
  ADD CONSTRAINT `staff_schedules_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `staff_specialties`
--
ALTER TABLE `staff_specialties`
  ADD CONSTRAINT `staff_specialties_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `staff_specialties_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`referred_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `wishlists`
--
ALTER TABLE `wishlists`
  ADD CONSTRAINT `wishlists_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
