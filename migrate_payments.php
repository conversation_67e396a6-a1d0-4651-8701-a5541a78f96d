<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment System Migration</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Payment System Database Migration</h1>
    
    <?php
    require_once __DIR__ . '/config/app.php';
    
    if (isset($_POST['run_migration'])) {
        echo "<div class='step'>";
        echo "<h2>Migration Results</h2>";
        
        try {
            global $database;
            
            echo "<p class='info'>Starting database migration...</p>";
            
            // 1. Add missing columns to payments table
            echo "<h3>1. Updating payments table structure</h3>";
            
            $alterQueries = [
                "ALTER TABLE payments ADD COLUMN flutterwave_tx_ref VARCHAR(255) NULL",
                "ALTER TABLE payments ADD COLUMN flutterwave_tx_id VARCHAR(255) NULL", 
                "ALTER TABLE payments ADD COLUMN payment_gateway ENUM('STRIPE', 'FLUTTERWAVE') DEFAULT 'STRIPE'",
                "ALTER TABLE payments ADD COLUMN payment_reference VARCHAR(255) NULL",
                "ALTER TABLE payments ADD COLUMN payment_data JSON NULL",
                "ALTER TABLE payments ADD COLUMN webhook_verified BOOLEAN DEFAULT FALSE",
                "ALTER TABLE payments ADD COLUMN verification_attempts INT DEFAULT 0",
                "ALTER TABLE payments ADD COLUMN last_verification_attempt TIMESTAMP NULL"
            ];
            
            foreach ($alterQueries as $query) {
                try {
                    $database->execute($query);
                    echo "<p class='success'>✓ Added column: " . substr($query, 25, 30) . "...</p>";
                } catch (Exception $e) {
                    if (strpos($e->getMessage(), 'Duplicate column') !== false) {
                        echo "<p class='warning'>- Column already exists: " . substr($query, 25, 30) . "...</p>";
                    } else {
                        echo "<p class='error'>✗ Error: " . $e->getMessage() . "</p>";
                    }
                }
            }
            
            // Update currency and amount columns
            try {
                $database->execute("ALTER TABLE payments MODIFY COLUMN currency VARCHAR(3) DEFAULT 'TZS'");
                echo "<p class='success'>✓ Updated currency column</p>";
            } catch (Exception $e) {
                echo "<p class='warning'>- Currency column: " . $e->getMessage() . "</p>";
            }
            
            // 2. Create payment_logs table
            echo "<h3>2. Creating payment_logs table</h3>";
            
            $paymentLogsQuery = "
                CREATE TABLE IF NOT EXISTS payment_logs (
                    id VARCHAR(36) PRIMARY KEY,
                    payment_id VARCHAR(36) NOT NULL,
                    event_type ENUM('CREATED', 'PROCESSING', 'COMPLETED', 'FAILED', 'WEBHOOK_RECEIVED', 'VERIFIED', 'REFUNDED') NOT NULL,
                    gateway ENUM('STRIPE', 'FLUTTERWAVE') NOT NULL,
                    event_data JSON NULL,
                    ip_address VARCHAR(45) NULL,
                    user_agent TEXT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ";
            
            try {
                $database->execute($paymentLogsQuery);
                echo "<p class='success'>✓ Payment logs table created</p>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'already exists') !== false) {
                    echo "<p class='warning'>- Payment logs table already exists</p>";
                } else {
                    echo "<p class='error'>✗ Error creating payment logs table: " . $e->getMessage() . "</p>";
                }
            }
            
            // 3. Create payment_webhooks table
            echo "<h3>3. Creating payment_webhooks table</h3>";
            
            $paymentWebhooksQuery = "
                CREATE TABLE IF NOT EXISTS payment_webhooks (
                    id VARCHAR(36) PRIMARY KEY,
                    gateway ENUM('STRIPE', 'FLUTTERWAVE') NOT NULL,
                    webhook_id VARCHAR(255) NOT NULL,
                    event_type VARCHAR(100) NOT NULL,
                    payment_id VARCHAR(36) NULL,
                    status ENUM('PENDING', 'PROCESSED', 'FAILED', 'IGNORED') DEFAULT 'PENDING',
                    payload JSON NOT NULL,
                    signature_verified BOOLEAN DEFAULT FALSE,
                    processed_at TIMESTAMP NULL,
                    error_message TEXT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ";
            
            try {
                $database->execute($paymentWebhooksQuery);
                echo "<p class='success'>✓ Payment webhooks table created</p>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'already exists') !== false) {
                    echo "<p class='warning'>- Payment webhooks table already exists</p>";
                } else {
                    echo "<p class='error'>✗ Error creating payment webhooks table: " . $e->getMessage() . "</p>";
                }
            }
            
            // 4. Add indexes
            echo "<h3>4. Adding database indexes</h3>";
            
            $indexQueries = [
                "CREATE INDEX idx_payments_gateway ON payments(payment_gateway)",
                "CREATE INDEX idx_payments_reference ON payments(payment_reference)",
                "CREATE INDEX idx_payments_status ON payments(status)",
                "CREATE INDEX idx_payment_logs_payment ON payment_logs(payment_id)",
                "CREATE INDEX idx_payment_webhooks_gateway ON payment_webhooks(gateway)"
            ];
            
            foreach ($indexQueries as $query) {
                try {
                    $database->execute($query);
                    echo "<p class='success'>✓ Index created: " . substr($query, 13, 30) . "...</p>";
                } catch (Exception $e) {
                    if (strpos($e->getMessage(), 'Duplicate key') !== false) {
                        echo "<p class='warning'>- Index already exists: " . substr($query, 13, 30) . "...</p>";
                    } else {
                        echo "<p class='error'>✗ Error: " . $e->getMessage() . "</p>";
                    }
                }
            }
            
            // 5. Update existing records
            echo "<h3>5. Updating existing records</h3>";
            
            try {
                $database->execute("UPDATE payments SET currency = 'TZS' WHERE currency = 'USD' OR currency IS NULL");
                echo "<p class='success'>✓ Updated currency for existing payments</p>";
            } catch (Exception $e) {
                echo "<p class='error'>✗ Error updating currency: " . $e->getMessage() . "</p>";
            }
            
            echo "<h3>Migration Complete!</h3>";
            echo "<p class='success'>✓ Payment system database migration completed successfully!</p>";
            
            echo "<h3>Next Steps:</h3>";
            echo "<ol>";
            echo "<li>Update your Stripe API keys in config/app.php</li>";
            echo "<li>Update your Flutterwave API keys in config/app.php</li>";
            echo "<li>Test the payment system with a customer account</li>";
            echo "<li>Configure webhooks for production use</li>";
            echo "</ol>";
            
        } catch (Exception $e) {
            echo "<p class='error'>✗ Migration failed: " . $e->getMessage() . "</p>";
            echo "<p>Please check your database connection and try again.</p>";
        }
        
        echo "</div>";
    } else {
        ?>
        <div class="step">
            <h2>About This Migration</h2>
            <p>This migration will update your database to support the new payment system with Stripe and Flutterwave integration.</p>
            
            <h3>What will be updated:</h3>
            <ul>
                <li>Add new columns to the payments table</li>
                <li>Create payment_logs table for audit trail</li>
                <li>Create payment_webhooks table for webhook management</li>
                <li>Add database indexes for better performance</li>
                <li>Update existing payment records</li>
            </ul>
            
            <p><strong>Note:</strong> This migration is safe to run multiple times. Existing data will not be lost.</p>
        </div>
        
        <div class="step">
            <h2>Current Database Status</h2>
            <?php
            try {
                global $database;
                
                // Check payments table
                $result = $database->fetch("SHOW TABLES LIKE 'payments'");
                if ($result) {
                    echo "<p class='success'>✓ Payments table exists</p>";
                    
                    // Check for new columns
                    $columns = $database->fetchAll("DESCRIBE payments");
                    $existingColumns = array_column($columns, 'Field');
                    $requiredColumns = ['payment_gateway', 'payment_reference', 'flutterwave_tx_ref'];
                    
                    foreach ($requiredColumns as $col) {
                        if (in_array($col, $existingColumns)) {
                            echo "<p class='success'>✓ Column '$col' exists</p>";
                        } else {
                            echo "<p class='error'>✗ Column '$col' missing</p>";
                        }
                    }
                } else {
                    echo "<p class='error'>✗ Payments table does not exist</p>";
                }
                
                // Check payment_logs table
                $result = $database->fetch("SHOW TABLES LIKE 'payment_logs'");
                if ($result) {
                    echo "<p class='success'>✓ Payment logs table exists</p>";
                } else {
                    echo "<p class='error'>✗ Payment logs table does not exist</p>";
                }
                
                // Check payment_webhooks table
                $result = $database->fetch("SHOW TABLES LIKE 'payment_webhooks'");
                if ($result) {
                    echo "<p class='success'>✓ Payment webhooks table exists</p>";
                } else {
                    echo "<p class='error'>✗ Payment webhooks table does not exist</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>✗ Database error: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <form method="post">
            <button type="submit" name="run_migration" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
                Run Migration
            </button>
        </form>
        <?php
    }
    ?>
</body>
</html>
