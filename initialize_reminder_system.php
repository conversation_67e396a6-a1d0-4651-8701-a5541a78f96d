<?php
/**
 * Initialize Reminder System
 * Run this script once to set up the booking reminder system
 */

require_once 'config/app.php';

echo "<h1>Initializing Booking Reminder System</h1>";

try {
    // Initialize the reminder system
    echo "Setting up reminder system...<br>";
    $result = initializeReminderSystem();
    
    if ($result) {
        echo "✅ Reminder system initialized successfully!<br>";
        
        // Get statistics
        $stats = getReminderStats(30);
        echo "<h2>Current Statistics</h2>";
        echo "Total reminders: " . $stats['total_reminders'] . "<br>";
        echo "Sent reminders: " . $stats['sent_reminders'] . "<br>";
        echo "Failed reminders: " . $stats['failed_reminders'] . "<br>";
        echo "Pending reminders: " . $stats['pending_reminders'] . "<br>";
        
        // Check for upcoming reminders
        $upcoming = getUpcomingReminders(48);
        echo "<h2>Upcoming Reminders</h2>";
        echo "Found " . count($upcoming) . " reminders scheduled for the next 48 hours<br>";
        
        echo "<h2>✅ System Ready!</h2>";
        echo "<p>The booking reminder system is now fully operational.</p>";
        
        echo "<h2>Next Steps</h2>";
        echo "<ol>";
        echo "<li><strong>Test the system:</strong> <a href='test_reminder_system.php'>Run comprehensive tests</a></li>";
        echo "<li><strong>Monitor reminders:</strong> <a href='admin/booking-reminders.php'>Admin interface</a></li>";
        echo "<li><strong>Set up cron job:</strong> Add the cron job to run every 5 minutes</li>";
        echo "<li><strong>Test with bookings:</strong> Create and confirm test bookings</li>";
        echo "</ol>";
        
        echo "<h2>Cron Job Setup</h2>";
        echo "<p>Add this to your crontab:</p>";
        echo "<code style='background: #f5f5f5; padding: 10px; display: block;'>";
        echo "*/5 * * * * /usr/bin/php " . __DIR__ . "/cron/send_reminders.php";
        echo "</code>";
        
    } else {
        echo "❌ Failed to initialize reminder system<br>";
        echo "Please check the error logs for details.<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error during initialization: " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "Please check your database connection and permissions.<br>";
}

echo "<h2>System Status</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
echo "<h3>Reminder System Features:</h3>";
echo "<ul>";
echo "<li>✅ Database tables created</li>";
echo "<li>✅ Reminder scheduling integrated</li>";
echo "<li>✅ Email notifications enabled</li>";
echo "<li>✅ Priority-based processing</li>";
echo "<li>✅ Fail-safe recovery mechanisms</li>";
echo "<li>✅ Comprehensive logging</li>";
echo "<li>✅ Admin monitoring interface</li>";
echo "</ul>";
echo "</div>";
?>
