<?php
/**
 * Database Migration: Add OTP Fields for Forgot Password
 * Flix Salon & SPA - PHP Version
 */

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>";
echo "<html><head><title>OTP Fields Migration</title>";
echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:50px auto;padding:20px;}</style>";
echo "</head><body>";

echo "<h2>Adding OTP Fields to Users Table</h2>";

try {
    require_once __DIR__ . '/../config/app.php';

    if (!isset($database)) {
        throw new Exception("Database connection not available. Please check your configuration.");
    }

    echo "<p>✅ Database connection established.</p>";

    // Check if OTP fields already exist
    $columns = $database->fetchAll("SHOW COLUMNS FROM users LIKE 'reset_otp'");

    if (empty($columns)) {
        echo "<p>Adding OTP fields to users table...</p>";

        // Add OTP fields one by one with error handling
        try {
            $database->query("ALTER TABLE users ADD COLUMN reset_otp VARCHAR(6) NULL");
            echo "<p style='color: green;'>✅ Added reset_otp field</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ reset_otp field may already exist: " . $e->getMessage() . "</p>";
        }

        try {
            $database->query("ALTER TABLE users ADD COLUMN otp_expires_at TIMESTAMP NULL");
            echo "<p style='color: green;'>✅ Added otp_expires_at field</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ otp_expires_at field may already exist: " . $e->getMessage() . "</p>";
        }

        try {
            $database->query("ALTER TABLE users ADD COLUMN otp_attempts INT DEFAULT 0");
            echo "<p style='color: green;'>✅ Added otp_attempts field</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ otp_attempts field may already exist: " . $e->getMessage() . "</p>";
        }

        // Add indexes for better performance
        try {
            $database->query("CREATE INDEX idx_users_reset_otp ON users(reset_otp)");
            echo "<p style='color: green;'>✅ Created index on reset_otp</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Index on reset_otp may already exist: " . $e->getMessage() . "</p>";
        }

        try {
            $database->query("CREATE INDEX idx_users_otp_expires ON users(otp_expires_at)");
            echo "<p style='color: green;'>✅ Created index on otp_expires_at</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Index on otp_expires_at may already exist: " . $e->getMessage() . "</p>";
        }

    } else {
        echo "<p style='color: orange;'>⚠️ OTP fields already exist in users table.</p>";
    }
    
    // Verify the fields were added
    $otpColumn = $database->fetch("SHOW COLUMNS FROM users LIKE 'reset_otp'");
    $expiresColumn = $database->fetch("SHOW COLUMNS FROM users LIKE 'otp_expires_at'");
    $attemptsColumn = $database->fetch("SHOW COLUMNS FROM users LIKE 'otp_attempts'");
    
    if ($otpColumn && $expiresColumn && $attemptsColumn) {
        echo "<p style='color: green;'>✅ All OTP fields verified successfully!</p>";
        echo "<h3>Added Fields:</h3>";
        echo "<ul>";
        echo "<li><strong>reset_otp</strong>: VARCHAR(6) NULL - Stores the 6-digit OTP</li>";
        echo "<li><strong>otp_expires_at</strong>: TIMESTAMP NULL - OTP expiration time</li>";
        echo "<li><strong>otp_attempts</strong>: INT DEFAULT 0 - Failed OTP attempts counter</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ Error: Some OTP fields were not created properly.</p>";
    }
    
    echo "<h3>Migration Summary:</h3>";
    echo "<p>The forgot password system is now ready with the following features:</p>";
    echo "<ul>";
    echo "<li>✅ 6-digit OTP generation and storage</li>";
    echo "<li>✅ 15-minute OTP expiration</li>";
    echo "<li>✅ Rate limiting (max 3 attempts per hour)</li>";
    echo "<li>✅ Secure OTP validation</li>";
    echo "<li>✅ Email integration with existing SMTP system</li>";
    echo "</ul>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<p>1. Test the forgot password flow at: <a href='../auth/forgot-password.php'>../auth/forgot-password.php</a></p>";
    echo "<p>2. Ensure your email configuration is working properly</p>";
    echo "<p>3. Test with a valid email address in your system</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
}

echo "<hr>";
echo "<p><a href='../auth/forgot-password.php'>← Test Forgot Password System</a></p>";
echo "</body></html>";
