<?php
$currentPath = $_SERVER['REQUEST_URI'];
$basePath = getBasePath();

// Normalize current path for comparison
$normalizedCurrentPath = rtrim(parse_url($currentPath, PHP_URL_PATH), '/');
$customerBasePath = $basePath . '/customer';
$navigation = [
    [
        'name' => 'Dashboard',
        'href' => $basePath . '/customer',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7zm0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10M7 17h4" />'
    ],
    [
        'name' => 'Book Appointment',
        'href' => $basePath . '/customer/book',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />'
    ],
    [
        'name' => 'My Bookings',
        'href' => $basePath . '/customer/bookings',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />'
    ],
    [
        'name' => 'My Wishlist',
        'href' => $basePath . '/customer/wishlist',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />',
        'badge' => true
    ],
    [
        'name' => 'Points & Rewards',
        'href' => $basePath . '/customer/rewards',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />'
    ],
    [
        'name' => 'Profile',
        'href' => $basePath . '/customer/profile',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />'
    ]
];
?>

<nav class="space-y-1">
    <?php
    // Include wishlist functions for badge count
    require_once __DIR__ . '/wishlist_functions.php';

    foreach ($navigation as $item):
        // Normalize item href for comparison
        $normalizedItemHref = rtrim($item['href'], '/');

        // Check if this menu item is active
        $isActive = false;

        // Exact match for all pages
        if ($normalizedCurrentPath === $normalizedItemHref) {
            $isActive = true;
        }
        // For non-dashboard items, check sub-pages
        elseif ($normalizedItemHref !== $customerBasePath && strpos($normalizedCurrentPath . '/', $normalizedItemHref . '/') === 0) {
            $isActive = true;
        }

        // Get wishlist count for badge
        $wishlistCount = 0;
        if (isset($item['badge']) && $item['badge']) {
            $wishlistCount = isset($_SESSION['user_id'])
                ? getWishlistCount($_SESSION['user_id'])
                : getSessionWishlistCount();
        }
    ?>
        <a href="<?= $item['href'] ?>"
           class="<?= $isActive
               ? 'bg-salon-gold text-black'
               : 'text-gray-300 hover:bg-secondary-700 hover:text-white'
           ?> group flex items-center justify-between px-2 py-2 text-sm font-medium rounded-md transition-colors">
            <div class="flex items-center">
                <svg class="<?= $isActive ? 'text-black' : 'text-gray-400 group-hover:text-gray-300' ?> mr-3 flex-shrink-0 h-6 w-6"
                     fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <?= $item['icon'] ?>
                </svg>
                <?= $item['name'] ?>
            </div>
            <?php if (isset($item['badge']) && $item['badge'] && $wishlistCount > 0): ?>
                <span class="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full min-w-[20px] text-center" id="wishlist-badge">
                    <?= $wishlistCount ?>
                </span>
            <?php endif; ?>
        </a>
    <?php endforeach; ?>
</nav>
