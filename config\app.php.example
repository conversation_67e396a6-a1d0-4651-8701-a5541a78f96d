<?php
/**
 * Application Configuration Template
 * Flix Salon & SPA - PHP Version
 *
 * Copy this file to app.php and update the values for your environment
 */

// Configure persistent sessions before starting
if (session_status() === PHP_SESSION_NONE) {
    // Session configuration for persistent sessions
    ini_set('session.cookie_lifetime', 1209600); // 2 weeks (14 days * 24 hours * 60 minutes * 60 seconds)
    ini_set('session.gc_maxlifetime', 1209600);  // 2 weeks server-side cleanup
    ini_set('session.cookie_httponly', 1);       // Prevent XSS attacks
    ini_set('session.cookie_secure', 0);         // Set to 1 for HTTPS only (0 for development)
    ini_set('session.cookie_samesite', 'Lax');   // CSRF protection
    ini_set('session.use_strict_mode', 1);       // Prevent session fixation
    ini_set('session.use_only_cookies', 1);      // Only use cookies, not URL parameters

    // Set session cookie parameters for persistent sessions
    session_set_cookie_params([
        'lifetime' => 1209600,    // 2 weeks
        'path' => '/',
        'domain' => '',           // Current domain
        'secure' => false,        // Set to true for HTTPS only
        'httponly' => true,       // Prevent XSS
        'samesite' => 'Lax'      // CSRF protection
    ]);

    session_start();
}

// Application constants
define('APP_NAME', 'Flix Salon & SPA');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/flix-php'); // Update for your domain
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('UPLOAD_URL', APP_URL . '/uploads/');

// Get the base path for the application
function getBasePath() {
    // Update this path for your installation
    return '/flix-php';
}

// Get full URL with base path
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $basePath = getBasePath();
    return $protocol . '://' . $host . $basePath;
}

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'flix_salonce'); // Update database name
define('DB_USER', 'root');         // Update database username
define('DB_PASS', '');             // Update database password

// Security settings
define('JWT_SECRET', 'your-secret-key-here-change-this'); // CHANGE THIS!
define('BCRYPT_COST', 12);

// Email configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');    // Update with your email
define('SMTP_PASSWORD', 'your-app-password');       // Update with your app password

// Payment configuration (Stripe)
define('STRIPE_PUBLIC_KEY', 'pk_test_...');  // Update with your Stripe public key
define('STRIPE_SECRET_KEY', 'sk_test_...');  // Update with your Stripe secret key

// Application settings
define('TIMEZONE', 'Africa/Dar_es_Salaam');  // Update timezone as needed
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// Currency settings
define('CURRENCY_CODE', 'TZS');  // Update currency code
define('CURRENCY_SYMBOL', 'TSH'); // Update currency symbol

// Set timezone
date_default_timezone_set(TIMEZONE);

// Error reporting (set to 0 for production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// CORS headers for API requests
if (isset($_SERVER['HTTP_ORIGIN'])) {
    header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
    header('Access-Control-Allow-Credentials: true');
    header('Access-Control-Max-Age: 86400');
}

if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
        header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    }
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
    }
    exit(0);
}

// Helper function to redirect with proper base path
function redirect($url) {
    // If URL starts with /, prepend the base path
    if (strpos($url, '/') === 0) {
        $url = getBasePath() . $url;
    }
    header("Location: $url");
    exit();
}

// Helper function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Helper function to check user role
function hasRole($role) {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === $role;
}

// Helper function to get current user
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    global $database;
    return $database->fetch(
        "SELECT * FROM users WHERE id = ?",
        [$_SESSION['user_id']]
    );
}

// Include additional function files
require_once __DIR__ . '/../includes/service_functions.php';
require_once __DIR__ . '/../includes/service_category_functions.php';
require_once __DIR__ . '/../includes/booking_functions.php';
require_once __DIR__ . '/../includes/customer_functions.php';
require_once __DIR__ . '/../includes/staff_functions.php';
require_once __DIR__ . '/../includes/package_functions.php';
require_once __DIR__ . '/../includes/offer_functions.php';
require_once __DIR__ . '/../includes/upload_functions.php';
?>
