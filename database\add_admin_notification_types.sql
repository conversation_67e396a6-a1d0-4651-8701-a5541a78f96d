-- Add new notification types for admin-triggered staff notifications
-- Flix Salon & SPA - PHP Version

-- Add new notification types to the existing ENUM
ALTER TABLE notifications MODIFY COLUMN type ENUM(
    'BOOKING_NEW', 'BOOKING_CONFIRMED', 'BOOKING_CANCELLED', 'BOOKING_COMPLETED',
    'BOOKING_REMINDER', 'BOOKING_EXPIRED', 'BOOKING_NO_SHOW',
    'CUSTOMER_NEW', 'CUSTOMER_BIRTHDAY', 'NEWSLETTER_SUBSCRIBER',
    'PAYMENT_SUCCESS', 'PAYMENT_FAILED', 'REFUND_PROCESSED',
    'STAFF_NEW', 'STAFF_SCHEDULE_CHANGE', 'STAFF_LEAVE_REQUEST',
    'SYSTEM_MAINTENANCE', 'SYSTEM_UPDATE', 'SYSTEM_BACKUP',
    'PROMOTION_NEW', 'OFFER_EXPIRING', 'LOY<PERSON>TY_MILESTONE',
    'RE<PERSON>EW_NEW', 'COMPLAINT_NEW', 'GENERAL',
    'ADMIN_STAFF_ASSIGNED', 'ADMIN_STAFF_UNASSIGNED', 'ADMIN_BOOKING_UPDATED',
    'ADMIN_STATUS_CHANGED', 'ADMIN_BOOKING_RESCHEDULED', 'ADMIN_BOOKING_DETAILS_CHANGED'
) NOT NULL;
