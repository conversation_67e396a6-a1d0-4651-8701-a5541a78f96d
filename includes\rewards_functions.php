<?php
require_once __DIR__ . '/../config/database.php';

/**
 * Points & Rewards Management Functions
 * Handles loyalty program, points tracking, tier management, and reward redemptions
 */

/**
 * Get comprehensive rewards data for dashboard
 */
function getRewardsData() {
    return [
        'settings' => getRewardSettings(),
        'tiers' => getCustomerTiers(),
        'redemptions' => getRewardRedemptions(),
        'stats' => getRewardsStatistics(),
        'recentTransactions' => getRecentPointTransactions(10),
        'tierDistribution' => getTierDistribution(),
        'topRewards' => getTopRewards(5)
    ];
}

/**
 * Get reward system settings
 */
function getRewardSettings() {
    global $database;
    
    // Get settings from database or return defaults
    $settings = $database->fetchAll("
        SELECT setting_key, setting_value 
        FROM settings 
        WHERE category = 'points' OR category = 'loyalty'
    ");
    
    $config = [
        'pointsPerTZS' => 1,
        'welcomeBonus' => 100,
        'referralBonus' => 500,
        'birthdayBonus' => 200,
        'reviewBonus' => 50,
        'minimumRedemption' => 100,
        'pointsExpiry' => 365,
        'tierSystemEnabled' => true
    ];
    
    // Override with database values
    foreach ($settings as $setting) {
        switch ($setting['setting_key']) {
            case 'earn_rate':
                $config['pointsPerTZS'] = intval($setting['setting_value']);
                break;
            case 'redeem_rate':
                $config['minimumRedemption'] = intval($setting['setting_value']);
                break;
            case 'welcome_bonus':
                $config['welcomeBonus'] = intval($setting['setting_value']);
                break;
            case 'referral_bonus':
                $config['referralBonus'] = intval($setting['setting_value']);
                break;
            case 'birthday_bonus':
                $config['birthdayBonus'] = intval($setting['setting_value']);
                break;
            case 'review_bonus':
                $config['reviewBonus'] = intval($setting['setting_value']);
                break;
        }
    }
    
    return $config;
}

/**
 * Update reward settings
 */
function updateRewardSettings($settings) {
    global $database;
    
    $settingsMap = [
        'pointsPerTZS' => 'earn_rate',
        'minimumRedemption' => 'redeem_rate',
        'welcomeBonus' => 'welcome_bonus',
        'referralBonus' => 'referral_bonus',
        'birthdayBonus' => 'birthday_bonus',
        'reviewBonus' => 'review_bonus'
    ];
    
    foreach ($settingsMap as $key => $dbKey) {
        if (isset($settings[$key])) {
            // Update or insert setting
            $existing = $database->fetch("
                SELECT id FROM settings 
                WHERE category = 'points' AND setting_key = ?
            ", [$dbKey]);
            
            if ($existing) {
                $database->execute("
                    UPDATE settings 
                    SET setting_value = ?, updated_at = NOW() 
                    WHERE category = 'points' AND setting_key = ?
                ", [$settings[$key], $dbKey]);
            } else {
                $database->execute("
                    INSERT INTO settings (id, category, setting_key, setting_value, created_at, updated_at) 
                    VALUES (?, 'points', ?, ?, NOW(), NOW())
                ", [generateUUID(), $dbKey, $settings[$key]]);
            }
        }
    }
    
    return true;
}

/**
 * Get customer tier definitions from database
 */
function getCustomerTiers() {
    global $database;

    $tiers = $database->fetchAll("
        SELECT * FROM customer_tiers
        WHERE is_active = 1
        ORDER BY sort_order ASC, min_spent ASC
    ");

    if (empty($tiers)) {
        // If no tiers exist, create default ones
        createDefaultCustomerTiers();
        $tiers = $database->fetchAll("
            SELECT * FROM customer_tiers
            WHERE is_active = 1
            ORDER BY sort_order ASC, min_spent ASC
        ");
    }

    return array_map(function($tier) {
        return [
            'id' => $tier['id'],
            'name' => $tier['name'],
            'minSpent' => intval($tier['min_spent']),
            'pointsMultiplier' => floatval($tier['points_multiplier']),
            'benefits' => $tier['benefits'] ? explode(',', $tier['benefits']) : [],
            'color' => $tier['color'],
            'bgColor' => $tier['bg_color'],
            'isActive' => (bool)$tier['is_active'],
            'sortOrder' => intval($tier['sort_order'])
        ];
    }, $tiers);
}

/**
 * Create default customer tiers
 */
function createDefaultCustomerTiers() {
    global $database;

    $defaultTiers = [
        [
            'id' => generateUUID(),
            'name' => 'Bronze',
            'min_spent' => 0,
            'points_multiplier' => 1.0,
            'benefits' => 'Basic rewards,Birthday bonus',
            'color' => 'text-orange-600',
            'bg_color' => 'bg-orange-100',
            'sort_order' => 1
        ],
        [
            'id' => generateUUID(),
            'name' => 'Silver',
            'min_spent' => 200000, // TSH 200,000
            'points_multiplier' => 1.2,
            'benefits' => '20% bonus points,Priority booking,Birthday bonus',
            'color' => 'text-gray-600',
            'bg_color' => 'bg-gray-100',
            'sort_order' => 2
        ],
        [
            'id' => generateUUID(),
            'name' => 'Gold',
            'min_spent' => 500000, // TSH 500,000
            'points_multiplier' => 1.5,
            'benefits' => '50% bonus points,Priority booking,Exclusive offers,Birthday bonus',
            'color' => 'text-yellow-600',
            'bg_color' => 'bg-yellow-100',
            'sort_order' => 3
        ],
        [
            'id' => generateUUID(),
            'name' => 'VIP',
            'min_spent' => 1000000, // TSH 1,000,000
            'points_multiplier' => 2.0,
            'benefits' => 'Double points,VIP treatment,Exclusive services,Personal stylist,Birthday bonus',
            'color' => 'text-purple-600',
            'bg_color' => 'bg-purple-100',
            'sort_order' => 4
        ]
    ];

    foreach ($defaultTiers as $tier) {
        $database->execute("
            INSERT INTO customer_tiers (id, name, min_spent, points_multiplier, benefits, color, bg_color, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ", [
            $tier['id'], $tier['name'], $tier['min_spent'], $tier['points_multiplier'],
            $tier['benefits'], $tier['color'], $tier['bg_color'], $tier['sort_order']
        ]);
    }
}

/**
 * Get customer tier based on total spent
 */
function getCustomerTier($totalSpent) {
    $tiers = getCustomerTiers();

    // Find the highest tier the customer qualifies for
    $customerTier = $tiers[0]; // Default to first tier

    foreach ($tiers as $tier) {
        if ($totalSpent >= $tier['minSpent']) {
            $customerTier = $tier;
        }
    }

    return $customerTier;
}

/**
 * Create a new customer tier
 */
function createCustomerTier($tierData) {
    global $database;

    try {
        $tierId = generateUUID();

        $database->execute("
            INSERT INTO customer_tiers (id, name, min_spent, points_multiplier, benefits, color, bg_color, is_active, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ", [
            $tierId,
            $tierData['name'],
            $tierData['min_spent'],
            $tierData['points_multiplier'],
            $tierData['benefits'],
            $tierData['color'],
            $tierData['bg_color'],
            $tierData['is_active'],
            $tierData['sort_order']
        ]);

        return $tierId;
    } catch (Exception $e) {
        error_log("Error creating customer tier: " . $e->getMessage());
        return false;
    }
}

/**
 * Update an existing customer tier
 */
function updateCustomerTier($tierId, $tierData) {
    global $database;

    try {
        $result = $database->execute("
            UPDATE customer_tiers
            SET name = ?, min_spent = ?, points_multiplier = ?, benefits = ?,
                color = ?, bg_color = ?, is_active = ?, sort_order = ?, updated_at = NOW()
            WHERE id = ?
        ", [
            $tierData['name'],
            $tierData['min_spent'],
            $tierData['points_multiplier'],
            $tierData['benefits'],
            $tierData['color'],
            $tierData['bg_color'],
            $tierData['is_active'],
            $tierData['sort_order'],
            $tierId
        ]);

        return $result;
    } catch (Exception $e) {
        error_log("Error updating customer tier: " . $e->getMessage());
        return false;
    }
}

/**
 * Deactivate a customer tier (soft delete)
 */
function deleteCustomerTier($tierId) {
    global $database;

    try {
        // For safety, we'll soft delete by setting is_active to false instead of permanent deletion
        // This preserves data integrity and allows for tier reactivation if needed
        $result = $database->execute("
            UPDATE customer_tiers
            SET is_active = 0, updated_at = NOW()
            WHERE id = ?
        ", [$tierId]);

        return $result;
    } catch (Exception $e) {
        error_log("Error deleting customer tier: " . $e->getMessage());
        return false;
    }
}

/**
 * Get a single customer tier by ID
 */
function getCustomerTierById($tierId) {
    global $database;

    $tier = $database->fetch("
        SELECT * FROM customer_tiers WHERE id = ?
    ", [$tierId]);

    if (!$tier) {
        return null;
    }

    return [
        'id' => $tier['id'],
        'name' => $tier['name'],
        'minSpent' => intval($tier['min_spent']),
        'pointsMultiplier' => floatval($tier['points_multiplier']),
        'benefits' => $tier['benefits'] ? explode(',', $tier['benefits']) : [],
        'color' => $tier['color'],
        'bgColor' => $tier['bg_color'],
        'isActive' => (bool)$tier['is_active'],
        'sortOrder' => intval($tier['sort_order'])
    ];
}

/**
 * Get reward redemption options from database
 */
function getRewardRedemptions() {
    global $database;

    $rewards = $database->fetchAll("
        SELECT
            r.*,
            COALESCE(r.usage_count, 0) as usage_count
        FROM rewards r
        ORDER BY r.is_active DESC, r.points_required ASC
    ");

    return array_map(function($reward) {
        return [
            'id' => $reward['id'],
            'name' => $reward['name'],
            'description' => $reward['description'],
            'pointsCost' => intval($reward['points_required']),
            'value' => floatval($reward['value']),
            'type' => $reward['type'],
            'isActive' => (bool)$reward['is_active'],
            'usageCount' => intval($reward['usage_count']),
            'createdAt' => $reward['created_at'],
            'updatedAt' => $reward['updated_at']
        ];
    }, $rewards);
}

/**
 * Create a new reward
 */
function createReward($data) {
    global $database;

    $id = generateUUID();

    $result = $database->query("
        INSERT INTO rewards (id, name, description, points_required, value, type, is_active, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    ", [
        $id,
        $data['name'],
        $data['description'],
        $data['points_required'],
        $data['value'],
        $data['type'],
        $data['is_active'] ?? 1
    ]);

    return $result ? $id : false;
}

/**
 * Update an existing reward
 */
function updateReward($id, $data) {
    global $database;

    return $database->query("
        UPDATE rewards
        SET name = ?, description = ?, points_required = ?, value = ?, type = ?, is_active = ?, updated_at = NOW()
        WHERE id = ?
    ", [
        $data['name'],
        $data['description'],
        $data['points_required'],
        $data['value'],
        $data['type'],
        $data['is_active'] ?? 1,
        $id
    ]);
}

/**
 * Delete a reward
 */
function deleteReward($id) {
    global $database;

    return $database->query("DELETE FROM rewards WHERE id = ?", [$id]);
}

/**
 * Get a single reward by ID
 */
function getReward($id) {
    global $database;

    $reward = $database->fetch("SELECT * FROM rewards WHERE id = ?", [$id]);

    if ($reward) {
        return [
            'id' => $reward['id'],
            'name' => $reward['name'],
            'description' => $reward['description'],
            'pointsCost' => intval($reward['points_required']),
            'value' => floatval($reward['value']),
            'type' => $reward['type'],
            'isActive' => (bool)$reward['is_active'],
            'createdAt' => $reward['created_at'],
            'updatedAt' => $reward['updated_at']
        ];
    }

    return null;
}

/**
 * Get rewards statistics
 */
function getRewardsStatistics() {
    global $database;
    
    $stats = [];
    
    // Total points issued
    $pointsIssued = $database->fetch("
        SELECT COALESCE(SUM(points), 0) as total
        FROM point_transactions 
        WHERE type = 'EARNED'
    ");
    $stats['totalPointsIssued'] = intval($pointsIssued['total']);
    
    // Total points redeemed
    $pointsRedeemed = $database->fetch("
        SELECT COALESCE(SUM(ABS(points)), 0) as total
        FROM point_transactions 
        WHERE type = 'REDEMPTION'
    ");
    $stats['totalPointsRedeemed'] = intval($pointsRedeemed['total']);
    
    // Active members (customers with points > 0)
    $activeMembers = $database->fetch("
        SELECT COUNT(*) as count
        FROM users 
        WHERE role = 'CUSTOMER' AND points > 0
    ");
    $stats['activeMembers'] = intval($activeMembers['count']);
    
    // Average points per customer
    $avgPoints = $database->fetch("
        SELECT COALESCE(AVG(points), 0) as average
        FROM users 
        WHERE role = 'CUSTOMER'
    ");
    $stats['averagePointsPerCustomer'] = round(floatval($avgPoints['average']), 1);
    
    // Total customers
    $totalCustomers = $database->fetch("
        SELECT COUNT(*) as count
        FROM users 
        WHERE role = 'CUSTOMER'
    ");
    $stats['totalCustomers'] = intval($totalCustomers['count']);
    
    // Points engagement rate
    $stats['engagementRate'] = $stats['totalCustomers'] > 0 
        ? round(($stats['activeMembers'] / $stats['totalCustomers']) * 100, 1) 
        : 0;
    
    return $stats;
}

/**
 * Get recent point transactions
 */
function getRecentPointTransactions($limit = 10) {
    global $database;
    
    $transactions = $database->fetchAll("
        SELECT 
            pt.*,
            u.name as customer_name,
            u.email as customer_email
        FROM point_transactions pt
        LEFT JOIN users u ON pt.user_id = u.id
        ORDER BY pt.created_at DESC
        LIMIT ?
    ", [$limit]);
    
    return array_map(function($transaction) {
        return [
            'id' => $transaction['id'],
            'customerName' => $transaction['customer_name'] ?? 'Unknown',
            'customerEmail' => $transaction['customer_email'] ?? '',
            'points' => intval($transaction['points']),
            'type' => $transaction['type'],
            'description' => $transaction['description'],
            'createdAt' => $transaction['created_at']
        ];
    }, $transactions);
}

/**
 * Get tier distribution of customers
 */
function getTierDistribution() {
    global $database;
    
    $customers = $database->fetchAll("
        SELECT 
            u.id,
            COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as total_spent
        FROM users u
        LEFT JOIN bookings b ON u.id = b.user_id
        WHERE u.role = 'CUSTOMER'
        GROUP BY u.id
    ");
    
    $tiers = getCustomerTiers();
    $distribution = [];
    
    // Initialize distribution
    foreach ($tiers as $tier) {
        $distribution[$tier['name']] = [
            'name' => $tier['name'],
            'count' => 0,
            'color' => $tier['color']
        ];
    }
    
    // Count customers in each tier
    foreach ($customers as $customer) {
        $tier = getCustomerTier($customer['total_spent']);
        $distribution[$tier['name']]['count']++;
    }
    
    return array_values($distribution);
}

/**
 * Get top redeemed rewards
 */
function getTopRewards($limit = 5) {
    $rewards = getRewardRedemptions();
    
    // Sort by usage count
    usort($rewards, function($a, $b) {
        return $b['usageCount'] - $a['usageCount'];
    });
    
    return array_slice($rewards, 0, $limit);
}

/**
 * Award points to customer
 */
function awardPoints($customerId, $points, $description, $type = 'EARNED') {
    global $database;
    
    try {
        // Start transaction
        $database->beginTransaction();
        
        // Add points to customer
        $database->execute("
            UPDATE users 
            SET points = points + ?, updated_at = NOW() 
            WHERE id = ? AND role = 'CUSTOMER'
        ", [$points, $customerId]);
        
        // Record transaction
        $database->execute("
            INSERT INTO point_transactions (id, user_id, points, type, description, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ", [generateUUID(), $customerId, $points, $type, $description]);
        
        $database->commit();
        return true;
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
}

/**
 * Redeem points from customer
 */
function redeemPoints($customerId, $points, $description) {
    global $database;
    
    try {
        // Check if customer has enough points
        $customer = $database->fetch("
            SELECT points FROM users 
            WHERE id = ? AND role = 'CUSTOMER'
        ", [$customerId]);
        
        if (!$customer || $customer['points'] < $points) {
            throw new Exception('Insufficient points');
        }
        
        // Start transaction
        $database->beginTransaction();
        
        // Deduct points from customer
        $database->execute("
            UPDATE users 
            SET points = points - ?, updated_at = NOW() 
            WHERE id = ? AND role = 'CUSTOMER'
        ", [$points, $customerId]);
        
        // Record transaction (negative points for redemption)
        $database->execute("
            INSERT INTO point_transactions (id, user_id, points, type, description, created_at) 
            VALUES (?, ?, ?, 'REDEMPTION', ?, NOW())
        ", [generateUUID(), $customerId, -$points, $description]);
        
        $database->commit();
        return true;
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
}

/**
 * Calculate points for booking amount
 */
function calculatePointsForBooking($amount, $customerId = null) {
    $settings = getRewardSettings();
    $basePoints = floor($amount / 1000); // 1 point per TSH 1,000
    
    if ($customerId) {
        // Get customer tier for multiplier
        $totalSpent = getTotalSpentByCustomer($customerId);
        $tier = getCustomerTier($totalSpent);
        $basePoints = floor($basePoints * $tier['pointsMultiplier']);
    }
    
    return $basePoints;
}

/**
 * Get total spent by customer
 */
function getTotalSpentByCustomer($customerId) {
    global $database;
    
    $result = $database->fetch("
        SELECT COALESCE(SUM(total_amount), 0) as total_spent
        FROM bookings 
        WHERE user_id = ? AND status = 'COMPLETED'
    ", [$customerId]);
    
    return floatval($result['total_spent']);
}

/**
 * Process welcome bonus for new customer
 */
function processWelcomeBonus($customerId) {
    $settings = getRewardSettings();
    
    if ($settings['welcomeBonus'] > 0) {
        awardPoints($customerId, $settings['welcomeBonus'], 'Welcome bonus for new customer');
    }
}

/**
 * Process birthday bonus
 */
function processBirthdayBonus($customerId) {
    $settings = getRewardSettings();
    
    if ($settings['birthdayBonus'] > 0) {
        awardPoints($customerId, $settings['birthdayBonus'], 'Birthday bonus');
    }
}

/**
 * Process referral bonus
 */
function processReferralBonus($referrerId, $referredId) {
    $settings = getRewardSettings();
    
    if ($settings['referralBonus'] > 0) {
        // Award points to both referrer and referred customer
        awardPoints($referrerId, $settings['referralBonus'], 'Referral bonus - referred a friend');
        awardPoints($referredId, $settings['referralBonus'], 'Referral bonus - joined through referral');
    }
}
