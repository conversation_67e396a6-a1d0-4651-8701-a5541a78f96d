<?php
/**
 * DPO Pay Payment Page
 * Handles DPO Pay payment processing for bookings
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('/auth/login.php');
}

// Check if DPO is enabled
if (!DPO_ENABLED) {
    redirect('/customer/payments?error=dpo_disabled');
}

// Get payment ID from URL
$paymentId = $_GET['payment_id'] ?? '';

if (empty($paymentId)) {
    redirect('/customer/payments?error=invalid_payment');
}

// Get payment details
$payment = $database->fetch("
    SELECT p.*, b.user_id, b.id as booking_id, b.date as booking_date, b.start_time as booking_time,
           u.name, u.email, u.phone
    FROM payments p
    INNER JOIN bookings b ON p.booking_id = b.id
    INNER JOIN users u ON b.user_id = u.id
    WHERE p.id = ? AND b.user_id = ? AND p.payment_gateway = 'DPO' AND p.status = 'PENDING'
", [$paymentId, $_SESSION['user_id']]);

if (!$payment) {
    redirect('/customer/payments?error=payment_not_found');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Process DPO payment
        $result = processDpoPayment($paymentId);
        
        if ($result['success']) {
            // Redirect to DPO payment page
            header("Location: " . $result['payment_url']);
            exit;
        } else {
            $error = $result['error'] ?? 'Payment processing failed';
        }
        
    } catch (Exception $e) {
        $error = 'An error occurred while processing your payment: ' . $e->getMessage();
        error_log("DPO Payment Error: " . $e->getMessage());
    }
}

// Get booking details for display
$bookingDetails = $database->fetch("
    SELECT b.*,
           b.date as booking_date,
           b.start_time as booking_time,
           CASE
               WHEN b.service_id IS NOT NULL THEN s.name
               WHEN b.package_id IS NOT NULL THEN p.name
           END as service_name,
           CASE
               WHEN b.service_id IS NOT NULL THEN s.price
               WHEN b.package_id IS NOT NULL THEN p.price
           END as service_price
    FROM bookings b
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN packages p ON b.package_id = p.id
    WHERE b.id = ?
", [$payment['booking_id']]);

if (!$bookingDetails) {
    redirect('/customer/payments?error=booking_not_found');
}

$pageTitle = 'DPO Pay Payment';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - <?= APP_NAME ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .bg-secondary-900 { background-color: #0f172a; }
        .bg-secondary-800 { background-color: #1e293b; }
        .bg-secondary-700 { background-color: #334155; }
        .text-salon-gold { color: #f59e0b; }
        .bg-salon-gold { background-color: #f59e0b; }
        .border-salon-gold { border-color: #f59e0b; }
    </style>
</head>
<body class="bg-secondary-900 text-white">

<div class="min-h-screen py-8">
    <div class="max-w-2xl mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-salon-gold mb-2">Complete Your Payment</h1>
            <p class="text-gray-400">Secure payment powered by DPO Pay</p>
        </div>

        <!-- Payment Summary -->
        <div class="bg-secondary-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-salon-gold">
                <i class="fas fa-receipt mr-2"></i>Payment Summary
            </h2>
            
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-400">Service:</span>
                    <span class="font-medium"><?= htmlspecialchars($bookingDetails['service_name'] ?? 'Service') ?></span>
                </div>

                <div class="flex justify-between">
                    <span class="text-gray-400">Date & Time:</span>
                    <span class="font-medium">
                        <?php if (!empty($bookingDetails['booking_date']) && !empty($bookingDetails['booking_time'])): ?>
                            <?= date('M j, Y', strtotime($bookingDetails['booking_date'])) ?> at
                            <?= date('g:i A', strtotime($bookingDetails['booking_time'])) ?>
                        <?php else: ?>
                            Date & Time TBD
                        <?php endif; ?>
                    </span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-gray-400">Payment Reference:</span>
                    <span class="font-medium"><?= htmlspecialchars($payment['payment_reference']) ?></span>
                </div>
                
                <hr class="border-secondary-700">
                
                <div class="flex justify-between text-lg font-bold">
                    <span>Total Amount:</span>
                    <span class="text-salon-gold"><?= CURRENCY_SYMBOL ?> <?= number_format($payment['amount']) ?></span>
                </div>
            </div>
        </div>

        <!-- Error Display -->
        <?php if (isset($error)): ?>
        <div class="bg-red-600 text-white p-4 rounded-lg mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <span><?= htmlspecialchars($error) ?></span>
            </div>
        </div>
        <?php endif; ?>

        <!-- Payment Form -->
        <div class="bg-secondary-800 rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4 text-salon-gold">
                <i class="fas fa-credit-card mr-2"></i>Payment Method
            </h3>
            
            <div class="mb-6">
                <div class="flex items-center p-4 border border-salon-gold rounded-lg bg-secondary-700">
                    <img src="<?= getBasePath() ?>/assets/images/dpo-logo.png" alt="DPO Pay" class="h-8 mr-3" onerror="this.style.display='none'">
                    <div>
                        <h4 class="font-semibold text-salon-gold">DPO Pay</h4>
                        <p class="text-sm text-gray-400">Secure payment with cards, mobile money & bank transfer</p>
                    </div>
                </div>
            </div>

            <form method="POST" id="payment-form">
                <div class="mb-6">
                    <div class="bg-secondary-700 rounded-lg p-4">
                        <h4 class="font-semibold mb-2 text-salon-gold">Accepted Payment Methods:</h4>
                        <div class="grid grid-cols-2 gap-2 text-sm text-gray-400">
                            <div><i class="fas fa-credit-card mr-2"></i>Visa & Mastercard</div>
                            <div><i class="fas fa-mobile-alt mr-2"></i>Mobile Money</div>
                            <div><i class="fas fa-university mr-2"></i>Bank Transfer</div>
                            <div><i class="fas fa-wallet mr-2"></i>Digital Wallets</div>
                        </div>
                    </div>
                </div>

                <button type="submit" id="pay-button" class="w-full bg-salon-gold hover:bg-yellow-600 text-black font-semibold py-4 px-6 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    <span id="button-text">
                        <i class="fas fa-lock mr-2"></i>
                        Pay <?= CURRENCY_SYMBOL ?> <?= number_format($payment['amount']) ?> Securely
                    </span>
                    <div id="spinner" class="hidden">
                        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-black mx-auto"></div>
                    </div>
                </button>
            </form>
        </div>

        <!-- Security Notice -->
        <div class="mt-6 text-center">
            <div class="inline-flex items-center text-sm text-gray-400">
                <i class="fas fa-shield-alt mr-2 text-green-500"></i>
                Your payment is secured with 256-bit SSL encryption
            </div>
        </div>

        <!-- Back Link -->
        <div class="mt-6 text-center">
            <a href="<?= getBasePath() ?>/customer/payments" class="text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Payments
            </a>
        </div>
    </div>
</div>

<script>
document.getElementById('payment-form').addEventListener('submit', function(e) {
    const button = document.getElementById('pay-button');
    const buttonText = document.getElementById('button-text');
    const spinner = document.getElementById('spinner');
    
    // Disable button and show loading
    button.disabled = true;
    buttonText.classList.add('hidden');
    spinner.classList.remove('hidden');
    
    // Re-enable button after 10 seconds if still on page (in case of error)
    setTimeout(function() {
        if (button.disabled) {
            button.disabled = false;
            buttonText.classList.remove('hidden');
            spinner.classList.add('hidden');
        }
    }, 10000);
});
</script>

</body>
</html>
