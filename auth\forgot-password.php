<?php
/**
 * Forgot Password Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/forgot_password_functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user['role'] === 'ADMIN') {
        redirect('/admin');
    } elseif ($user['role'] === 'STAFF') {
        redirect('/staff');
    } else {
        redirect('/customer');
    }
}

$error = '';
$success = '';
$email = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // CSRF Protection
    if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
        $error = 'Invalid request. Please try again.';
    } else {
        $email = sanitize($_POST['email'] ?? '');

        if (empty($email)) {
            $error = 'Please enter your email address';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address';
        } else {
            // Check if email exists
            if (!emailExists($email)) {
                $error = 'Email address not found in our system';
            } else {
                // Send OTP
                $result = sendPasswordResetOTP($email);
                
                if ($result['success']) {
                    // Store email in session for OTP verification
                    $_SESSION['reset_email'] = $email;
                    $_SESSION['reset_step'] = 'verify_otp';
                    
                    // Redirect to OTP verification page
                    redirect('/auth/verify-otp.php');
                } else {
                    $error = $result['error'];
                }
            }
        }
    }
}

$pageTitle = "Forgot Password";
$pageDescription = "Reset your Flix Salon & SPA account password";

// Include header
include __DIR__ . '/../includes/header.php';
?>
<!-- Enhanced Auth Page Styles - Unified Design -->
<style>
    /* Auth page background with gradient overlay */
    .auth-main {
        min-height: calc(100vh - 140px);
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #141414 100%);
        background-attachment: fixed;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
    }

    .auth-main::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 70% 80%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
        pointer-events: none;
    }

    /* Glass container with enhanced effects */
    .auth-container {
        background: rgba(10, 10, 10, 0.85);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(245, 158, 11, 0.2);
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.8),
            0 0 0 1px rgba(245, 158, 11, 0.1),
            inset 0 1px 0 rgba(245, 158, 11, 0.1);
        position: relative;
        overflow: hidden;
    }

    .auth-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.5), transparent);
    }

    /* Enhanced input styling */
    .input-group {
        position: relative;
    }

    .input-group input {
        background: rgba(20, 20, 20, 0.8) !important;
        border: 1px solid rgba(75, 85, 99, 0.5) !important;
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .input-group input:focus {
        background: rgba(20, 20, 20, 0.95) !important;
        border-color: rgba(245, 158, 11, 0.8) !important;
        box-shadow:
            0 0 0 3px rgba(245, 158, 11, 0.1),
            0 4px 12px rgba(245, 158, 11, 0.15);
        transform: translateY(-1px);
    }

    .input-group input:focus + .input-border {
        transform: scaleX(1);
    }

    .input-border {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #f59e0b, #fbbf24, #f59e0b);
        transform: scaleX(0);
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 1px;
    }

    /* Enhanced button styling */
    .btn-primary {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #f59e0b 100%);
        background-size: 200% 200%;
        border: 1px solid rgba(245, 158, 11, 0.3);
        box-shadow:
            0 4px 15px rgba(245, 158, 11, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .btn-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #fbbf24 100%);
        background-position: 100% 0;
        transform: translateY(-2px);
        box-shadow:
            0 8px 25px rgba(245, 158, 11, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border-color: rgba(245, 158, 11, 0.6);
    }

    .btn-primary:hover::before {
        left: 100%;
    }

    .btn-primary:active {
        transform: translateY(0);
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    }

    /* Enhanced info card */
    .info-card {
        background: rgba(10, 10, 10, 0.9);
        border: 1px solid rgba(59, 130, 246, 0.3);
        backdrop-filter: blur(15px);
        box-shadow:
            0 8px 25px rgba(59, 130, 246, 0.1),
            inset 0 1px 0 rgba(59, 130, 246, 0.1);
        position: relative;
        overflow: hidden;
    }

    .info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
    }

    /* Enhanced animations */
    .error-message {
        animation: slideInEnhanced 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes slideInEnhanced {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* Link enhancements */
    a {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    a:hover {
        text-shadow: 0 0 8px rgba(245, 158, 11, 0.5);
    }

    /* Icon container enhancement */
    .auth-container .mx-auto.h-16.w-16 {
        background: rgba(245, 158, 11, 0.15);
        border: 1px solid rgba(245, 158, 11, 0.3);
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.2);
    }
</style>

<!-- Auth Page Content -->
<div class="auth-main">
    <div class="max-w-md w-full space-y-8">
        <div class="auth-container auth-card rounded-2xl p-8 sm:p-10">
            <div class="text-center mb-8">
                <div class="mx-auto h-16 w-16 bg-salon-gold/20 rounded-full flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m0 0a2 2 0 01-2 2m2-2H9m12 0V9a2 2 0 00-2-2M3 11a2 2 0 012-2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m0 0a2 2 0 01-2 2m2-2H9m12 0V9a2 2 0 00-2-2" />
                    </svg>
                </div>
                <h2 class="text-2xl sm:text-3xl font-bold text-white mb-2">Forgot Password?</h2>
                <p class="text-gray-300 text-sm sm:text-base">
                    No worries! Enter your email and we'll send you a verification code
                </p>
            </div>

            <form class="space-y-6" method="POST" autocomplete="off">
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">

                <?php if ($error): ?>
                    <div class="error-message bg-red-500/10 border border-red-500/30 rounded-xl p-4 backdrop-blur-sm">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-400 mb-1">Error</h3>
                                <p class="text-sm text-red-300"><?= htmlspecialchars($error) ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="space-y-6">
                    <div class="input-group">
                        <label for="email" class="block text-sm font-semibold text-gray-200 mb-2">
                            Email Address
                        </label>
                        <div class="relative">
                            <input id="email" name="email" type="email" autocomplete="off" required
                                   value="<?= htmlspecialchars($email) ?>"
                                   class="appearance-none relative block w-full px-4 py-3 border border-gray-600 placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm"
                                   placeholder="Enter your email address">
                            <div class="input-border"></div>
                        </div>
                    </div>
                </div>

                <!-- Information Card -->
                <div class="info-card rounded-xl p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-400 mb-1">How it works</h3>
                            <ul class="text-sm text-blue-300 space-y-1">
                                <li>• We'll send a 6-digit code to your email</li>
                                <li>• Enter the code to verify your identity</li>
                                <li>• Create a new secure password</li>
                                <li>• Code expires in 15 minutes for security</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="pt-2">
                    <button type="submit"
                            class="btn-primary group relative w-full flex justify-center py-3 px-6 border border-transparent text-sm font-semibold rounded-xl text-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold focus:ring-offset-gray-800">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-4">
                            <svg class="h-5 w-5 text-black/80 group-hover:text-black transition-colors" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                            </svg>
                        </span>
                        Send Verification Code
                    </button>
                </div>

                <div class="text-center pt-6">
                    <p class="text-sm text-gray-300">
                        Remember your password?
                        <a href="<?= getBasePath() ?>/auth/login.php"
                           class="font-semibold text-salon-gold hover:text-gold-light transition-colors ml-1">
                            Sign in here
                        </a>
                    </p>
                </div>
            </form>

        </div>
    </div>
</div>

<?php
// Include footer
include __DIR__ . '/../includes/footer.php';
?>
