<?php
/**
 * Reviews API Endpoint
 * Handles review creation and management
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/reviews_functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Please login to submit a review.']);
    exit;
}

// Handle POST request for creating reviews
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate CSRF token
        if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
            throw new Exception('Invalid security token. Please refresh the page and try again.');
        }
        
        // Validate required fields
        $requiredFields = ['title', 'comment', 'rating', 'review_type'];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("Please fill in all required fields.");
            }
        }
        
        // Validate rating
        $rating = intval($_POST['rating']);
        if ($rating < 1 || $rating > 5) {
            throw new Exception('Please select a valid rating between 1 and 5 stars.');
        }
        
        // Validate service or package selection
        $serviceId = !empty($_POST['service_id']) ? intval($_POST['service_id']) : null;
        $packageId = !empty($_POST['package_id']) ? intval($_POST['package_id']) : null;
        
        if ($_POST['review_type'] === 'service' && !$serviceId) {
            throw new Exception('Please select a service to review.');
        }
        
        if ($_POST['review_type'] === 'package' && !$packageId) {
            throw new Exception('Please select a package to review.');
        }
        
        if (!$serviceId && !$packageId) {
            throw new Exception('Please select either a service or package to review.');
        }
        
        // Check if customer has already reviewed this service/package
        $existingReview = getCustomerReview($_SESSION['user_id'], $serviceId, $packageId);
        if ($existingReview) {
            throw new Exception('You have already submitted a review for this service/package.');
        }
        
        // Check if customer can review (has completed booking)
        $canReview = canCustomerReview($_SESSION['user_id'], $serviceId, $packageId);
        
        // Prepare review data
        $reviewData = [
            'customer_id' => $_SESSION['user_id'],
            'service_id' => $serviceId,
            'package_id' => $packageId,
            'rating' => $rating,
            'title' => trim($_POST['title']),
            'comment' => trim($_POST['comment']),
            'is_verified' => $canReview
        ];
        
        // Create the review
        $reviewId = createReview($reviewData);
        
        if ($reviewId) {
            // Success response
            echo json_encode([
                'success' => true,
                'message' => 'Thank you for your review! It has been submitted for moderation and will appear on the site once approved.',
                'review_id' => $reviewId,
                'is_verified' => $canReview
            ]);
        } else {
            throw new Exception('Failed to submit review. Please try again.');
        }
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit;
}

// Handle GET request for fetching reviews (AJAX)
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action'])) {
    try {
        switch ($_GET['action']) {
            case 'get_reviews':
                $filters = [
                    'rating' => $_GET['rating'] ?? '',
                    'service_id' => $_GET['service_id'] ?? '',
                    'package_id' => $_GET['package_id'] ?? '',
                    'is_verified' => isset($_GET['verified']) ? ($_GET['verified'] === '1') : '',
                    'search' => $_GET['search'] ?? ''
                ];
                
                // Remove empty filters
                $filters = array_filter($filters, function($value) {
                    return $value !== '' && $value !== null;
                });
                
                $page = max(1, intval($_GET['page'] ?? 1));
                $limit = 12;
                
                $reviews = getReviews($filters, $page, $limit);
                $totalReviews = getReviewsCount($filters);
                $totalPages = ceil($totalReviews / $limit);
                
                echo json_encode([
                    'success' => true,
                    'reviews' => $reviews,
                    'pagination' => [
                        'current_page' => $page,
                        'total_pages' => $totalPages,
                        'total_reviews' => $totalReviews,
                        'per_page' => $limit
                    ]
                ]);
                break;
                
            case 'get_stats':
                $stats = getReviewStats();
                echo json_encode([
                    'success' => true,
                    'stats' => $stats
                ]);
                break;
                
            case 'get_featured':
                $limit = intval($_GET['limit'] ?? 6);
                $featured = getFeaturedReviews($limit);
                echo json_encode([
                    'success' => true,
                    'featured_reviews' => $featured
                ]);
                break;
                
            default:
                throw new Exception('Invalid action specified.');
        }
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit;
}

// Invalid request method
http_response_code(405);
echo json_encode([
    'success' => false,
    'message' => 'Method not allowed.'
]);
?>
