<?php
/**
 * Services Page
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/config/app.php';

// Get filter parameters
$searchQuery = sanitize($_GET['search'] ?? '');
$selectedCategory = sanitize($_GET['category'] ?? '');
$minPrice = $_GET['min_price'] ?? '';
$maxPrice = $_GET['max_price'] ?? '';
$maxDuration = $_GET['max_duration'] ?? '';

// Find the selected category details for display
$selectedCategoryDetails = null;
$isRandomLoad = false; // This is now handled by the dropdown navigation
if ($selectedCategory) {
    foreach (getActiveServiceCategories() as $category) {
        if ($category['name'] === $selectedCategory) {
            $selectedCategoryDetails = $category;
            break;
        }
    }
}

// Get all active service categories
$activeCategories = getActiveServiceCategories();

// Build query for filtered services
$whereClause = "WHERE is_active = 1";
$params = [];

if ($searchQuery) {
    $whereClause .= " AND (name LIKE ? OR description LIKE ?)";
    $params[] = "%$searchQuery%";
    $params[] = "%$searchQuery%";
}

if ($selectedCategory) {
    $whereClause .= " AND category = ?";
    $params[] = $selectedCategory;
}

if ($minPrice !== '') {
    $whereClause .= " AND price >= ?";
    $params[] = floatval($minPrice);
}

if ($maxPrice !== '') {
    $whereClause .= " AND price <= ?";
    $params[] = floatval($maxPrice);
}

if ($maxDuration !== '') {
    $whereClause .= " AND duration <= ?";
    $params[] = intval($maxDuration);
}

// Get filtered services
$allServices = $database->fetchAll(
    "SELECT * FROM services $whereClause ORDER BY category, name",
    $params
);

// Group services by category using the dynamic categories
$servicesByCategory = [];
$categoryDescriptions = [];

// Check if any filters are applied
$hasFilters = $searchQuery || $selectedCategory || $minPrice !== '' || $maxPrice !== '' || $maxDuration !== '';

// First, populate category descriptions from the service_categories table
foreach ($activeCategories as $category) {
    $categoryDescriptions[$category['name']] = $category['description'];
    $servicesByCategory[$category['name']] = [];
}

// Then group services under their categories
foreach ($allServices as $service) {
    $categoryName = $service['category'];
    if ($categoryName && isset($servicesByCategory[$categoryName])) {
        $servicesByCategory[$categoryName][] = $service;
    }
}

// Always remove empty categories when filters are applied
// This ensures only relevant categories with matching services are shown
if ($hasFilters) {
    $servicesByCategory = array_filter($servicesByCategory, function($services) {
        return !empty($services);
    });

    // If we have filtered results, sort categories by the number of services (most relevant first)
    if (!empty($servicesByCategory)) {
        uksort($servicesByCategory, function($a, $b) use ($servicesByCategory) {
            $countA = count($servicesByCategory[$a]);
            $countB = count($servicesByCategory[$b]);

            // Sort by count descending, then by name ascending
            if ($countA === $countB) {
                return strcmp($a, $b);
            }
            return $countB - $countA;
        });
    }
} else {
    // When no filters are applied, remove empty categories to keep the page clean
    $servicesByCategory = array_filter($servicesByCategory, function($services) {
        return !empty($services);
    });
}

$pageTitle = $selectedCategory ? $selectedCategory . " Services" : "Our Services";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Line clamp utilities for text truncation */
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Enhanced hover effects */
.group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
}

.group:hover .group-hover\:text-salon-gold {
    color: #D4AF37;
}

/* Smooth transitions for all interactive elements */
.transition-all {
    transition: all 0.3s ease;
}
</style>

<!-- Hero Section -->
<section class="relative bg-salon-black py-24">
    <div class="absolute inset-0 bg-gradient-to-r from-salon-black via-salon-black/90 to-transparent"></div>
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30" 
         style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-3xl">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                Our Premium <span class="text-salon-gold">Services</span>
            </h1>
            <p class="text-xl text-gray-300 mb-8">
                Discover our comprehensive range of beauty and wellness services, designed to make you look and feel your absolute best.
            </p>
            <div class="flex flex-col sm:flex-row gap-4">
                <a href="<?= getBasePath() ?>/booking" 
                   class="inline-flex items-center justify-center px-8 py-4 bg-salon-gold text-black font-semibold rounded-lg hover:bg-gold-light transition-all hover:scale-105">
                    Book Appointment
                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
                <a href="#services" 
                   class="inline-flex items-center justify-center px-8 py-4 border border-salon-gold text-salon-gold font-semibold rounded-lg hover:bg-salon-gold hover:text-black transition-all">
                    View Services
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Category Filter Indicator -->
<?php if ($selectedCategoryDetails): ?>
<section class="py-6 bg-salon-black border-b border-secondary-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between bg-secondary-900 rounded-lg p-4">
            <div class="flex items-center">
                <div class="w-3 h-3 bg-salon-gold rounded-full mr-3"></div>
                <div>
                    <h3 class="text-lg font-semibold text-white">
                        Showing services in: <span class="text-salon-gold"><?= htmlspecialchars($selectedCategoryDetails['name']) ?></span>
                    </h3>
                    <?php if ($selectedCategoryDetails['description']): ?>
                        <p class="text-sm text-gray-400 mt-1"><?= htmlspecialchars($selectedCategoryDetails['description']) ?></p>
                    <?php endif; ?>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <span class="text-sm text-gray-400">
                    <?= count($allServices) ?> service<?= count($allServices) !== 1 ? 's' : '' ?> found
                </span>
                <a href="<?= getBasePath() ?>/services"
                   class="inline-flex items-center px-3 py-1 bg-salon-gold text-black text-sm font-medium rounded-lg hover:bg-gold-light transition-colors">
                    <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Clear Filter
                </a>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Filter and Search Section -->
<section class="py-12 bg-secondary-900 border-b border-secondary-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-salon-black rounded-lg p-6 shadow-lg border border-secondary-700">
            <form method="GET" action="<?= getBasePath() ?>/services.php" class="space-y-6">
                <!-- Search and Category Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Search Input -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-300 mb-2">
                            Search Services
                        </label>
                        <div class="relative">
                            <input type="text"
                                   id="search"
                                   name="search"
                                   value="<?= htmlspecialchars($searchQuery) ?>"
                                   placeholder="Search by service name or description..."
                                   class="w-full pl-10 pr-4 py-3 bg-secondary-900 border border-secondary-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-300 mb-2">
                            Category
                        </label>
                        <select id="category"
                                name="category"
                                class="w-full py-3 px-4 bg-secondary-900 border border-secondary-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                            <option value="">All Categories</option>
                            <?php foreach ($activeCategories as $category): ?>
                                <option value="<?= htmlspecialchars($category['name']) ?>"
                                        <?= $selectedCategory === $category['name'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($category['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <!-- Price and Duration Filters -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Min Price -->
                    <div>
                        <label for="min_price" class="block text-sm font-medium text-gray-300 mb-2">
                            Min Price (TSH)
                        </label>
                        <input type="number"
                               id="min_price"
                               name="min_price"
                               value="<?= htmlspecialchars($minPrice) ?>"
                               min="0"
                               step="1"
                               placeholder="0"
                               class="w-full py-3 px-4 bg-secondary-900 border border-secondary-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                    </div>

                    <!-- Max Price -->
                    <div>
                        <label for="max_price" class="block text-sm font-medium text-gray-300 mb-2">
                            Max Price (TSH)
                        </label>
                        <input type="number"
                               id="max_price"
                               name="max_price"
                               value="<?= htmlspecialchars($maxPrice) ?>"
                               min="0"
                               step="1"
                               placeholder="999"
                               class="w-full py-3 px-4 bg-secondary-900 border border-secondary-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                    </div>

                    <!-- Max Duration -->
                    <div>
                        <label for="max_duration" class="block text-sm font-medium text-gray-300 mb-2">
                            Max Duration (minutes)
                        </label>
                        <input type="number"
                               id="max_duration"
                               name="max_duration"
                               value="<?= htmlspecialchars($maxDuration) ?>"
                               min="1"
                               placeholder="180"
                               class="w-full py-3 px-4 bg-secondary-900 border border-secondary-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button type="submit"
                            class="inline-flex items-center justify-center px-6 py-3 bg-salon-gold text-black font-semibold rounded-lg hover:bg-gold-light transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search Services
                    </button>

                    <?php if ($searchQuery || $selectedCategory || $minPrice || $maxPrice || $maxDuration): ?>
                        <a href="<?= getBasePath() ?>/services.php"
                           class="inline-flex items-center justify-center px-6 py-3 border border-gray-600 text-gray-300 font-semibold rounded-lg hover:bg-gray-600 hover:text-white transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Clear Filters
                        </a>
                    <?php endif; ?>
                </div>

                <!-- Results Summary -->
                <?php if ($hasFilters): ?>
                    <div class="text-center pt-4 border-t border-secondary-700">
                        <p class="text-gray-300">
                            <?php
                            $totalServices = count($allServices);
                            $totalCategories = count($servicesByCategory);
                            if ($totalServices > 0): ?>
                                Found <span class="font-semibold text-salon-gold"><?= $totalServices ?></span> service<?= $totalServices !== 1 ? 's' : '' ?>
                                <?php if ($totalCategories > 0): ?>
                                    in <span class="font-semibold text-salon-gold"><?= $totalCategories ?></span> categor<?= $totalCategories !== 1 ? 'ies' : 'y' ?>
                                <?php endif; ?>
                                <?php if ($searchQuery): ?>
                                    matching "<span class="font-semibold text-white"><?= htmlspecialchars($searchQuery) ?></span>"
                                <?php endif; ?>
                                <?php if ($selectedCategory): ?>
                                    in <span class="font-semibold text-white"><?= htmlspecialchars($selectedCategory) ?></span>
                                <?php endif; ?>
                                <?php if ($minPrice !== '' || $maxPrice !== ''): ?>
                                    <?php if ($minPrice !== '' && $maxPrice !== ''): ?>
                                        with price between <span class="font-semibold text-white"><?= formatCurrency($minPrice) ?></span> - <span class="font-semibold text-white"><?= formatCurrency($maxPrice) ?></span>
                                    <?php elseif ($minPrice !== ''): ?>
                                        with price from <span class="font-semibold text-white"><?= formatCurrency($minPrice) ?></span>
                                    <?php else: ?>
                                        with price up to <span class="font-semibold text-white"><?= formatCurrency($maxPrice) ?></span>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <?php if ($maxDuration !== ''): ?>
                                    with duration up to <span class="font-semibold text-white"><?= $maxDuration ?></span> minutes
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="text-gray-400">No services found matching your criteria</span>
                                <br>
                                <span class="text-sm text-gray-500 mt-2 block">Try adjusting your filters or search terms</span>
                            <?php endif; ?>
                        </p>
                    </div>
                <?php endif; ?>
            </form>
        </div>
    </div>
</section>

<!-- Active Filters Indicator -->
<?php if ($hasFilters && !empty($servicesByCategory)): ?>
<section class="py-4 bg-secondary-900 border-b border-secondary-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex flex-wrap items-center gap-2">
                <span class="text-sm text-gray-400">Active filters:</span>
                <?php if ($searchQuery): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-salon-gold/20 text-salon-gold border border-salon-gold/30">
                        Search: "<?= htmlspecialchars($searchQuery) ?>"
                    </span>
                <?php endif; ?>
                <?php if ($selectedCategory): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400 border border-blue-500/30">
                        Category: <?= htmlspecialchars($selectedCategory) ?>
                    </span>
                <?php endif; ?>
                <?php if ($minPrice !== ''): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400 border border-green-500/30">
                        Min: <?= formatCurrency($minPrice) ?>
                    </span>
                <?php endif; ?>
                <?php if ($maxPrice !== ''): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400 border border-green-500/30">
                        Max: <?= formatCurrency($maxPrice) ?>
                    </span>
                <?php endif; ?>
                <?php if ($maxDuration !== ''): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-500/20 text-purple-400 border border-purple-500/30">
                        Duration: ≤<?= $maxDuration ?>min
                    </span>
                <?php endif; ?>
            </div>
            <a href="<?= getBasePath() ?>/services.php"
               class="inline-flex items-center text-sm text-gray-400 hover:text-salon-gold transition-colors">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Clear all
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Services Section -->
<section id="services" class="py-20 bg-salon-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <?php if (empty($servicesByCategory)): ?>
            <!-- No Services Available -->
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <?php if ($hasFilters): ?>
                        <svg class="w-12 h-12 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    <?php else: ?>
                        <svg class="w-12 h-12 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2z"></path>
                        </svg>
                    <?php endif; ?>
                </div>

                <?php if ($hasFilters): ?>
                    <h2 class="text-2xl font-bold text-white mb-4">No Services Found</h2>
                    <p class="text-gray-300 mb-6">No services match your current search criteria.</p>
                    <div class="space-y-4 mb-8">
                        <p class="text-gray-400 text-sm">Try:</p>
                        <ul class="text-gray-400 text-sm space-y-2 max-w-md mx-auto">
                            <li>• Adjusting your price range</li>
                            <li>• Removing some filters</li>
                            <li>• Using different search terms</li>
                            <li>• Selecting a different category</li>
                        </ul>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="<?= getBasePath() ?>/services.php"
                           class="inline-flex items-center px-6 py-3 bg-salon-gold text-black font-semibold rounded-lg hover:bg-gold-light transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Clear All Filters
                        </a>
                        <a href="<?= getBasePath() ?>/contact"
                           class="inline-flex items-center px-6 py-3 border border-salon-gold text-salon-gold font-semibold rounded-lg hover:bg-salon-gold hover:text-black transition-colors">
                            Contact Us for Help
                        </a>
                    </div>
                <?php else: ?>
                    <h2 class="text-2xl font-bold text-white mb-4">No Services Available</h2>
                    <p class="text-gray-300 mb-8">We're currently updating our services. Please check back soon!</p>
                    <a href="<?= getBasePath() ?>/contact" class="inline-flex items-center px-6 py-3 bg-salon-gold text-black font-semibold rounded-lg hover:bg-gold-light transition-colors">
                        Contact Us for Information
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <?php foreach ($servicesByCategory as $category => $categoryServices): ?>
                <div class="mb-20 last:mb-0">
                    <!-- Enhanced Category Header -->
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-white mb-3">
                            <?= htmlspecialchars($category) ?>
                        </h2>
                        <?php if (isset($categoryDescriptions[$category]) && $categoryDescriptions[$category]): ?>
                            <p class="text-lg text-gray-300 mb-6 max-w-2xl mx-auto">
                                <?= htmlspecialchars($categoryDescriptions[$category]) ?>
                            </p>
                        <?php endif; ?>
                        <div class="w-24 h-1 bg-salon-gold mx-auto"></div>
                    </div>

                    <!-- Improved Services Grid with Compact Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <?php foreach ($categoryServices as $service): ?>
                            <div class="bg-secondary-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] group border border-secondary-700 hover:border-salon-gold/30">
                                <!-- Compact Service Image with Lazy Loading -->
                                <div class="relative bg-secondary-700 overflow-hidden">
                                    <?php if ($service['image']): ?>
                                        <?php
                                        // Check if image is a URL or uploaded file
                                        $imageSrc = filter_var($service['image'], FILTER_VALIDATE_URL)
                                            ? $service['image']
                                            : getBasePath() . '/uploads/' . $service['image'];
                                        ?>
                                        <!-- Lazy Loading Placeholder -->
                                        <div class="lazy-placeholder w-full h-32 bg-gradient-to-br from-salon-gold/10 to-secondary-700 flex items-center justify-center">
                                            <div class="text-center">
                                                <div class="animate-pulse">
                                                    <div class="w-8 h-8 bg-salon-gold/20 rounded-full mx-auto mb-1"></div>
                                                    <div class="h-1 bg-salon-gold/20 rounded w-12 mx-auto"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Actual Image (lazy loaded) -->
                                        <img data-src="<?= htmlspecialchars($imageSrc) ?>"
                                             alt="<?= htmlspecialchars($service['name']) ?>"
                                             class="lazy-image w-full h-32 object-cover group-hover:scale-110 transition-transform duration-300 opacity-0"
                                             loading="lazy">
                                    <?php else: ?>
                                        <div class="w-full h-32 bg-gradient-to-br from-salon-gold/20 to-secondary-700 flex items-center justify-center">
                                            <svg class="w-10 h-10 text-salon-gold/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2z"></path>
                                            </svg>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Price Badge -->
                                    <div class="absolute top-2 right-2 bg-salon-gold text-black px-2 py-1 rounded-full text-sm font-bold">
                                        <?= formatCurrency($service['price']) ?>
                                    </div>
                                </div>

                                <!-- Compact Service Content -->
                                <div class="p-4">
                                    <div class="mb-3">
                                        <h3 class="text-lg font-semibold text-white group-hover:text-salon-gold transition-colors mb-1 line-clamp-1">
                                            <?= htmlspecialchars($service['name']) ?>
                                        </h3>
                                        <?php if ($service['description']): ?>
                                            <p class="text-gray-400 text-sm line-clamp-2 mb-3">
                                                <?= htmlspecialchars($service['description']) ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Service Info -->
                                    <div class="flex items-center justify-between mb-4 text-sm">
                                        <div class="flex items-center text-gray-400">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span><?= $service['duration'] ?> min</span>
                                        </div>
                                        <div class="flex items-center text-salon-gold">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                            <span>Premium</span>
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex gap-2">
                                        <button onclick="bookService('<?= $service['id'] ?>', '<?= htmlspecialchars($service['name']) ?>', <?= $service['price'] ?>, <?= $service['duration'] ?>)"
                                                class="flex-1 bg-salon-gold text-black py-2 px-3 rounded-lg text-sm font-semibold hover:bg-gold-light transition-colors">
                                            Book Now
                                        </button>
                                        <button onclick="viewServiceDetails('<?= $service['id'] ?>')"
                                                class="px-3 py-2 border border-salon-gold text-salon-gold rounded-lg hover:bg-salon-gold hover:text-black transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </button>

                                        <!-- Wishlist Heart Icon -->
                                        <button onclick="toggleWishlist('service', '<?= $service['id'] ?>', this)"
                                                class="wishlist-btn px-3 py-2 border-2 border-gray-500 text-gray-300 rounded-lg hover:border-red-400 hover:text-red-400 transition-all duration-300"
                                                data-item-type="service"
                                                data-item-id="<?= $service['id'] ?>"
                                                title="Add to wishlist">
                                            <i class="far fa-heart text-base"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-20 bg-salon-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
                Why Choose <span class="text-salon-gold">Flix Salon & SPA</span>
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Experience the difference with our premium services and expert professionals
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center group">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Expert Professionals</h3>
                <p class="text-gray-300">Certified and experienced stylists and therapists</p>
            </div>

            <div class="text-center group">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Premium Products</h3>
                <p class="text-gray-300">High-quality, professional-grade products only</p>
            </div>

            <div class="text-center group">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Flexible Scheduling</h3>
                <p class="text-gray-300">Easy online booking with flexible time slots</p>
            </div>

            <div class="text-center group">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Loyalty Rewards</h3>
                <p class="text-gray-300">Earn points with every visit and get exclusive benefits</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-salon-gold to-amber-400">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-black mb-4">
            Ready to Transform Your Look?
        </h2>
        <p class="text-xl text-black/80 mb-8 max-w-2xl mx-auto">
            Book your appointment today and experience the luxury of Flix Salon & SPA
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="<?= getBasePath() ?>/booking" 
               class="inline-flex items-center justify-center px-8 py-4 bg-black text-salon-gold font-semibold rounded-lg hover:bg-secondary-800 transition-all hover:scale-105">
                Book Appointment Now
                <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
            </a>
            <a href="<?= getBasePath() ?>/contact" 
               class="inline-flex items-center justify-center px-8 py-4 border-2 border-black text-black font-semibold rounded-lg hover:bg-black hover:text-salon-gold transition-all">
                Contact Us
            </a>
        </div>
    </div>
</section>

<!-- Service Details Modal -->
<div id="serviceModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-secondary-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 id="modalServiceName" class="text-2xl font-bold text-white"></h2>
                <button onclick="closeServiceModal()" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div id="modalServiceContent">
                <!-- Service details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function bookService(serviceId, serviceName, price, duration) {
    // Check if user is logged in
    <?php if (isLoggedIn()): ?>
        // Redirect to booking page with service pre-selected
        window.location.href = `<?= getBasePath() ?>/customer/book/?service=${serviceId}`;
    <?php else: ?>
        // Store service selection in sessionStorage for persistence across authentication
        const serviceSelection = {
            serviceId: serviceId,
            serviceName: serviceName,
            price: price,
            duration: duration,
            timestamp: Date.now()
        };
        sessionStorage.setItem('pendingServiceBooking', JSON.stringify(serviceSelection));

        // Redirect to login page with return URL
        if (confirm('Please log in to book an appointment. Would you like to log in now?')) {
            window.location.href = `<?= getBasePath() ?>/auth/login.php?redirect=${encodeURIComponent('/customer/book/?service=' + serviceId)}`;
        }
    <?php endif; ?>
}

function viewServiceDetails(serviceId) {
    // Show loading state
    document.getElementById('modalServiceName').textContent = 'Loading...';
    document.getElementById('modalServiceContent').innerHTML = `
        <div class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-salon-gold"></div>
        </div>
    `;
    document.getElementById('serviceModal').classList.remove('hidden');

    // Fetch service details from public API
    fetch(`<?= getBasePath() ?>/api/services.php?id=${serviceId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(service => {
            document.getElementById('modalServiceName').textContent = service.name;
            document.getElementById('modalServiceContent').innerHTML = `
                <div class="space-y-4">
                    ${service.image ? `<img src="${service.image}" alt="${escapeHtml(service.name)}" class="w-full h-64 object-cover rounded-lg">` : ''}
                    <div class="flex justify-between items-center">
                        <span class="text-3xl font-bold text-salon-gold">${formatCurrency(service.price)}</span>
                        <span class="text-gray-300">${service.duration} minutes</span>
                    </div>
                    ${service.description ? `<p class="text-gray-300">${escapeHtml(service.description)}</p>` : '<p class="text-gray-400 italic">No description available.</p>'}
                    ${service.category ? `<div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-salon-gold/20 text-salon-gold">
                        ${escapeHtml(service.category)}
                    </div>` : ''}
                    <div class="flex gap-4 pt-4">
                        <button onclick="bookService('${service.id}', '${escapeHtml(service.name)}', ${service.price}, ${service.duration})"
                                class="flex-1 bg-salon-gold text-black py-3 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                            Book This Service
                        </button>
                        <button onclick="closeServiceModal()"
                                class="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors">
                            Close
                        </button>
                    </div>
                </div>
            `;
        })
        .catch(error => {
            console.error('Error fetching service details:', error);
            document.getElementById('modalServiceName').textContent = 'Error Loading Service';
            document.getElementById('modalServiceContent').innerHTML = `
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-white mb-2">Failed to Load Service Details</h3>
                    <p class="text-gray-300 mb-4">We couldn't load the service information. Please try again.</p>
                    <button onclick="viewServiceDetails('${serviceId}')"
                            class="bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors mr-2">
                        Retry
                    </button>
                    <button onclick="closeServiceModal()"
                            class="bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                        Close
                    </button>
                </div>
            `;
        });
}

function closeServiceModal() {
    document.getElementById('serviceModal').classList.add('hidden');
}

function formatCurrency(amount) {
    return 'TSH ' + parseInt(amount).toLocaleString();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeServiceModal();
    }
});

// Close modal on backdrop click
document.getElementById('serviceModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeServiceModal();
    }
});

// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Initialize wishlist states when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeWishlistStates();
});

// CSRF Token
const csrfToken = '<?= $_SESSION['csrf_token'] ?? '' ?>';

// Initialize wishlist heart states
function initializeWishlistStates() {
    const wishlistBtns = document.querySelectorAll('.wishlist-btn');

    wishlistBtns.forEach(btn => {
        const itemType = btn.dataset.itemType;
        const itemId = btn.dataset.itemId;

        // Check if item is in wishlist
        fetch(`<?= getBasePath() ?>/api/wishlist.php?action=check&item_type=${itemType}&item_id=${itemId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.inWishlist) {
                    updateHeartIcon(btn, true);
                }
            })
            .catch(error => console.error('Error checking wishlist:', error));
    });
}

// Toggle wishlist item
function toggleWishlist(itemType, itemId, button) {
    // Prevent event bubbling
    event.stopPropagation();

    // Add loading state
    const icon = button.querySelector('i');
    const originalClass = icon.className;
    icon.className = 'fas fa-spinner fa-spin text-white';

    fetch('<?= getBasePath() ?>/api/wishlist.php?action=toggle', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `item_type=${itemType}&item_id=${itemId}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateHeartIcon(button, data.inWishlist);
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        // Restore original icon if there was an error
        if (icon.className.includes('fa-spinner')) {
            icon.className = originalClass;
        }
    });
}

// Update heart icon appearance
function updateHeartIcon(button, inWishlist) {
    const icon = button.querySelector('i');

    if (inWishlist) {
        icon.className = 'fas fa-heart text-base';
        button.classList.remove('border-gray-500', 'text-gray-300', 'hover:border-red-400', 'hover:text-red-400');
        button.classList.add('border-red-400', 'text-red-400', 'bg-red-50', 'animate-pulse');
        setTimeout(() => button.classList.remove('animate-pulse'), 600);
    } else {
        icon.className = 'far fa-heart text-base';
        button.classList.remove('border-red-400', 'text-red-400', 'bg-red-50');
        button.classList.add('border-gray-500', 'text-gray-300', 'hover:border-red-400', 'hover:text-red-400');
    }
}

// Show toast notification with smooth animations
function showToast(message, type = 'success') {
    // Remove any existing toast first
    const existingToast = document.getElementById('wishlist-toast');
    if (existingToast) {
        existingToast.style.transform = 'translateX(100%)';
        existingToast.style.opacity = '0';
        setTimeout(() => existingToast.remove(), 200);
    }

    // Create new toast
    const toast = document.createElement('div');
    toast.id = 'wishlist-toast';

    // Set base styles for smooth animation
    toast.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 9999;
        background-color: #1e293b;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.75rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: translateX(100%) scale(0.95);
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        max-width: 20rem;
        backdrop-filter: blur(8px);
    `;

    // Set content and style based on type
    if (type === 'success') {
        toast.style.borderLeft = '4px solid #10b981';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-check-circle" style="color: #10b981; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    } else {
        toast.style.borderLeft = '4px solid #ef4444';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-exclamation-circle" style="color: #ef4444; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    }

    // Add to DOM
    document.body.appendChild(toast);

    // Trigger smooth slide-in animation
    requestAnimationFrame(() => {
        requestAnimationFrame(() => {
            toast.style.transform = 'translateX(0) scale(1)';
            toast.style.opacity = '1';
        });
    });

    // Hide toast after 2 seconds with smooth slide-out
    setTimeout(() => {
        toast.style.transform = 'translateX(100%) scale(0.95)';
        toast.style.opacity = '0';

        // Remove from DOM after animation completes
        setTimeout(() => {
            if (toast && toast.parentNode) {
                toast.remove();
            }
        }, 400);
    }, 2000);
}

// Lazy Loading Implementation for Services
class ServiceLazyLoader {
    constructor() {
        this.imageObserver = null;
        this.init();
    }

    init() {
        // Check if Intersection Observer is supported
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            this.observeImages();
        } else {
            // Fallback for browsers without Intersection Observer
            this.loadAllImages();
        }
    }

    observeImages() {
        const lazyImages = document.querySelectorAll('.lazy-image');
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }

    loadImage(img) {
        const placeholder = img.previousElementSibling;

        // Create a new image to preload
        const imageLoader = new Image();

        imageLoader.onload = () => {
            // Image loaded successfully
            img.src = img.dataset.src;
            img.classList.remove('opacity-0');
            img.classList.add('opacity-100');

            // Hide placeholder with fade effect
            if (placeholder && placeholder.classList.contains('lazy-placeholder')) {
                placeholder.style.transition = 'opacity 0.3s ease-out';
                placeholder.style.opacity = '0';
                setTimeout(() => {
                    placeholder.style.display = 'none';
                }, 300);
            }

            // Add loaded class for any additional styling
            img.classList.add('lazy-loaded');
        };

        imageLoader.onerror = () => {
            // Handle image load error
            img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=';
            img.classList.remove('opacity-0');
            img.classList.add('opacity-100');

            if (placeholder && placeholder.classList.contains('lazy-placeholder')) {
                placeholder.style.display = 'none';
            }
        };

        // Start loading the image
        imageLoader.src = img.dataset.src;
    }

    loadAllImages() {
        // Fallback: load all images immediately
        const lazyImages = document.querySelectorAll('.lazy-image');
        lazyImages.forEach(img => {
            this.loadImage(img);
        });
    }
}

// Initialize lazy loading when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new ServiceLazyLoader();

    // Add lazy loading styles
    const style = document.createElement('style');
    style.textContent = `
        .lazy-image {
            transition: opacity 0.3s ease-in-out;
        }

        .lazy-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .lazy-loaded {
            z-index: 2;
        }

        /* Pulse animation for loading placeholder */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
    `;
    document.head.appendChild(style);
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
