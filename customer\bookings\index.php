<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';
require_once __DIR__ . '/../../includes/booking_expiration.php';

// Run expiration check if needed
runExpirationCheckIfNeeded();

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'cancel_booking':
                    $bookingId = $_POST['booking_id'];
                    cancelCustomerBooking($_SESSION['user_id'], $bookingId);
                    $message = 'Booking cancelled successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get filters
$statusFilter = $_GET['status'] ?? 'all';
$dateFilter = $_GET['date_range'] ?? 'all';

// Get customer data
$customerId = $_SESSION['user_id'];
$profile = getCustomerProfile($customerId);

// Get bookings with filters
global $database;
$conditions = ["b.user_id = ?"];
$params = [$customerId];

if ($statusFilter !== 'all') {
    $conditions[] = "b.status = ?";
    $params[] = $statusFilter;
}

if ($dateFilter !== 'all') {
    switch ($dateFilter) {
        case 'upcoming':
            $conditions[] = "b.date >= CURDATE()";
            break;
        case 'past':
            $conditions[] = "b.date < CURDATE()";
            break;
        case 'this_month':
            $conditions[] = "YEAR(b.date) = YEAR(CURDATE()) AND MONTH(b.date) = MONTH(CURDATE())";
            break;
        case 'last_month':
            $conditions[] = "YEAR(b.date) = YEAR(CURDATE() - INTERVAL 1 MONTH) AND MONTH(b.date) = MONTH(CURDATE() - INTERVAL 1 MONTH)";
            break;
    }
}

$whereClause = 'WHERE ' . implode(' AND ', $conditions);

$bookings = $database->fetchAll("
    SELECT
        b.*,
        s.name as service_name,
        s.duration as service_duration,
        s.price as service_price,
        p.name as package_name,
        p.price as package_price,
        st.name as staff_name,
        st.phone as staff_phone,
        pay.id as payment_id,
        pay.status as payment_status,
        pay.payment_gateway,
        pay.payment_reference
    FROM bookings b
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN packages p ON b.package_id = p.id
    LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
    LEFT JOIN payments pay ON b.id = pay.booking_id
    $whereClause
    ORDER BY b.date DESC, b.start_time DESC
", $params);

// Calculate duration for package bookings
foreach ($bookings as $index => $booking) {
    if (!empty($booking['package_id']) && empty($booking['service_duration'])) {
        // Get total duration for package
        $packageServices = $database->fetchAll("
            SELECT s.duration
            FROM services s
            INNER JOIN package_services ps ON s.id = ps.service_id
            WHERE ps.package_id = ?
        ", [$booking['package_id']]);

        $bookings[$index]['service_duration'] = array_sum(array_column($packageServices, 'duration'));
    }
}

$pageTitle = "My Bookings";

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<!-- Page Header -->
<div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl shadow-xl mb-8 hover-lift">
    <div class="px-6 sm:px-8 lg:px-10">
        <div class="py-8 md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div>
                        <div class="flex items-center">
                            <h1 class="text-3xl font-bold leading-7 text-white sm:leading-9 sm:truncate font-serif">
                                My <span class="text-salon-gold">Bookings</span>
                            </h1>
                        </div>
                        <dl class="mt-3 flex flex-col sm:mt-2 sm:flex-row sm:flex-wrap">
                            <dt class="sr-only">Page description</dt>
                            <dd class="flex items-center text-lg text-gray-300 font-medium sm:mr-6">
                                View and manage your appointment history
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="mt-6 flex space-x-4 md:mt-0 md:ml-4">
                <?php if (PAYMENT_ENABLED): ?>
                <a href="<?= getBasePath() ?>/customer/payments" class="inline-flex items-center px-6 py-3 border border-secondary-700 shadow-lg text-sm font-semibold rounded-xl text-white bg-secondary-800/50 hover:bg-secondary-700 hover:border-salon-gold/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold transition-all duration-300 hover-lift">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                    </svg>
                    Payment Center
                </a>
                <?php endif; ?>
                <a href="<?= getBasePath() ?>/customer/book" class="inline-flex items-center px-6 py-3 border border-transparent shadow-lg text-sm font-semibold rounded-xl text-black bg-salon-gold hover:bg-gold-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold transition-all duration-300 hover:scale-105">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Book New Appointment
                </a>
            </div>
        </div>
    </div>
</div>

            <!-- Message Display -->
            <?php if ($message): ?>
                <div class="mb-8 p-6 rounded-xl <?= $messageType === 'success' ? 'bg-salon-gold/10 border border-salon-gold text-salon-gold' : 'bg-red-500/10 border border-red-500 text-red-400' ?> backdrop-blur-sm">
                    <div class="flex items-center">
                        <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-triangle' ?> mr-3 text-xl"></i>
                        <?= htmlspecialchars($message) ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-8 mb-8 hover-lift">
                <form method="GET" class="flex flex-col sm:flex-row gap-6">
                    <div class="flex-1">
                        <label for="status" class="block text-sm font-semibold text-salon-gold mb-3">Status</label>
                        <select id="status" name="status"
                                class="w-full px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                            <option value="all" <?= $statusFilter === 'all' ? 'selected' : '' ?>>All Status</option>
                            <option value="PENDING" <?= $statusFilter === 'PENDING' ? 'selected' : '' ?>>Pending</option>
                            <option value="CONFIRMED" <?= $statusFilter === 'CONFIRMED' ? 'selected' : '' ?>>Confirmed</option>
                            <option value="IN_PROGRESS" <?= $statusFilter === 'IN_PROGRESS' ? 'selected' : '' ?>>In Progress</option>
                            <option value="COMPLETED" <?= $statusFilter === 'COMPLETED' ? 'selected' : '' ?>>Completed</option>
                            <option value="CANCELLED" <?= $statusFilter === 'CANCELLED' ? 'selected' : '' ?>>Cancelled</option>
                            <option value="NO_SHOW" <?= $statusFilter === 'NO_SHOW' ? 'selected' : '' ?>>No Show</option>
                            <option value="EXPIRED" <?= $statusFilter === 'EXPIRED' ? 'selected' : '' ?>>Expired</option>
                        </select>
                    </div>
                    <div class="flex-1">
                        <label for="date_range" class="block text-sm font-semibold text-salon-gold mb-3">Date Range</label>
                        <select id="date_range" name="date_range"
                                class="w-full px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                            <option value="all" <?= $dateFilter === 'all' ? 'selected' : '' ?>>All Time</option>
                            <option value="upcoming" <?= $dateFilter === 'upcoming' ? 'selected' : '' ?>>Upcoming</option>
                            <option value="past" <?= $dateFilter === 'past' ? 'selected' : '' ?>>Past</option>
                            <option value="this_month" <?= $dateFilter === 'this_month' ? 'selected' : '' ?>>This Month</option>
                            <option value="last_month" <?= $dateFilter === 'last_month' ? 'selected' : '' ?>>Last Month</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="px-8 py-3 bg-salon-gold hover:bg-gold-light text-black rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                    </div>
                </form>
            </div>

            <!-- Bookings List -->
            <div class="space-y-6">
                <?php if (empty($bookings)): ?>
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-16 text-center hover-lift">
                        <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-calendar-times text-4xl text-salon-gold"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-3">No Bookings Found</h3>
                        <p class="text-gray-400 mb-8 text-lg">You haven't made any appointments yet or no bookings match your filters</p>
                        <a href="<?= getBasePath() ?>/customer/book" class="bg-salon-gold hover:bg-gold-light text-black px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg">
                            <i class="fas fa-plus mr-2"></i>Book Your First Appointment
                        </a>
                    </div>
                <?php else: ?>
                    <?php foreach ($bookings as $booking): ?>
                        <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-8 hover-lift">
                            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                                <div class="flex-1">
                                    <div class="flex items-start gap-6">
                                        <div class="w-16 h-16 rounded-full bg-salon-gold flex items-center justify-center">
                                            <?php if (!empty($booking['package_name'])): ?>
                                                <i class="fas fa-box text-black text-xl"></i>
                                            <?php else: ?>
                                                <i class="fas fa-cut text-black text-xl"></i>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-center gap-4 mb-3">
                                                <h3 class="text-xl font-bold text-white">
                                                    <?php if (!empty($booking['service_name'])): ?>
                                                        <?= htmlspecialchars($booking['service_name']) ?>
                                                    <?php elseif (!empty($booking['package_name'])): ?>
                                                        <?= htmlspecialchars($booking['package_name']) ?>
                                                        <span class="text-xs bg-salon-gold text-black px-3 py-1 rounded-full ml-3 font-semibold">PACKAGE</span>
                                                    <?php else: ?>
                                                        Unknown Service
                                                    <?php endif; ?>
                                                </h3>
                                                <?php $statusInfo = getBookingStatusInfo($booking['status']); ?>
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold <?= $statusInfo['class'] ?>">
                                                    <i class="<?= $statusInfo['icon'] ?> mr-2"></i>
                                                    <?= $statusInfo['label'] ?>
                                                </span>
                                            </div>
                                            
                                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                                <div class="bg-secondary-800/50 rounded-lg p-4">
                                                    <p class="text-salon-gold text-sm font-semibold mb-1">Staff Member</p>
                                                    <p class="text-white font-semibold"><?= htmlspecialchars($booking['staff_name']) ?></p>
                                                </div>
                                                <div class="bg-secondary-800/50 rounded-lg p-4">
                                                    <p class="text-salon-gold text-sm font-semibold mb-1">Date & Time</p>
                                                    <p class="text-white font-semibold">
                                                        <?= date('M j, Y', strtotime($booking['date'])) ?><br>
                                                        <?= date('g:i A', strtotime($booking['start_time'])) ?>
                                                    </p>
                                                </div>
                                                <div class="bg-secondary-800/50 rounded-lg p-4">
                                                    <p class="text-salon-gold text-sm font-semibold mb-1">Duration & Price</p>
                                                    <p class="text-white font-semibold">
                                                        <?= $booking['service_duration'] ?> min<br>
                                                        <?= CURRENCY_SYMBOL ?> <?= number_format($booking['total_amount']) ?>
                                                    </p>
                                                </div>
                                                <div class="bg-secondary-800/50 rounded-lg p-4">
                                                    <p class="text-salon-gold text-sm font-semibold mb-1">Points</p>
                                                    <p class="text-white font-semibold">
                                                        <?php if ($booking['points_used'] > 0): ?>
                                                            <span class="text-red-400">-<?= $booking['points_used'] ?></span><br>
                                                        <?php endif; ?>
                                                        <?php if ($booking['points_earned'] > 0): ?>
                                                            <span class="text-salon-gold">+<?= $booking['points_earned'] ?></span>
                                                        <?php endif; ?>
                                                    </p>
                                                </div>
                                            </div>

                                            <?php if (!empty($booking['package_name'])): ?>
                                                <?php
                                                // Get package services
                                                $packageServices = $database->fetchAll("
                                                    SELECT s.name
                                                    FROM services s
                                                    INNER JOIN package_services ps ON s.id = ps.service_id
                                                    WHERE ps.package_id = ?
                                                    ORDER BY s.name
                                                ", [$booking['package_id']]);
                                                ?>
                                                <div class="mt-3">
                                                    <p class="text-gray-400 text-sm">Package includes:</p>
                                                    <div class="flex flex-wrap gap-1 mt-1">
                                                        <?php foreach ($packageServices as $service): ?>
                                                            <span class="inline-block px-2 py-1 bg-secondary-700 text-xs rounded text-gray-300">
                                                                <?= htmlspecialchars($service['name']) ?>
                                                            </span>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>

                                            <?php if ($booking['notes']): ?>
                                                <div class="mt-3">
                                                    <p class="text-gray-400 text-sm">Notes:</p>
                                                    <p class="text-gray-300 text-sm"><?= htmlspecialchars($booking['notes']) ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-4 lg:mt-0 lg:ml-6">
                                    <div class="flex flex-wrap gap-2">
                                        <?php if ($booking['status'] === 'PENDING' || $booking['status'] === 'CONFIRMED'): ?>
                                            <?php
                                            // Check if booking is within cancellation window (24 hours)
                                            $bookingDateTime = new DateTime($booking['date'] . ' ' . $booking['start_time']);
                                            $now = new DateTime();
                                            $hoursDiff = ($bookingDateTime->getTimestamp() - $now->getTimestamp()) / 3600;
                                            ?>

                                            <?php if ($hoursDiff >= 24): ?>
                                                <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to cancel this appointment?')">
                                                    <input type="hidden" name="action" value="cancel_booking">
                                                    <input type="hidden" name="booking_id" value="<?= $booking['id'] ?>">
                                                    <button type="submit" class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
                                                        <i class="fas fa-times mr-1"></i>Cancel
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <span class="px-3 py-1 bg-gray-600 text-gray-300 text-sm rounded cursor-not-allowed">
                                                    <i class="fas fa-clock mr-1"></i>Cannot Cancel
                                                </span>
                                            <?php endif; ?>
                                        <?php elseif ($booking['status'] === 'EXPIRED'): ?>
                                            <span class="px-3 py-1 bg-orange-600 text-white text-sm rounded">
                                                <i class="fas fa-hourglass-end mr-1"></i>Booking Expired
                                            </span>
                                        <?php endif; ?>
                                        
                                        <?php if ($booking['staff_phone']): ?>
                                            <a href="tel:<?= $booking['staff_phone'] ?>" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors">
                                                <i class="fas fa-phone mr-1"></i>Call Staff
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php if (PAYMENT_ENABLED && ($booking['status'] === 'CONFIRMED' || $booking['status'] === 'COMPLETED')): ?>
                                            <?php if (!$booking['payment_status'] || $booking['payment_status'] === 'FAILED'): ?>
                                                <a href="<?= getBasePath() ?>/customer/payments" class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded transition-colors">
                                                    <i class="fas fa-credit-card mr-1"></i>Pay Now
                                                </a>
                                            <?php elseif ($booking['payment_status'] === 'PENDING'): ?>
                                                <span class="px-3 py-1 bg-yellow-600 text-white text-sm rounded">
                                                    <i class="fas fa-clock mr-1"></i>Payment Pending
                                                </span>
                                            <?php elseif ($booking['payment_status'] === 'COMPLETED'): ?>
                                                <span class="px-3 py-1 bg-green-600 text-white text-sm rounded">
                                                    <i class="fas fa-check mr-1"></i>Paid
                                                </span>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <?php if ($booking['status'] === 'COMPLETED'): ?>
                                            <button onclick="openReviewModal('<?= $booking['id'] ?>')" class="px-3 py-1 bg-salon-gold hover:bg-yellow-500 text-black text-sm rounded transition-colors">
                                                <i class="fas fa-star mr-1"></i>Review
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

<!-- Review Modal -->
<div id="reviewModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-secondary-800 rounded-lg max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-white">Leave a Review</h3>
                        <button onclick="closeReviewModal()" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form id="reviewForm" method="POST">
                        <input type="hidden" name="action" value="submit_review">
                        <input type="hidden" id="reviewBookingId" name="booking_id" value="">

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-300 mb-2">Rating</label>
                            <div class="flex gap-1" id="starRating">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <button type="button" class="star text-2xl text-gray-400 hover:text-yellow-400 transition-colors" data-rating="<?= $i ?>">
                                        <i class="fas fa-star"></i>
                                    </button>
                                <?php endfor; ?>
                            </div>
                            <input type="hidden" id="rating" name="rating" value="" required>
                        </div>

                        <div class="mb-4">
                            <label for="review_comment" class="block text-sm font-medium text-gray-300 mb-2">Comment</label>
                            <textarea id="review_comment" name="comment" rows="4" 
                                      class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                      placeholder="Share your experience..."></textarea>
                        </div>

                        <div class="flex items-center justify-end space-x-3">
                            <button type="button" onclick="closeReviewModal()" 
                                    class="px-4 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                                Cancel
                            </button>
                            <button type="submit" 
                                    class="px-4 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                                Submit Review
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openReviewModal(bookingId) {
            document.getElementById('reviewBookingId').value = bookingId;
            document.getElementById('reviewModal').classList.remove('hidden');
        }

        function closeReviewModal() {
            document.getElementById('reviewModal').classList.add('hidden');
            // Reset form
            document.getElementById('reviewForm').reset();
            document.getElementById('rating').value = '';
            document.querySelectorAll('.star').forEach(star => {
                star.classList.remove('text-yellow-400');
                star.classList.add('text-gray-400');
            });
        }

        // Star rating functionality
        document.querySelectorAll('.star').forEach(star => {
            star.addEventListener('click', function() {
                const rating = parseInt(this.dataset.rating);
                document.getElementById('rating').value = rating;
                
                // Update star display
                document.querySelectorAll('.star').forEach((s, index) => {
                    if (index < rating) {
                        s.classList.remove('text-gray-400');
                        s.classList.add('text-yellow-400');
                    } else {
                        s.classList.remove('text-yellow-400');
                        s.classList.add('text-gray-400');
                    }
                });
            });
        });

        // Close modal when clicking outside
        document.getElementById('reviewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeReviewModal();
            }
        });
    </script>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>
