<?php
/**
 * Database Reset Script
 * Flix Salonce - PHP Version
 * 
 * WARNING: This will delete all data in the database!
 * Use only for development/testing purposes.
 */

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Flix Salonce - Database Reset</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <style>
        body { background: #0f172a; color: #ffffff; font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class='min-h-screen bg-gray-900 text-white'>
    <div class='max-w-4xl mx-auto py-12 px-6'>
        <div class='text-center mb-8'>
            <h1 class='text-4xl font-bold text-red-400 mb-2'>⚠️ Database Reset</h1>
            <h2 class='text-2xl font-semibold mb-4'>Flix Salonce</h2>
        </div>";

// Check if confirmation is provided
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "
        <div class='bg-red-900 border border-red-700 rounded-lg p-6 mb-6'>
            <h3 class='text-xl font-semibold text-red-400 mb-4'>⚠️ WARNING</h3>
            <p class='mb-4'>This action will permanently delete ALL data in the database including:</p>
            <ul class='list-disc list-inside space-y-1 mb-6'>
                <li>All user accounts and profiles</li>
                <li>All bookings and appointments</li>
                <li>All services and packages</li>
                <li>All staff information</li>
                <li>All gallery images and content</li>
                <li>All settings and configurations</li>
            </ul>
            <p class='mb-6 text-red-300 font-semibold'>This action cannot be undone!</p>
            
            <div class='flex space-x-4'>
                <a href='?confirm=yes' class='bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg font-semibold transition-colors'>
                    Yes, Reset Database
                </a>
                <a href='setup.php' class='bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-semibold transition-colors'>
                    Cancel
                </a>
            </div>
        </div>";
} else {
    // Perform database reset
    echo "
        <div class='bg-gray-800 rounded-lg p-6 mb-6'>
            <h3 class='text-xl font-semibold mb-4'>Reset Progress</h3>
            <div class='space-y-2'>";
    
    try {
        require_once __DIR__ . '/config/database.php';
        
        echo "<div class='flex items-center space-x-2'>
                <div class='w-4 h-4 bg-blue-500 rounded-full animate-pulse'></div>
                <span>Connecting to database...</span>
              </div>";
        
        echo "<div class='flex items-center space-x-2'>
                <div class='w-4 h-4 bg-green-500 rounded-full'></div>
                <span>✓ Database connection successful</span>
              </div>";
        
        // Get all table names
        echo "<div class='flex items-center space-x-2'>
                <div class='w-4 h-4 bg-blue-500 rounded-full animate-pulse'></div>
                <span>Dropping existing tables...</span>
              </div>";
        
        // Disable foreign key checks
        $database->query("SET FOREIGN_KEY_CHECKS = 0");
        
        // Get all tables in the database
        $tables = $database->fetchAll("SHOW TABLES");
        $tableCount = 0;
        
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            $database->query("DROP TABLE IF EXISTS `$tableName`");
            $tableCount++;
        }
        
        // Re-enable foreign key checks
        $database->query("SET FOREIGN_KEY_CHECKS = 1");
        
        echo "<div class='flex items-center space-x-2'>
                <div class='w-4 h-4 bg-green-500 rounded-full'></div>
                <span>✓ Dropped $tableCount tables</span>
              </div>";
        
        echo "<div class='flex items-center space-x-2'>
                <div class='w-4 h-4 bg-green-500 rounded-full'></div>
                <span>✓ Database reset completed successfully!</span>
              </div>";
        
        echo "</div>
            </div>
            
            <div class='bg-green-900 border border-green-700 rounded-lg p-6 mb-6'>
                <h3 class='text-xl font-semibold text-green-400 mb-4'>Reset Complete!</h3>
                <p class='mb-4'>The database has been completely reset. All tables and data have been removed.</p>
                
                <div class='flex space-x-4'>
                    <a href='setup.php' class='bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-semibold transition-colors'>
                        Run Setup Again
                    </a>
                    <a href='/' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold transition-colors'>
                        Go to Website
                    </a>
                </div>
            </div>";
        
    } catch (Exception $e) {
        echo "<div class='flex items-center space-x-2'>
                <div class='w-4 h-4 bg-red-500 rounded-full'></div>
                <span>✗ Error: " . htmlspecialchars($e->getMessage()) . "</span>
              </div>";
        
        echo "</div>
            </div>
            
            <div class='bg-red-900 border border-red-700 rounded-lg p-6'>
                <h3 class='text-xl font-semibold text-red-400 mb-4'>Reset Failed</h3>
                <p class='mb-4'>There was an error resetting the database:</p>
                <div class='bg-gray-800 p-4 rounded-lg'>
                    <code class='text-red-400'>" . htmlspecialchars($e->getMessage()) . "</code>
                </div>
                
                <div class='mt-6'>
                    <h4 class='font-semibold mb-2'>Troubleshooting:</h4>
                    <ul class='list-disc list-inside space-y-1 text-sm'>
                        <li>Make sure MySQL is running</li>
                        <li>Check database credentials in config/database.php</li>
                        <li>Ensure the database 'flix_salonce' exists</li>
                        <li>Verify user has DROP privileges</li>
                    </ul>
                </div>
                
                <div class='mt-4'>
                    <button onclick='window.location.reload()' class='bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg font-semibold transition-colors'>
                        Retry Reset
                    </button>
                </div>
            </div>";
    }
}

echo "
        <div class='mt-8 text-center text-gray-400 text-sm'>
            <p>Flix Salonce - PHP Version | Database Reset Script</p>
            <p class='mt-2 text-red-400'>⚠️ Use this script only for development/testing purposes</p>
        </div>
    </div>
</body>
</html>";
?>
