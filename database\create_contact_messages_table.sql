-- Create contact_messages table for storing contact form submissions
-- Flix Salonce - Contact Management System

CREATE TABLE IF NOT EXISTS contact_messages (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NULL,
    subject VARCHAR(255) NULL,
    message TEXT NOT NULL,
    status ENUM('NEW', 'READ', 'REPLIED', 'ARCHIVED') DEFAULT 'NEW',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_email (email),
    INDEX idx_subject (subject)
);

-- Add notification types for contact messages
INSERT IGNORE INTO notification_types (id, name, description, icon, color) VALUES
('contact-new', 'New Contact Message', 'New contact form submission received', 'fas fa-envelope', 'blue'),
('contact-replied', 'Contact Message Replied', 'Contact message has been replied to', 'fas fa-reply', 'green');
