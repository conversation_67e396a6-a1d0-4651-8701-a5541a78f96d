<?php
/**
 * Admin Create Customer
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = createCustomer($_POST);
    
    if ($result['success']) {
        $_SESSION['success'] = 'Customer created successfully!';
        redirect('/admin/customers/view.php?id=' . $result['id']);
    } else {
        $_SESSION['error'] = $result['error'];
    }
}

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Create New Customer";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>
            
            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-white">Create New Customer</h1>
                                <p class="mt-1 text-sm text-gray-300">Add a new customer to the system</p>
                            </div>
                            <div class="mt-4 sm:mt-0">
                                <a href="<?= getBasePath() ?>/admin/customers" 
                                   class="bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                    Back to Customers
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Customer Form -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6">
                        <form method="POST" id="customerForm">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Personal Information -->
                                <div class="lg:col-span-2">
                                    <h3 class="text-lg font-medium text-white mb-4">Personal Information</h3>
                                </div>

                                <!-- Full Name -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Full Name *</label>
                                    <input type="text" name="name" id="customerName" required 
                                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                           placeholder="Enter customer's full name">
                                </div>

                                <!-- Email -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Email Address *</label>
                                    <input type="email" name="email" id="customerEmail" required 
                                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                           placeholder="<EMAIL>">
                                </div>

                                <!-- Phone -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Phone Number</label>
                                    <input type="tel" name="phone" id="customerPhone" 
                                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                           placeholder="(*************">
                                </div>

                                <!-- Date of Birth -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Date of Birth</label>
                                    <input type="date" name="date_of_birth" id="customerDOB" 
                                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                </div>

                                <!-- Account Settings -->
                                <div class="lg:col-span-2">
                                    <h3 class="text-lg font-medium text-white mb-4 mt-6">Account Settings</h3>
                                </div>

                                <!-- Password -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Password *</label>
                                    <input type="password" name="password" id="customerPassword" required minlength="6"
                                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                           placeholder="Minimum 6 characters">
                                </div>

                                <!-- Confirm Password -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Confirm Password *</label>
                                    <input type="password" name="confirm_password" id="confirmPassword" required minlength="6"
                                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                           placeholder="Confirm password">
                                </div>

                                <!-- Initial Points -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Initial Loyalty Points</label>
                                    <input type="number" name="points" id="initialPoints" min="0" value="0"
                                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                           placeholder="0">
                                    <p class="text-xs text-gray-400 mt-1">Optional welcome bonus points</p>
                                </div>

                                <!-- Send Welcome Email -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Notifications</label>
                                    <div class="flex items-center">
                                        <input type="checkbox" name="send_welcome_email" id="sendWelcomeEmail" value="1" checked
                                               class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold">
                                        <span class="ml-2 text-sm text-gray-300">Send welcome email</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-300 mb-2">Notes</label>
                                <textarea name="notes" rows="3" 
                                          class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                          placeholder="Any additional notes about the customer..."></textarea>
                            </div>

                            <!-- Submit Button -->
                            <div class="mt-6 flex gap-4">
                                <button type="submit" 
                                        class="flex-1 bg-salon-gold text-black py-3 px-6 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    Create Customer
                                </button>
                                <a href="<?= getBasePath() ?>/admin/customers" 
                                   class="flex-1 bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-700 transition-colors text-center">
                                    Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('customerForm');
    const password = document.getElementById('customerPassword');
    const confirmPassword = document.getElementById('confirmPassword');

    // Password confirmation validation
    function validatePasswords() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }

    password.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);

    // Form validation
    form.addEventListener('submit', function(e) {
        const requiredFields = ['name', 'email', 'password', 'confirm_password'];
        let isValid = true;

        requiredFields.forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('border-red-500');
            } else {
                field.classList.remove('border-red-500');
            }
        });

        // Check password match
        if (password.value !== confirmPassword.value) {
            isValid = false;
            confirmPassword.classList.add('border-red-500');
            alert('Passwords do not match.');
        }

        // Check email format
        const emailField = document.querySelector('[name="email"]');
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailField.value)) {
            isValid = false;
            emailField.classList.add('border-red-500');
            alert('Please enter a valid email address.');
        }

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields correctly.');
        }
    });

    // Auto-generate password option
    const generatePasswordBtn = document.createElement('button');
    generatePasswordBtn.type = 'button';
    generatePasswordBtn.className = 'mt-2 text-sm text-blue-400 hover:text-blue-300';
    generatePasswordBtn.textContent = 'Generate secure password';
    generatePasswordBtn.addEventListener('click', function() {
        const generatedPassword = generateSecurePassword();
        password.value = generatedPassword;
        confirmPassword.value = generatedPassword;
        validatePasswords();
    });
    
    password.parentNode.appendChild(generatePasswordBtn);
});

function generateSecurePassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
