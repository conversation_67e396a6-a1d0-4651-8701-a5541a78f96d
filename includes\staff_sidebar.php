<?php
$currentPath = $_SERVER['REQUEST_URI'];
$basePath = getBasePath();

// Normalize current path for comparison
$normalizedCurrentPath = rtrim(parse_url($currentPath, PHP_URL_PATH), '/');
$staffBasePath = $basePath . '/staff';
$navigation = [
    [
        'name' => 'Dashboard',
        'href' => $basePath . '/staff',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7zm0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10M7 17h4" />'
    ],
    [
        'name' => 'Schedule',
        'href' => $basePath . '/staff/schedule',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />'
    ],
    [
        'name' => 'Appointments',
        'href' => $basePath . '/staff/appointments',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />'
    ],
    [
        'name' => 'Notifications',
        'href' => $basePath . '/staff/notifications',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />',
        'badge' => '<span id="sidebarNotificationCounter" class="hidden ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-500 text-white"></span>'
    ],
    [
        'name' => 'Earnings',
        'href' => $basePath . '/staff/earnings',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />'
    ]
];
?>

<nav class="space-y-1">
    <?php foreach ($navigation as $item):
        // Normalize item href for comparison
        $normalizedItemHref = rtrim($item['href'], '/');

        // Check if this menu item is active
        $isActive = false;

        // Exact match for all pages
        if ($normalizedCurrentPath === $normalizedItemHref) {
            $isActive = true;
        }
        // For non-dashboard items, check sub-pages
        elseif ($normalizedItemHref !== $staffBasePath && strpos($normalizedCurrentPath . '/', $normalizedItemHref . '/') === 0) {
            $isActive = true;
        }
    ?>
        <a href="<?= $item['href'] ?>"
           class="<?= $isActive
               ? 'bg-salon-gold text-black shadow-lg'
               : 'text-gray-300 hover:bg-secondary-800 hover:text-salon-gold border border-transparent hover:border-salon-gold/20'
           ?> group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-300 hover-lift">
            <svg class="<?= $isActive ? 'text-black' : 'text-gray-400 group-hover:text-salon-gold' ?> mr-3 flex-shrink-0 h-5 w-5 transition-colors duration-300"
                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <?= $item['icon'] ?>
            </svg>
            <?= $item['name'] ?>
            <?= $item['badge'] ?? '' ?>
        </a>
    <?php endforeach; ?>
</nav>

<script>
// Update sidebar notification counter
function updateSidebarNotificationCounter() {
    fetch('<?= getBasePath() ?>/api/staff/notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error:', data.error);
                return;
            }
            
            const counter = document.getElementById('sidebarNotificationCounter');
            const unreadCount = data.data.counts.unread;
            
            if (unreadCount > 0) {
                counter.textContent = unreadCount > 99 ? '99+' : unreadCount;
                counter.classList.remove('hidden');
            } else {
                counter.classList.add('hidden');
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

// Update counter every 30 seconds
setInterval(updateSidebarNotificationCounter, 30000);

// Initial update
document.addEventListener('DOMContentLoaded', updateSidebarNotificationCounter);
</script>


