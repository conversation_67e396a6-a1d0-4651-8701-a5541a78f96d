<?php
/**
 * Admin Booking Analytics Dashboard
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get date range for analytics
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$endDate = $_GET['end_date'] ?? date('Y-m-t'); // Last day of current month

// Get booking analytics data
$analytics = getBookingAnalytics($startDate, $endDate);

$pageTitle = "Booking Analytics";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>
            
            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-white">Booking Analytics</h1>
                                <p class="mt-1 text-sm text-gray-300">Detailed insights into booking performance</p>
                            </div>
                            <div class="mt-4 sm:mt-0">
                                <a href="<?= getBasePath() ?>/admin/bookings" 
                                   class="bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                    Back to Bookings
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Date Range Filter -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <form method="GET" class="flex flex-col sm:flex-row gap-4 items-end">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Start Date</label>
                                <input type="date" name="start_date" value="<?= htmlspecialchars($startDate) ?>" 
                                       class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">End Date</label>
                                <input type="date" name="end_date" value="<?= htmlspecialchars($endDate) ?>" 
                                       class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            </div>
                            <button type="submit" class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                Update Analytics
                            </button>
                        </form>
                    </div>

                    <!-- Key Metrics -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <div class="bg-secondary-800 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-300 truncate">Total Revenue</dt>
                                        <dd class="text-lg font-medium text-white"><?= formatCurrency($analytics['total_revenue']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="bg-secondary-800 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-300 truncate">Total Bookings</dt>
                                        <dd class="text-lg font-medium text-white"><?= number_format($analytics['total_bookings']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="bg-secondary-800 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-300 truncate">Avg. Booking Value</dt>
                                        <dd class="text-lg font-medium text-white"><?= formatCurrency($analytics['avg_booking_value']) ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="bg-secondary-800 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-300 truncate">Completion Rate</dt>
                                        <dd class="text-lg font-medium text-white"><?= number_format($analytics['completion_rate'], 1) ?>%</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <!-- Top Services -->
                        <div class="bg-secondary-800 shadow rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-white mb-4">Top Services</h3>
                            <div class="space-y-3">
                                <?php foreach ($analytics['top_services'] as $service): ?>
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <p class="text-sm font-medium text-white"><?= htmlspecialchars($service['name']) ?></p>
                                            <p class="text-xs text-gray-400"><?= $service['booking_count'] ?> bookings</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm font-medium text-salon-gold"><?= formatCurrency($service['revenue']) ?></p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Top Staff -->
                        <div class="bg-secondary-800 shadow rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-white mb-4">Top Performing Staff</h3>
                            <div class="space-y-3">
                                <?php foreach ($analytics['top_staff'] as $staff): ?>
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <p class="text-sm font-medium text-white"><?= htmlspecialchars($staff['name']) ?></p>
                                            <p class="text-xs text-gray-400"><?= $staff['booking_count'] ?> bookings</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm font-medium text-salon-gold"><?= formatCurrency($staff['revenue']) ?></p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Status Distribution -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Booking Status Distribution</h3>
                        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                            <?php foreach ($analytics['status_distribution'] as $status): ?>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-white"><?= $status['count'] ?></div>
                                    <div class="text-sm text-gray-300"><?= ucfirst(strtolower(str_replace('_', ' ', $status['status']))) ?></div>
                                    <div class="text-xs text-gray-400"><?= number_format($status['percentage'], 1) ?>%</div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Daily Booking Trend -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Daily Booking Trend</h3>
                        <div class="space-y-2">
                            <?php foreach ($analytics['daily_trend'] as $day): ?>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-300"><?= date('M j, Y', strtotime($day['date'])) ?></span>
                                    <div class="flex items-center gap-4">
                                        <span class="text-sm text-white"><?= $day['booking_count'] ?> bookings</span>
                                        <span class="text-sm text-salon-gold"><?= formatCurrency($day['revenue']) ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
