# DPO Pay Integration Guide

## Overview

This guide documents the complete replacement of Stripe with DPO Pay for the Flix Salon & SPA payment system. DPO Pay is a leading payment gateway in Africa, specifically designed for Tanzania and other African markets.

## Why DPO Pay?

- **Tanzania Support**: DPO Pay is fully available and supported in Tanzania
- **Local Payment Methods**: Supports mobile money, local bank transfers, and cards
- **TZS Currency**: Native support for Tanzanian Shilling (TZS)
- **Regulatory Compliance**: Complies with local financial regulations
- **Better Conversion**: Higher success rates for local customers

## Features Implemented

### ✅ Core Payment Processing
- **Token Creation**: Secure payment token generation
- **Payment Redirect**: Seamless redirect to DPO Pay gateway
- **Payment Verification**: Real-time payment status verification
- **Return Handling**: Proper handling of successful/failed payments

### ✅ Database Integration
- **Payment Records**: Full payment tracking and logging
- **Status Management**: Comprehensive payment status handling
- **Audit Trail**: Complete payment event logging
- **Migration Support**: Smooth transition from Stripe

### ✅ User Interface
- **Payment Selection**: Updated payment method selection
- **DPO Payment Page**: Dedicated DPO payment interface
- **Return Pages**: Success/failure handling pages
- **Status Indicators**: Real-time payment status updates

## File Structure

```
├── config/app.php                          # DPO configuration
├── includes/dpo_functions.php              # DPO API functions
├── customer/payments/dpo.php               # DPO payment page
├── customer/payments/dpo-return.php        # Payment return handler
├── api/payments/dpo/create-token.php       # Token creation API
├── api/payments/dpo/verify-payment.php     # Payment verification API
├── database/update_payments_dpo.sql        # Database migration
├── migrate_dpo.php                         # Migration script
└── test_dpo.php                           # Integration test
```

## Configuration

### 1. DPO Pay Account Setup

#### Step 1: Register with DPO Pay
1. Visit [DPO Pay Portal](https://portal.dpopay.com/)
2. Click "Register" and complete the business registration form
3. Provide required business documents:
   - Business license
   - Tax identification number
   - Bank account details
   - Director/owner identification

#### Step 2: Account Verification
1. Wait for DPO Pay to verify your business (usually 1-3 business days)
2. You'll receive an email confirmation when approved
3. Log into your DPO Pay portal

#### Step 3: Get API Credentials
1. In the DPO Pay portal, navigate to **API Settings** or **Integration**
2. Copy your **Company Token** (looks like: `9F416C11-127B-4DE2-AC7A-156B6A3687A6`)
3. Copy your **Service Type** (looks like: `3854` or similar number)
4. Note your **Service Description** for reference

#### Step 4: Configure Sandbox (for testing)
1. Ask DPO support for sandbox/test credentials if needed
2. Test credentials allow you to test without real money
3. Contact: <EMAIL> for sandbox access

### 2. Application Configuration

#### Update config/app.php
Replace the placeholder values with your actual DPO credentials:

```php
// DPO Pay Configuration
define('DPO_COMPANY_TOKEN', '9F416C11-127B-4DE2-AC7A-156B6A3687A6');  // Your actual company token
define('DPO_SERVICE_TYPE', '3854');  // Your actual service type
define('DPO_TEST_MODE', true);  // Set to false for production

// Payment Settings
define('DPO_ENABLED', true);
define('STRIPE_ENABLED', false);  // Disabled for Tanzania
```

#### Important Notes:
- **Never commit real credentials to version control**
- Use environment variables for production
- Keep test mode enabled until you're ready for live transactions

### 3. Database Migration
Run the migration to update your database:

```bash
# Option 1: Via browser
http://localhost/flix-php/migrate_dpo.php

# Option 2: Via MySQL command line
mysql -u root -p flix_salonce2 < database/update_payments_dpo.sql
```

## API Reference

### DPO Functions

#### `createDpoToken($paymentData)`
Creates a payment token with DPO Pay.

**Parameters:**
- `amount`: Payment amount in TZS
- `currency`: Currency code (TZS)
- `reference`: Unique payment reference
- `customer_first_name`: Customer first name
- `customer_last_name`: Customer last name
- `customer_email`: Customer email
- `customer_phone`: Customer phone number
- `redirect_url`: Success return URL
- `back_url`: Cancel return URL

**Returns:**
```php
[
    'success' => true,
    'transToken' => 'ABC123...',
    'transRef' => 'REF123...',
    'paymentUrl' => 'https://secure.3gdirectpay.com/payv2.php?ID=ABC123...'
]
```

#### `verifyDpoToken($transToken)`
Verifies payment status with DPO Pay.

**Parameters:**
- `transToken`: Transaction token from createDpoToken

**Returns:**
```php
[
    'success' => true,
    'verified' => true,
    'result' => '000',
    'resultExplanation' => 'Transaction Paid',
    'transactionAmount' => '1000',
    'transactionCurrency' => 'TZS'
]
```

### API Endpoints

#### `POST /api/payments/dpo/create-token.php`
Creates DPO payment token for a booking.

#### `POST /api/payments/dpo/verify-payment.php`
Verifies DPO payment status.

## Payment Flow

### 1. Customer Initiates Payment
1. Customer selects "Pay Now" on booking
2. System shows payment method selection
3. Customer chooses "DPO Pay"

### 2. Token Creation
1. System calls `createDpoToken()` with payment details
2. DPO Pay returns transaction token and payment URL
3. System stores token in database

### 3. Payment Processing
1. Customer redirected to DPO Pay gateway
2. Customer completes payment using preferred method
3. DPO Pay processes the payment

### 4. Return Handling
1. Customer redirected back to application
2. System verifies payment with `verifyDpoToken()`
3. Payment status updated in database
4. Customer sees success/failure message

## Testing

### 1. Run Integration Test
```bash
http://localhost/flix-php/test_dpo.php
```

### 2. Test Payment Flow
1. Create a test booking
2. Navigate to payments page
3. Select DPO Pay
4. Complete test payment
5. Verify status updates

### 3. DPO Test Credentials
Use DPO Pay sandbox credentials for testing:
- Test cards provided by DPO Pay
- Sandbox environment URLs
- Test mobile money numbers

## Security Considerations

### 1. Token Security
- Payment tokens are single-use
- Tokens expire automatically
- Secure token storage in database

### 2. Data Protection
- Customer data encrypted in transit
- PCI DSS compliance through DPO Pay
- Minimal sensitive data storage

### 3. Verification
- All payments verified server-side
- Amount validation on return
- Status cross-checking with DPO Pay

## Troubleshooting

### Common Issues

#### 1. "DPO Pay is disabled"
**Symptoms:** Error message when trying to use DPO Pay
**Solutions:**
- Check `DPO_ENABLED` is set to `true` in config/app.php
- Verify database migration completed successfully
- Run `http://localhost/flix-php/migrate_dpo.php` to ensure setup

#### 2. "HTML Response Instead of XML"
**Symptoms:** SimpleXMLElement parsing errors, HTML in API response
**Solutions:**
- Verify `DPO_COMPANY_TOKEN` is correct (not placeholder value)
- Check `DPO_SERVICE_TYPE` is correct
- Ensure API URL is correct for your region
- Contact DPO support to verify account status
- Run `http://localhost/flix-php/debug_dpo_api.php` to diagnose

#### 3. "Invalid company token"
**Symptoms:** Error code from DPO API
**Solutions:**
- Double-check company token from DPO portal
- Ensure no extra spaces or characters
- Verify you're using the correct environment (test vs live)
- Check if token has expired or been regenerated

#### 4. "Token creation failed"
**Symptoms:** DPO API returns error codes
**Solutions:**
- Check internet connectivity
- Verify DPO Pay service status
- Review error logs in browser console
- Ensure all required fields are provided
- Check service type is valid for your account

#### 5. "Payment verification failed"
**Symptoms:** Cannot verify completed payments
**Solutions:**
- Check transaction token validity
- Verify payment was actually completed on DPO side
- Review DPO Pay transaction logs in portal
- Ensure verification is called with correct token

#### 6. "Credentials Not Configured"
**Symptoms:** Placeholder values still in config
**Solutions:**
- Replace `YOUR_DPO_COMPANY_TOKEN_HERE` with actual token
- Replace `YOUR_DPO_SERVICE_TYPE_HERE` with actual service type
- Get credentials from DPO Pay portal
- Contact DPO support if you don't have credentials

### Debug Mode
Enable debug logging by adding to config/app.php:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Production Deployment

### 1. Update Configuration
```php
define('DPO_TEST_MODE', false);
define('DPO_COMPANY_TOKEN', 'your_live_company_token');
define('DPO_SERVICE_TYPE', 'your_live_service_type');
```

### 2. Security Checklist
- [ ] Disable debug mode
- [ ] Use HTTPS for all payment pages
- [ ] Configure proper error handling
- [ ] Set up monitoring and alerts
- [ ] Test with small amounts first

### 3. Go Live
1. Switch to live DPO Pay credentials
2. Test with real small transactions
3. Monitor payment success rates
4. Set up customer support for payment issues

## Support

### DPO Pay Support
- Portal: https://portal.dpopay.com/
- Documentation: https://docs.dpopay.com/
- Email: <EMAIL>
- Phone: +*********** 847

### Integration Support
For issues with this integration:
1. Check the troubleshooting section
2. Review error logs
3. Test with the provided test script
4. Verify configuration settings

## Migration from Stripe

The system maintains backward compatibility:
- Existing Stripe payments remain functional
- New payments default to DPO Pay
- Payment history preserved
- Gradual migration supported

This ensures a smooth transition without disrupting existing customers or payment records.
