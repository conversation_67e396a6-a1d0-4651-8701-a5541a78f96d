<?php
/**
 * Reset Password Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/forgot_password_functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user['role'] === 'ADMIN') {
        redirect('/admin');
    } elseif ($user['role'] === 'STAFF') {
        redirect('/staff');
    } else {
        redirect('/customer');
    }
}

// Check if user has valid session for password reset
if (!isset($_SESSION['reset_user_id']) || !isset($_SESSION['reset_step']) || $_SESSION['reset_step'] !== 'reset_password') {
    redirect('/auth/forgot-password.php');
}

$error = '';
$success = '';
$userId = $_SESSION['reset_user_id'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // CSRF Protection
    if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
        $error = 'Invalid request. Please try again.';
    } else {
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';

        if (empty($password) || empty($confirmPassword)) {
            $error = 'Please fill in all fields';
        } elseif ($password !== $confirmPassword) {
            $error = 'Passwords do not match';
        } elseif (strlen($password) < 8) {
            $error = 'Password must be at least 8 characters long';
        } else {
            // Reset password
            $result = resetUserPassword($userId, $password);
            
            if ($result['success']) {
                // Clear session data
                unset($_SESSION['reset_email'], $_SESSION['reset_step'], $_SESSION['reset_user_id']);
                
                // Set success message and redirect to login
                $_SESSION['password_reset_success'] = 'Password reset successfully! You can now login with your new password.';
                redirect('/auth/login.php');
            } else {
                $error = $result['error'];
            }
        }
    }
}

$pageTitle = "Reset Password";
$pageDescription = "Create a new password for your account";

// Include header
include __DIR__ . '/../includes/header.php';
?>
<!-- Enhanced Auth Page Styles - Unified Design -->
<style>
    /* Auth page background with gradient overlay */
    .auth-main {
        min-height: calc(100vh - 140px);
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #141414 100%);
        background-attachment: fixed;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
    }

    .auth-main::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 70% 80%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
        pointer-events: none;
    }

    /* Glass container with enhanced effects */
    .auth-container {
        background: rgba(10, 10, 10, 0.85);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(245, 158, 11, 0.2);
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.8),
            0 0 0 1px rgba(245, 158, 11, 0.1),
            inset 0 1px 0 rgba(245, 158, 11, 0.1);
        position: relative;
        overflow: hidden;
    }

    .auth-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.5), transparent);
    }

    /* Enhanced input styling */
    .input-group {
        position: relative;
    }

    .input-group input {
        background: rgba(20, 20, 20, 0.8) !important;
        border: 1px solid rgba(75, 85, 99, 0.5) !important;
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .input-group input:focus {
        background: rgba(20, 20, 20, 0.95) !important;
        border-color: rgba(245, 158, 11, 0.8) !important;
        box-shadow:
            0 0 0 3px rgba(245, 158, 11, 0.1),
            0 4px 12px rgba(245, 158, 11, 0.15);
        transform: translateY(-1px);
    }

    .input-group input:focus + .input-border {
        transform: scaleX(1);
    }

    .input-border {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #f59e0b, #fbbf24, #f59e0b);
        transform: scaleX(0);
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 1px;
    }

    /* Enhanced button styling */
    .btn-primary {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #f59e0b 100%);
        background-size: 200% 200%;
        border: 1px solid rgba(245, 158, 11, 0.3);
        box-shadow:
            0 4px 15px rgba(245, 158, 11, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .btn-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-primary:hover:not(:disabled) {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #fbbf24 100%);
        background-position: 100% 0;
        transform: translateY(-2px);
        box-shadow:
            0 8px 25px rgba(245, 158, 11, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border-color: rgba(245, 158, 11, 0.6);
    }

    .btn-primary:hover:not(:disabled)::before {
        left: 100%;
    }

    .btn-primary:disabled {
        background: rgba(107, 114, 128, 0.5);
        border-color: rgba(107, 114, 128, 0.3);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
        backdrop-filter: blur(10px);
    }

    .btn-primary:disabled::before {
        display: none;
    }

    .btn-primary:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    }

    /* Enhanced password requirements styling */
    .password-requirements {
        background: rgba(10, 10, 10, 0.9);
        border: 1px solid rgba(245, 158, 11, 0.2);
        backdrop-filter: blur(15px);
        box-shadow:
            0 8px 25px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(245, 158, 11, 0.1);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .password-requirements.hidden {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
        pointer-events: none;
    }

    .password-requirements:not(.hidden) {
        opacity: 1;
        transform: translateY(0) scale(1);
    }

    .requirement-item {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        padding: 0.25rem;
        border-radius: 0.5rem;
    }

    .requirement-met {
        color: #10b981;
        background: rgba(16, 185, 129, 0.1);
        transform: scale(1.02);
    }

    .requirement-unmet {
        color: #ef4444;
        background: rgba(239, 68, 68, 0.05);
    }

    /* Enhanced strength bar */
    .strength-bar {
        height: 6px;
        background: rgba(55, 65, 81, 0.8);
        border-radius: 3px;
        overflow: hidden;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(75, 85, 99, 0.3);
    }

    #password-strength-container {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    #password-strength-container.hidden {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
        pointer-events: none;
    }

    #password-strength-container:not(.hidden) {
        opacity: 1;
        transform: translateY(0) scale(1);
    }

    .strength-fill {
        height: 100%;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 2px;
    }

    .strength-weak {
        background: linear-gradient(90deg, #ef4444, #dc2626);
        width: 20%;
        box-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
    }
    .strength-fair {
        background: linear-gradient(90deg, #f59e0b, #d97706);
        width: 40%;
        box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
    }
    .strength-good {
        background: linear-gradient(90deg, #eab308, #ca8a04);
        width: 60%;
        box-shadow: 0 0 10px rgba(234, 179, 8, 0.3);
    }
    .strength-strong {
        background: linear-gradient(90deg, #22c55e, #16a34a);
        width: 80%;
        box-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
    }
    .strength-excellent {
        background: linear-gradient(90deg, #10b981, #059669);
        width: 100%;
        box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
    }

    /* Enhanced animations */
    .error-message {
        animation: slideInEnhanced 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes slideInEnhanced {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* Link enhancements */
    a {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    a:hover {
        text-shadow: 0 0 8px rgba(245, 158, 11, 0.5);
    }

    /* Icon container enhancement */
    .auth-container .mx-auto.h-16.w-16 {
        background: rgba(245, 158, 11, 0.15);
        border: 1px solid rgba(245, 158, 11, 0.3);
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.2);
    }
</style>

<!-- Auth Page Content -->
<div class="auth-main">
    <div class="max-w-md w-full space-y-8">
        <div class="auth-container auth-card rounded-2xl p-8 sm:p-10">
            <div class="text-center mb-8">
                <div class="mx-auto h-16 w-16 bg-salon-gold/20 rounded-full flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m0 0a2 2 0 01-2 2m2-2H9m12 0V9a2 2 0 00-2-2M3 11a2 2 0 012-2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m0 0a2 2 0 01-2 2m2-2H9m12 0V9a2 2 0 00-2-2" />
                    </svg>
                </div>
                <h2 class="text-2xl sm:text-3xl font-bold text-white mb-2">Create New Password</h2>
                <p class="text-gray-300 text-sm sm:text-base">
                    Choose a strong password to secure your account
                </p>
            </div>

            <form id="resetForm" class="space-y-6" method="POST" autocomplete="off">
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">

                <?php if ($error): ?>
                    <div class="error-message bg-red-500/10 border border-red-500/30 rounded-xl p-4 backdrop-blur-sm">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-400 mb-1">Error</h3>
                                <p class="text-sm text-red-300"><?= htmlspecialchars($error) ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="space-y-6">
                    <div class="input-group">
                        <label for="password" class="block text-sm font-semibold text-gray-200 mb-2">
                            New Password
                        </label>
                        <div class="relative">
                            <input id="password" name="password" type="password" autocomplete="off" required
                                   class="appearance-none relative block w-full px-4 py-3 pr-12 border border-gray-600 placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm"
                                   placeholder="Create a secure password">
                            <button type="button" class="absolute inset-y-0 right-0 pr-4 flex items-center hover:text-salon-gold transition-colors" onclick="togglePassword('password')">
                                <svg id="eye-icon-password" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </button>
                            <div class="input-border"></div>
                        </div>

                        <!-- Password Strength Indicator -->
                        <div id="password-strength-container" class="mt-3 hidden">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs font-medium text-gray-300">Password Strength:</span>
                                <span id="strength-text" class="text-xs font-medium text-gray-400">Weak</span>
                            </div>
                            <div class="strength-bar">
                                <div id="strength-fill" class="strength-fill"></div>
                            </div>
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="confirm_password" class="block text-sm font-semibold text-gray-200 mb-2">
                            Confirm New Password
                        </label>
                        <div class="relative">
                            <input id="confirm_password" name="confirm_password" type="password" autocomplete="off" required
                                   class="appearance-none relative block w-full px-4 py-3 pr-12 border border-gray-600 placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm"
                                   placeholder="Confirm your password">
                            <button type="button" class="absolute inset-y-0 right-0 pr-4 flex items-center hover:text-salon-gold transition-colors" onclick="togglePassword('confirm_password')">
                                <svg id="eye-icon-confirm_password" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </button>
                            <div class="input-border"></div>
                        </div>

                        <!-- Password Match Indicator -->
                        <div id="password-match" class="mt-2 hidden">
                            <div class="flex items-center text-sm">
                                <svg id="match-icon" class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span id="match-text">Passwords match</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Password Requirements Panel -->
                <div id="password-requirements" class="password-requirements rounded-xl p-4 mt-6 hidden">
                    <h4 class="text-sm font-semibold text-gray-200 mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-salon-gold" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 011-1 1 1 0 110 2 1 1 0 01-1-1zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                        Password Requirements
                    </h4>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <div id="req-length" class="requirement-item requirement-unmet flex items-center text-sm">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span>8+ characters</span>
                        </div>
                        <div id="req-lowercase" class="requirement-item requirement-unmet flex items-center text-sm">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span>Lowercase letter</span>
                        </div>
                        <div id="req-uppercase" class="requirement-item requirement-unmet flex items-center text-sm">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span>Uppercase letter</span>
                        </div>
                        <div id="req-number" class="requirement-item requirement-unmet flex items-center text-sm">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span>Number</span>
                        </div>
                    </div>
                </div>

                <div class="pt-2">
                    <button type="submit" id="submit-btn"
                            class="btn-primary group relative w-full flex justify-center py-3 px-6 border border-transparent text-sm font-semibold rounded-xl text-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-4">
                            <svg class="h-5 w-5 text-black/80 group-hover:text-black transition-colors" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                        Reset Password
                    </button>
                </div>
            </form>

        </div>
    </div>
</div>

<script>
// Password visibility toggle
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const eyeIcon = document.getElementById('eye-icon-' + fieldId);

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeIcon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
        `;
    } else {
        passwordInput.type = 'password';
        eyeIcon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        `;
    }
}

// Password strength checking
function checkPasswordStrength(password) {
    let score = 0;
    let feedback = 'Weak';
    
    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    
    // Character variety checks
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    // Determine strength
    if (score >= 6) {
        feedback = 'Excellent';
    } else if (score >= 5) {
        feedback = 'Strong';
    } else if (score >= 4) {
        feedback = 'Good';
    } else if (score >= 3) {
        feedback = 'Fair';
    }
    
    return { score, feedback };
}

// Update password requirements
function updatePasswordRequirements(password) {
    const requirements = {
        'req-length': password.length >= 8,
        'req-lowercase': /[a-z]/.test(password),
        'req-uppercase': /[A-Z]/.test(password),
        'req-number': /[0-9]/.test(password)
    };
    
    Object.entries(requirements).forEach(([id, met]) => {
        const element = document.getElementById(id);
        if (met) {
            element.classList.remove('requirement-unmet');
            element.classList.add('requirement-met');
        } else {
            element.classList.remove('requirement-met');
            element.classList.add('requirement-unmet');
        }
    });
    
    return Object.values(requirements).every(met => met);
}

// Check password match
function checkPasswordMatch() {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const matchElement = document.getElementById('password-match');
    const matchIcon = document.getElementById('match-icon');
    const matchText = document.getElementById('match-text');
    
    if (confirmPassword.length > 0) {
        matchElement.classList.remove('hidden');
        
        if (password === confirmPassword) {
            matchElement.className = 'mt-2 text-green-400';
            matchText.textContent = 'Passwords match';
            matchIcon.innerHTML = `<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />`;
            return true;
        } else {
            matchElement.className = 'mt-2 text-red-400';
            matchText.textContent = 'Passwords do not match';
            matchIcon.innerHTML = `<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />`;
            return false;
        }
    } else {
        matchElement.classList.add('hidden');
        return false;
    }
}

// Update submit button state
function updateSubmitButton() {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const submitBtn = document.getElementById('submit-btn');
    
    const requirementsMet = updatePasswordRequirements(password);
    const passwordsMatch = password === confirmPassword && password.length > 0;
    
    if (requirementsMet && passwordsMatch) {
        submitBtn.disabled = false;
        submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    } else {
        submitBtn.disabled = true;
        submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
    }
}

// Event listeners
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strengthContainer = document.getElementById('password-strength-container');
    const requirementsPanel = document.getElementById('password-requirements');
    
    if (password.length > 0) {
        strengthContainer.classList.remove('hidden');
        requirementsPanel.classList.remove('hidden');
        
        // Update strength indicator
        const strength = checkPasswordStrength(password);
        const strengthFill = document.getElementById('strength-fill');
        const strengthText = document.getElementById('strength-text');
        
        strengthFill.className = `strength-fill strength-${strength.feedback.toLowerCase()}`;
        strengthText.textContent = strength.feedback;
        strengthText.className = `text-xs font-medium ${
            strength.feedback === 'Excellent' ? 'text-green-400' :
            strength.feedback === 'Strong' ? 'text-green-400' :
            strength.feedback === 'Good' ? 'text-yellow-400' :
            strength.feedback === 'Fair' ? 'text-orange-400' : 'text-red-400'
        }`;
    } else {
        strengthContainer.classList.add('hidden');
        requirementsPanel.classList.add('hidden');
    }
    
    updateSubmitButton();
});

document.getElementById('password').addEventListener('focus', function() {
    if (this.value.length > 0) {
        document.getElementById('password-requirements').classList.remove('hidden');
    }
});

document.getElementById('password').addEventListener('blur', function() {
    if (this.value.length === 0) {
        document.getElementById('password-requirements').classList.add('hidden');
    }
});

document.getElementById('confirm_password').addEventListener('input', function() {
    checkPasswordMatch();
    updateSubmitButton();
});

// Initialize
updateSubmitButton();
</script>

<?php
// Include footer
include __DIR__ . '/../includes/footer.php';
?>
