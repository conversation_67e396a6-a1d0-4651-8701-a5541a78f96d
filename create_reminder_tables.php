<?php
/**
 * Create Reminder Tables Script
 * Run this script to create the booking reminder tables
 */

require_once 'config/app.php';

echo "<h1>Creating Booking Reminder Tables</h1>";

try {
    // Drop existing tables if they exist (for clean setup)
    echo "Dropping existing tables if they exist...<br>";
    $database->query("DROP TABLE IF EXISTS reminder_logs");
    $database->query("DROP TABLE IF EXISTS booking_reminders");
    echo "✅ Existing tables dropped<br>";
    
    // Create booking_reminders table
    echo "Creating booking_reminders table...<br>";
    $database->query("
        CREATE TABLE booking_reminders (
            id VARCHAR(36) PRIMARY KEY,
            booking_id VARCHAR(36) NOT NULL,
            reminder_type ENUM('24_HOURS', '1_DAY', '30_MINUTES', 'AT_TIME') NOT NULL,
            priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') NOT NULL DEFAULT 'MEDIUM',
            status ENUM('PENDING', 'SENT', 'FAILED', 'SKIPPED') NOT NULL DEFAULT 'PENDING',
            scheduled_time DATETIME NOT NULL,
            sent_time DATETIME NULL,
            attempts INT DEFAULT 0,
            max_attempts INT DEFAULT 3,
            customer_email_sent BOOLEAN DEFAULT FALSE,
            staff_email_sent BOOLEAN DEFAULT FALSE,
            customer_email_status ENUM('PENDING', 'SENT', 'FAILED') DEFAULT 'PENDING',
            staff_email_status ENUM('PENDING', 'SENT', 'FAILED') DEFAULT 'PENDING',
            error_message TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_booking_id (booking_id),
            INDEX idx_reminder_type (reminder_type),
            INDEX idx_status (status),
            INDEX idx_priority (priority),
            INDEX idx_scheduled_time (scheduled_time),
            INDEX idx_pending_reminders (status, scheduled_time),
            INDEX idx_booking_reminder_type (booking_id, reminder_type),
            
            UNIQUE KEY unique_booking_reminder (booking_id, reminder_type)
        )
    ");
    echo "✅ booking_reminders table created<br>";
    
    // Create reminder_logs table
    echo "Creating reminder_logs table...<br>";
    $database->query("
        CREATE TABLE reminder_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            reminder_id VARCHAR(36) NOT NULL,
            booking_id VARCHAR(36) NOT NULL,
            action ENUM('CREATED', 'SENT', 'FAILED', 'RETRY', 'SKIPPED') NOT NULL,
            recipient_type ENUM('CUSTOMER', 'STAFF', 'BOTH') NOT NULL,
            recipient_email VARCHAR(255),
            details TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            INDEX idx_reminder_id (reminder_id),
            INDEX idx_booking_id (booking_id),
            INDEX idx_action (action),
            INDEX idx_created_at (created_at)
        )
    ");
    echo "✅ reminder_logs table created<br>";
    
    // Verify tables were created
    echo "<h2>Verifying Table Structure</h2>";
    
    $reminderColumns = $database->fetchAll("DESCRIBE booking_reminders");
    echo "<h3>booking_reminders table (" . count($reminderColumns) . " columns):</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($reminderColumns as $col) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($col['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    $logColumns = $database->fetchAll("DESCRIBE reminder_logs");
    echo "<h3>reminder_logs table (" . count($logColumns) . " columns):</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($logColumns as $col) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($col['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>✅ Tables Created Successfully!</h2>";
    echo "<p>The booking reminder system is now ready to use.</p>";
    
    // Test basic functionality
    echo "<h2>Testing Basic Functionality</h2>";
    
    // Test inserting a sample reminder
    $testReminderId = generateUUID();
    $testBookingId = 'test-booking-' . time();
    
    $database->query(
        "INSERT INTO booking_reminders 
         (id, booking_id, reminder_type, priority, scheduled_time, max_attempts)
         VALUES (?, ?, ?, ?, ?, ?)",
        [
            $testReminderId,
            $testBookingId,
            '30_MINUTES',
            'HIGH',
            date('Y-m-d H:i:s', time() + 3600), // 1 hour from now
            3
        ]
    );
    
    echo "✅ Test reminder inserted<br>";
    
    // Test querying
    $testReminder = $database->fetch(
        "SELECT * FROM booking_reminders WHERE id = ?",
        [$testReminderId]
    );
    
    if ($testReminder) {
        echo "✅ Test reminder retrieved successfully<br>";
        echo "ID: " . htmlspecialchars($testReminder['id']) . "<br>";
        echo "Type: " . htmlspecialchars($testReminder['reminder_type']) . "<br>";
        echo "Priority: " . htmlspecialchars($testReminder['priority']) . "<br>";
        echo "Status: " . htmlspecialchars($testReminder['status']) . "<br>";
    }
    
    // Clean up test data
    $database->query("DELETE FROM booking_reminders WHERE id = ?", [$testReminderId]);
    echo "✅ Test data cleaned up<br>";
    
    echo "<h2>Next Steps</h2>";
    echo "<ol>";
    echo "<li>Run the reminder system test: <a href='test_reminder_system.php'>test_reminder_system.php</a></li>";
    echo "<li>Check the admin interface: <a href='admin/booking-reminders.php'>admin/booking-reminders.php</a></li>";
    echo "<li>Test with real bookings by confirming a booking in the admin panel</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error Creating Tables</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database connection and permissions.</p>";
}
?>
