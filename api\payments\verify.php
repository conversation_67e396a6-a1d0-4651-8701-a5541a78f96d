<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../config/app.php';

// Check authentication and customer role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $paymentId = $input['payment_id'] ?? '';
    
    if (empty($paymentId)) {
        throw new Exception('Payment ID is required');
    }
    
    global $database;
    
    // Get payment details and verify ownership
    $payment = $database->fetch("
        SELECT p.*, b.user_id 
        FROM payments p
        INNER JOIN bookings b ON p.booking_id = b.id
        WHERE p.id = ? AND b.user_id = ?
    ", [$paymentId, $_SESSION['user_id']]);
    
    if (!$payment) {
        throw new Exception('Payment not found or access denied');
    }
    
    // Check if payment is already completed
    if ($payment['status'] === 'COMPLETED') {
        echo json_encode([
            'success' => true,
            'status' => 'completed',
            'message' => 'Payment already completed'
        ]);
        exit;
    }
    
    // Verify payment with gateway
    $result = verifyPayment($paymentId, $payment['payment_gateway']);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'status' => $result['status'],
            'message' => 'Payment verified successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => $result['error']
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
