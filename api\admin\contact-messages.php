<?php
/**
 * Admin Contact Messages API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/contact_functions.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Require admin authentication
if (!isLoggedIn() || !hasRole('ADMIN')) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    if ($method === 'GET') {
        $action = $_GET['action'] ?? 'list';
        
        switch ($action) {
            case 'get':
                $id = $_GET['id'] ?? '';
                if (empty($id)) {
                    throw new Exception('Message ID is required');
                }
                
                $message = getContactMessage($id);
                if (!$message) {
                    throw new Exception('Message not found');
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => $message
                ]);
                break;
                
            case 'list':
                $filters = [
                    'status' => $_GET['status'] ?? '',
                    'search' => $_GET['search'] ?? '',
                    'date_from' => $_GET['date_from'] ?? '',
                    'date_to' => $_GET['date_to'] ?? '',
                    'subject' => $_GET['subject'] ?? ''
                ];
                
                $page = (int)($_GET['page'] ?? 1);
                $limit = (int)($_GET['limit'] ?? 20);
                
                $result = getContactMessages($filters, $page, $limit);
                
                echo json_encode([
                    'success' => true,
                    'data' => $result
                ]);
                break;
                
            case 'stats':
                $stats = getContactMessageStats();
                
                echo json_encode([
                    'success' => true,
                    'stats' => $stats
                ]);
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } elseif ($method === 'POST') {
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'update_status':
                $id = $input['id'] ?? '';
                $status = $input['status'] ?? '';
                
                if (empty($id) || empty($status)) {
                    throw new Exception('Message ID and status are required');
                }
                
                $result = updateContactMessageStatus($id, $status);
                
                if ($result) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Message status updated successfully'
                    ]);
                } else {
                    throw new Exception('Failed to update message status');
                }
                break;
                
            case 'delete':
                $id = $input['id'] ?? '';
                
                if (empty($id)) {
                    throw new Exception('Message ID is required');
                }
                
                $result = deleteContactMessage($id);
                
                if ($result) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Message deleted successfully'
                    ]);
                } else {
                    throw new Exception('Failed to delete message');
                }
                break;
                
            case 'reply':
                $id = $input['id'] ?? '';
                $replyMessage = $input['reply_message'] ?? '';
                
                if (empty($id) || empty($replyMessage)) {
                    throw new Exception('Message ID and reply message are required');
                }
                
                // Get the original message
                $originalMessage = getContactMessage($id);
                if (!$originalMessage) {
                    throw new Exception('Original message not found');
                }
                
                // Here you would typically send an email to the customer
                // For now, we'll just mark the message as replied
                $result = markContactMessageReplied($id, $replyMessage);
                
                if ($result) {
                    // In a real implementation, you would send an email here
                    // Example:
                    // sendReplyEmail($originalMessage['email'], $originalMessage['name'], $replyMessage);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'Reply sent successfully'
                    ]);
                } else {
                    throw new Exception('Failed to send reply');
                }
                break;
                
            case 'bulk_update':
                $ids = $input['ids'] ?? [];
                $bulkAction = $input['bulk_action'] ?? '';
                
                if (empty($ids) || empty($bulkAction)) {
                    throw new Exception('Message IDs and bulk action are required');
                }
                
                $result = bulkUpdateContactMessages($ids, $bulkAction);
                
                if ($result) {
                    $actionText = [
                        'mark_read' => 'marked as read',
                        'archive' => 'archived',
                        'delete' => 'deleted'
                    ];
                    
                    echo json_encode([
                        'success' => true,
                        'message' => count($ids) . ' message(s) ' . ($actionText[$bulkAction] ?? 'updated') . ' successfully'
                    ]);
                } else {
                    throw new Exception('Failed to perform bulk action');
                }
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Send reply email to customer (placeholder function)
 * In a real implementation, this would use your email service
 */
function sendReplyEmail($customerEmail, $customerName, $replyMessage) {
    // This is a placeholder function
    // In a real implementation, you would use PHPMailer, SendGrid, or another email service
    
    $subject = "Re: Your message to Flix Salon & SPA";
    $body = "Dear " . $customerName . ",\n\n";
    $body .= "Thank you for contacting Flix Salon & SPA. Here's our response to your inquiry:\n\n";
    $body .= $replyMessage . "\n\n";
    $body .= "Best regards,\n";
    $body .= "Flix Salon & SPA Team\n";
    $body .= "Phone: (*************\n";
    $body .= "Email: <EMAIL>";
    
    // Use your preferred email service here
    // mail($customerEmail, $subject, $body);
    
    return true;
}
?>
