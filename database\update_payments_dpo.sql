-- Update payments table to support DPO Pay
-- Run this migration to add DPO Pay support to the existing payment system

USE flix_salonce2;

-- Add DPO Pay support to payment_gateway enum
ALTER TABLE payments 
MODIFY COLUMN payment_gateway ENUM('DPO', 'STRIPE', 'FLUTTERWAVE') DEFAULT 'DPO';

-- Add DPO-specific columns
ALTER TABLE payments 
ADD COLUMN IF NOT EXISTS dpo_token VARCHAR(255) NULL AFTER stripe_payment_id;

-- Update payment_logs table to support DPO
ALTER TABLE payment_logs 
MODIFY COLUMN gateway ENUM('DPO', 'STRIPE', 'FLUTTERWAVE') NOT NULL;

-- Update payment_webhooks table to support DPO
ALTER TABLE payment_webhooks 
MODIFY COLUMN gateway ENUM('DPO', 'STRIPE', 'FLUTTERWAVE') NOT NULL;

-- Add indexes for DPO columns
CREATE INDEX IF NOT EXISTS idx_payments_dpo_token ON payments(dpo_token);

-- Update existing records to use DPO as default gateway for Tanzania
UPDATE payments 
SET payment_gateway = 'DPO' 
WHERE payment_gateway = 'STRIPE' AND currency = 'TZS';

-- Ensure all payments use TZS currency for Tanzania
UPDATE payments 
SET currency = 'TZS' 
WHERE currency IN ('USD', 'NGN') OR currency IS NULL;

-- Create a view for DPO payments for easier querying
CREATE OR REPLACE VIEW dpo_payments AS
SELECT 
    p.*,
    b.date as booking_date,
    b.start_time as booking_time,
    u.name as customer_name,
    u.email as customer_email,
    u.phone as customer_phone
FROM payments p
INNER JOIN bookings b ON p.booking_id = b.id
INNER JOIN users u ON p.user_id = u.id
WHERE p.payment_gateway = 'DPO';

-- Insert sample DPO configuration (optional - for testing)
-- You can remove this section if not needed
INSERT IGNORE INTO settings (key_name, key_value, description) VALUES
('dpo_company_token', 'YOUR_DPO_COMPANY_TOKEN', 'DPO Pay Company Token'),
('dpo_service_type', 'YOUR_DPO_SERVICE_TYPE', 'DPO Pay Service Type'),
('dpo_test_mode', '1', 'DPO Pay Test Mode (1 for test, 0 for live)'),
('dpo_enabled', '1', 'Enable DPO Pay payments (1 for enabled, 0 for disabled)');

-- Create settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    key_name VARCHAR(255) UNIQUE NOT NULL,
    key_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

COMMIT;
